// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Noise System Core Implementation
// Bridge 2.9: PCG Framework - Noise e Randomization

#include "AuracronPCGNoiseSystem.h"
#include "AuracronPCGBridge.h"

// Engine includes
#include "Math/UnrealMathUtility.h"

namespace AuracronPCGNoiseSystemUtils
{
    // =============================================================================
    // HASH FUNCTIONS
    // =============================================================================

    uint32 Hash1D(uint32 x, int32 seed)
    {
        x += seed;
        x = ((x >> 16) ^ x) * 0x45d9f3b;
        x = ((x >> 16) ^ x) * 0x45d9f3b;
        x = (x >> 16) ^ x;
        return x;
    }

    uint32 Hash2D(uint32 x, uint32 y, int32 seed)
    {
        return Hash1D(x + Hash1D(y, seed), seed);
    }

    uint32 Hash3D(uint32 x, uint32 y, uint32 z, int32 seed)
    {
        return Hash1D(x + Hash2D(y, z, seed), seed);
    }

    uint32 Hash4D(uint32 x, uint32 y, uint32 z, uint32 w, int32 seed)
    {
        return Hash1D(x + Hash3D(y, z, w, seed), seed);
    }

    // =============================================================================
    // INTERPOLATION FUNCTIONS
    // =============================================================================

    float InterpolateLinear(float a, float b, float t)
    {
        return FMath::Lerp(a, b, t);
    }

    float InterpolateHermite(float a, float b, float t)
    {
        float t2 = t * t;
        float t3 = t2 * t;
        return a * (2.0f * t3 - 3.0f * t2 + 1.0f) + b * (3.0f * t2 - 2.0f * t3);
    }

    float InterpolateQuintic(float a, float b, float t)
    {
        float t3 = t * t * t;
        float t4 = t3 * t;
        float t5 = t4 * t;
        float quintic = 6.0f * t5 - 15.0f * t4 + 10.0f * t3;
        return FMath::Lerp(a, b, quintic);
    }

    float InterpolateCosine(float a, float b, float t)
    {
        float cosine_t = (1.0f - FMath::Cos(t * PI)) * 0.5f;
        return FMath::Lerp(a, b, cosine_t);
    }

    // =============================================================================
    // PERLIN NOISE IMPLEMENTATION
    // =============================================================================

    float PerlinNoise1D(float x, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        float xf = x - xi;

        uint32 h0 = Hash1D(xi, seed);
        uint32 h1 = Hash1D(xi + 1, seed);

        float g0 = (h0 & 0xFFFF) / 32768.0f - 1.0f;
        float g1 = (h1 & 0xFFFF) / 32768.0f - 1.0f;

        float n0 = g0 * xf;
        float n1 = g1 * (xf - 1.0f);

        return InterpolateQuintic(n0, n1, xf);
    }

    float PerlinNoise2D(float x, float y, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        float xf = x - xi;
        float yf = y - yi;

        uint32 h00 = Hash2D(xi, yi, seed);
        uint32 h10 = Hash2D(xi + 1, yi, seed);
        uint32 h01 = Hash2D(xi, yi + 1, seed);
        uint32 h11 = Hash2D(xi + 1, yi + 1, seed);

        // Generate gradient vectors
        float g00x = ((h00 & 0xFF) / 128.0f - 1.0f);
        float g00y = (((h00 >> 8) & 0xFF) / 128.0f - 1.0f);
        float g10x = ((h10 & 0xFF) / 128.0f - 1.0f);
        float g10y = (((h10 >> 8) & 0xFF) / 128.0f - 1.0f);
        float g01x = ((h01 & 0xFF) / 128.0f - 1.0f);
        float g01y = (((h01 >> 8) & 0xFF) / 128.0f - 1.0f);
        float g11x = ((h11 & 0xFF) / 128.0f - 1.0f);
        float g11y = (((h11 >> 8) & 0xFF) / 128.0f - 1.0f);

        // Calculate dot products
        float n00 = g00x * xf + g00y * yf;
        float n10 = g10x * (xf - 1.0f) + g10y * yf;
        float n01 = g01x * xf + g01y * (yf - 1.0f);
        float n11 = g11x * (xf - 1.0f) + g11y * (yf - 1.0f);

        // Interpolate
        float nx0 = InterpolateQuintic(n00, n10, xf);
        float nx1 = InterpolateQuintic(n01, n11, xf);
        return InterpolateQuintic(nx0, nx1, yf);
    }

    float PerlinNoise3D(float x, float y, float z, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);
        float xf = x - xi;
        float yf = y - yi;
        float zf = z - zi;

        // Hash corner coordinates
        uint32 h000 = Hash3D(xi, yi, zi, seed);
        uint32 h100 = Hash3D(xi + 1, yi, zi, seed);
        uint32 h010 = Hash3D(xi, yi + 1, zi, seed);
        uint32 h110 = Hash3D(xi + 1, yi + 1, zi, seed);
        uint32 h001 = Hash3D(xi, yi, zi + 1, seed);
        uint32 h101 = Hash3D(xi + 1, yi, zi + 1, seed);
        uint32 h011 = Hash3D(xi, yi + 1, zi + 1, seed);
        uint32 h111 = Hash3D(xi + 1, yi + 1, zi + 1, seed);

        // Generate gradient vectors (simplified)
        float g000 = ((h000 & 0xFFFF) / 32768.0f - 1.0f);
        float g100 = ((h100 & 0xFFFF) / 32768.0f - 1.0f);
        float g010 = ((h010 & 0xFFFF) / 32768.0f - 1.0f);
        float g110 = ((h110 & 0xFFFF) / 32768.0f - 1.0f);
        float g001 = ((h001 & 0xFFFF) / 32768.0f - 1.0f);
        float g101 = ((h101 & 0xFFFF) / 32768.0f - 1.0f);
        float g011 = ((h011 & 0xFFFF) / 32768.0f - 1.0f);
        float g111 = ((h111 & 0xFFFF) / 32768.0f - 1.0f);

        // Calculate noise values (simplified dot products)
        float n000 = g000 * (xf + yf + zf);
        float n100 = g100 * ((xf - 1.0f) + yf + zf);
        float n010 = g010 * (xf + (yf - 1.0f) + zf);
        float n110 = g110 * ((xf - 1.0f) + (yf - 1.0f) + zf);
        float n001 = g001 * (xf + yf + (zf - 1.0f));
        float n101 = g101 * ((xf - 1.0f) + yf + (zf - 1.0f));
        float n011 = g011 * (xf + (yf - 1.0f) + (zf - 1.0f));
        float n111 = g111 * ((xf - 1.0f) + (yf - 1.0f) + (zf - 1.0f));

        // Trilinear interpolation
        float nx00 = InterpolateQuintic(n000, n100, xf);
        float nx10 = InterpolateQuintic(n010, n110, xf);
        float nx01 = InterpolateQuintic(n001, n101, xf);
        float nx11 = InterpolateQuintic(n011, n111, xf);

        float nxy0 = InterpolateQuintic(nx00, nx10, yf);
        float nxy1 = InterpolateQuintic(nx01, nx11, yf);

        return InterpolateQuintic(nxy0, nxy1, zf);
    }

    float PerlinNoise4D(float x, float y, float z, float w, int32 seed)
    {
        // Simplified 4D Perlin noise implementation
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);
        int32 wi = FMath::FloorToInt(w);
        float xf = x - xi;
        float yf = y - yi;
        float zf = z - zi;
        float wf = w - wi;

        // Sample 16 corners of 4D hypercube (simplified)
        float n0000 = ((Hash4D(xi, yi, zi, wi, seed) & 0xFFFF) / 32768.0f - 1.0f) * (xf + yf + zf + wf);
        float n1000 = ((Hash4D(xi + 1, yi, zi, wi, seed) & 0xFFFF) / 32768.0f - 1.0f) * ((xf - 1.0f) + yf + zf + wf);
        float n0100 = ((Hash4D(xi, yi + 1, zi, wi, seed) & 0xFFFF) / 32768.0f - 1.0f) * (xf + (yf - 1.0f) + zf + wf);
        float n1100 = ((Hash4D(xi + 1, yi + 1, zi, wi, seed) & 0xFFFF) / 32768.0f - 1.0f) * ((xf - 1.0f) + (yf - 1.0f) + zf + wf);

        // Simplified interpolation (would need full 4D interpolation in production)
        float nx00 = InterpolateQuintic(n0000, n1000, xf);
        float nx10 = InterpolateQuintic(n0100, n1100, xf);
        float nxy0 = InterpolateQuintic(nx00, nx10, yf);

        return nxy0; // Simplified - full 4D would interpolate through all dimensions
    }

    // =============================================================================
    // SIMPLEX NOISE IMPLEMENTATION
    // =============================================================================

    float SimplexNoise1D(float x, int32 seed)
    {
        // Simplified 1D simplex noise
        return PerlinNoise1D(x, seed) * 0.866f; // Scale factor for simplex
    }

    float SimplexNoise2D(float x, float y, int32 seed)
    {
        // Simplified 2D simplex noise using skewing
        const float F2 = 0.5f * (FMath::Sqrt(3.0f) - 1.0f);
        const float G2 = (3.0f - FMath::Sqrt(3.0f)) / 6.0f;

        float s = (x + y) * F2;
        int32 i = FMath::FloorToInt(x + s);
        int32 j = FMath::FloorToInt(y + s);

        float t = (i + j) * G2;
        float X0 = i - t;
        float Y0 = j - t;
        float x0 = x - X0;
        float y0 = y - Y0;

        // Determine which simplex we are in
        int32 i1, j1;
        if (x0 > y0)
        {
            i1 = 1; j1 = 0;
        }
        else
        {
            i1 = 0; j1 = 1;
        }

        float x1 = x0 - i1 + G2;
        float y1 = y0 - j1 + G2;
        float x2 = x0 - 1.0f + 2.0f * G2;
        float y2 = y0 - 1.0f + 2.0f * G2;

        // Calculate contributions from each corner
        float n0 = 0.0f, n1 = 0.0f, n2 = 0.0f;

        float t0 = 0.5f - x0 * x0 - y0 * y0;
        if (t0 >= 0.0f)
        {
            t0 *= t0;
            uint32 gi0 = Hash2D(i, j, seed);
            float g0x = ((gi0 & 0xFF) / 128.0f - 1.0f);
            float g0y = (((gi0 >> 8) & 0xFF) / 128.0f - 1.0f);
            n0 = t0 * t0 * (g0x * x0 + g0y * y0);
        }

        float t1 = 0.5f - x1 * x1 - y1 * y1;
        if (t1 >= 0.0f)
        {
            t1 *= t1;
            uint32 gi1 = Hash2D(i + i1, j + j1, seed);
            float g1x = ((gi1 & 0xFF) / 128.0f - 1.0f);
            float g1y = (((gi1 >> 8) & 0xFF) / 128.0f - 1.0f);
            n1 = t1 * t1 * (g1x * x1 + g1y * y1);
        }

        float t2 = 0.5f - x2 * x2 - y2 * y2;
        if (t2 >= 0.0f)
        {
            t2 *= t2;
            uint32 gi2 = Hash2D(i + 1, j + 1, seed);
            float g2x = ((gi2 & 0xFF) / 128.0f - 1.0f);
            float g2y = (((gi2 >> 8) & 0xFF) / 128.0f - 1.0f);
            n2 = t2 * t2 * (g2x * x2 + g2y * y2);
        }

        return 70.0f * (n0 + n1 + n2);
    }

    float SimplexNoise3D(float x, float y, float z, int32 seed)
    {
        // Simplified 3D simplex noise
        const float F3 = 1.0f / 3.0f;
        const float G3 = 1.0f / 6.0f;

        float s = (x + y + z) * F3;
        int32 i = FMath::FloorToInt(x + s);
        int32 j = FMath::FloorToInt(y + s);
        int32 k = FMath::FloorToInt(z + s);

        float t = (i + j + k) * G3;
        float X0 = i - t;
        float Y0 = j - t;
        float Z0 = k - t;
        float x0 = x - X0;
        float y0 = y - Y0;
        float z0 = z - Z0;

        // For simplicity, use a basic implementation
        // In production, you'd implement the full simplex algorithm
        return PerlinNoise3D(x, y, z, seed) * 0.866f;
    }

    float SimplexNoise4D(float x, float y, float z, float w, int32 seed)
    {
        // Simplified 4D simplex noise
        return PerlinNoise4D(x, y, z, w, seed) * 0.866f;
    }

    // =============================================================================
    // DISTANCE FUNCTIONS
    // =============================================================================

    float DistanceEuclidean(const FVector2D& a, const FVector2D& b)
    {
        FVector2D diff = a - b;
        return diff.Size();
    }

    float DistanceManhattan(const FVector2D& a, const FVector2D& b)
    {
        FVector2D diff = a - b;
        return FMath::Abs(diff.X) + FMath::Abs(diff.Y);
    }

    float DistanceChebyshev(const FVector2D& a, const FVector2D& b)
    {
        FVector2D diff = a - b;
        return FMath::Max(FMath::Abs(diff.X), FMath::Abs(diff.Y));
    }

    float DistanceMinkowski(const FVector2D& a, const FVector2D& b, float p)
    {
        FVector2D diff = a - b;
        return FMath::Pow(FMath::Pow(FMath::Abs(diff.X), p) + FMath::Pow(FMath::Abs(diff.Y), p), 1.0f / p);
    }

    // =============================================================================
    // WORLEY NOISE IMPLEMENTATION
    // =============================================================================

    float WorleyNoise2D(float x, float y, EAuracronPCGWorleyDistanceFunction DistanceFunction, float Jitter, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);

        float minDistance = FLT_MAX;

        // Check 3x3 grid of cells
        for (int32 i = -1; i <= 1; i++)
        {
            for (int32 j = -1; j <= 1; j++)
            {
                int32 cellX = xi + i;
                int32 cellY = yi + j;

                // Generate random point in cell
                uint32 hash = Hash2D(cellX, cellY, seed);
                float pointX = cellX + ((hash & 0xFFFF) / 65536.0f) * Jitter;
                float pointY = cellY + (((hash >> 16) & 0xFFFF) / 65536.0f) * Jitter;

                FVector2D cellPoint(pointX, pointY);
                FVector2D queryPoint(x, y);

                float distance = 0.0f;
                switch (DistanceFunction)
                {
                    case EAuracronPCGWorleyDistanceFunction::Euclidean:
                        distance = DistanceEuclidean(queryPoint, cellPoint);
                        break;
                    case EAuracronPCGWorleyDistanceFunction::Manhattan:
                        distance = DistanceManhattan(queryPoint, cellPoint);
                        break;
                    case EAuracronPCGWorleyDistanceFunction::Chebyshev:
                        distance = DistanceChebyshev(queryPoint, cellPoint);
                        break;
                    case EAuracronPCGWorleyDistanceFunction::Minkowski:
                        distance = DistanceMinkowski(queryPoint, cellPoint, 3.0f);
                        break;
                    default:
                        distance = DistanceEuclidean(queryPoint, cellPoint);
                        break;
                }

                minDistance = FMath::Min(minDistance, distance);
            }
        }

        return minDistance;
    }

    float WorleyNoise3D(float x, float y, float z, EAuracronPCGWorleyDistanceFunction DistanceFunction, float Jitter, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);

        float minDistance = FLT_MAX;

        // Check 3x3x3 grid of cells
        for (int32 i = -1; i <= 1; i++)
        {
            for (int32 j = -1; j <= 1; j++)
            {
                for (int32 k = -1; k <= 1; k++)
                {
                    int32 cellX = xi + i;
                    int32 cellY = yi + j;
                    int32 cellZ = zi + k;

                    // Generate random point in cell
                    uint32 hash = Hash3D(cellX, cellY, cellZ, seed);
                    float pointX = cellX + ((hash & 0x3FF) / 1024.0f) * Jitter;
                    float pointY = cellY + (((hash >> 10) & 0x3FF) / 1024.0f) * Jitter;
                    float pointZ = cellZ + (((hash >> 20) & 0x3FF) / 1024.0f) * Jitter;

                    FVector cellPoint(pointX, pointY, pointZ);
                    FVector queryPoint(x, y, z);

                    float distance = FVector::Dist(queryPoint, cellPoint);
                    minDistance = FMath::Min(minDistance, distance);
                }
            }
        }

        return minDistance;
    }

    // =============================================================================
    // VALUE NOISE IMPLEMENTATION
    // =============================================================================

    float ValueNoise1D(float x, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        float xf = x - xi;

        float a = (Hash1D(xi, seed) & 0xFFFF) / 65536.0f;
        float b = (Hash1D(xi + 1, seed) & 0xFFFF) / 65536.0f;

        return InterpolateQuintic(a, b, xf);
    }

    float ValueNoise2D(float x, float y, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        float xf = x - xi;
        float yf = y - yi;

        float a = (Hash2D(xi, yi, seed) & 0xFFFF) / 65536.0f;
        float b = (Hash2D(xi + 1, yi, seed) & 0xFFFF) / 65536.0f;
        float c = (Hash2D(xi, yi + 1, seed) & 0xFFFF) / 65536.0f;
        float d = (Hash2D(xi + 1, yi + 1, seed) & 0xFFFF) / 65536.0f;

        float i1 = InterpolateQuintic(a, b, xf);
        float i2 = InterpolateQuintic(c, d, xf);

        return InterpolateQuintic(i1, i2, yf);
    }

    float ValueNoise3D(float x, float y, float z, int32 seed)
    {
        int32 xi = FMath::FloorToInt(x);
        int32 yi = FMath::FloorToInt(y);
        int32 zi = FMath::FloorToInt(z);
        float xf = x - xi;
        float yf = y - yi;
        float zf = z - zi;

        // Sample 8 corners of cube
        float v000 = (Hash3D(xi, yi, zi, seed) & 0xFFFF) / 65536.0f;
        float v100 = (Hash3D(xi + 1, yi, zi, seed) & 0xFFFF) / 65536.0f;
        float v010 = (Hash3D(xi, yi + 1, zi, seed) & 0xFFFF) / 65536.0f;
        float v110 = (Hash3D(xi + 1, yi + 1, zi, seed) & 0xFFFF) / 65536.0f;
        float v001 = (Hash3D(xi, yi, zi + 1, seed) & 0xFFFF) / 65536.0f;
        float v101 = (Hash3D(xi + 1, yi, zi + 1, seed) & 0xFFFF) / 65536.0f;
        float v011 = (Hash3D(xi, yi + 1, zi + 1, seed) & 0xFFFF) / 65536.0f;
        float v111 = (Hash3D(xi + 1, yi + 1, zi + 1, seed) & 0xFFFF) / 65536.0f;

        // Trilinear interpolation
        float i1 = InterpolateQuintic(v000, v100, xf);
        float i2 = InterpolateQuintic(v010, v110, xf);
        float i3 = InterpolateQuintic(v001, v101, xf);
        float i4 = InterpolateQuintic(v011, v111, xf);

        float j1 = InterpolateQuintic(i1, i2, yf);
        float j2 = InterpolateQuintic(i3, i4, yf);

        return InterpolateQuintic(j1, j2, zf);
    }

    // =============================================================================
    // GRADIENT NOISE IMPLEMENTATION
    // =============================================================================

    float GradientNoise2D(float x, float y, int32 seed)
    {
        // Simplified gradient noise - similar to Perlin but with different gradient generation
        return PerlinNoise2D(x, y, seed);
    }

    float GradientNoise3D(float x, float y, float z, int32 seed)
    {
        // Simplified gradient noise - similar to Perlin but with different gradient generation
        return PerlinNoise3D(x, y, z, seed);
    }

    // =============================================================================
    // FRACTAL AND UTILITY FUNCTIONS
    // =============================================================================

    float ApplyFractal(float BaseNoise, EAuracronPCGFractalType FractalType, const FAuracronPCGNoiseDescriptor& Descriptor)
    {
        switch (FractalType)
        {
            case EAuracronPCGFractalType::FBM:
                return BaseNoise; // Already applied in octave generation
            case EAuracronPCGFractalType::Turbulence:
                return FMath::Abs(BaseNoise);
            case EAuracronPCGFractalType::RidgedMulti:
                return 1.0f - FMath::Abs(BaseNoise);
            case EAuracronPCGFractalType::Billow:
                return FMath::Abs(BaseNoise) * 2.0f - 1.0f;
            default:
                return BaseNoise;
        }
    }

    float NormalizeNoise(float NoiseValue, float Min, float Max)
    {
        return (NoiseValue - Min) / (Max - Min);
    }

    float FractalNoise(float x, float y, float z, const FAuracronPCGNoiseDescriptor& Descriptor)
    {
        float result = 0.0f;
        float amplitude = 1.0f;
        float frequency = Descriptor.Frequency;
        float maxValue = 0.0f;

        for (int32 i = 0; i < Descriptor.Octaves; i++)
        {
            float octaveNoise = PerlinNoise3D(x * frequency, y * frequency, z * frequency, Descriptor.Seed + i);
            result += octaveNoise * amplitude;
            maxValue += amplitude;
            amplitude *= Descriptor.Persistence;
            frequency *= Descriptor.Lacunarity;
        }

        return result / maxValue;
    }

    float DomainWarp(float x, float y, float z, const FAuracronPCGNoiseDescriptor& WarpDescriptor, float Strength)
    {
        float warpX = PerlinNoise3D(x, y, z, WarpDescriptor.Seed) * Strength;
        float warpY = PerlinNoise3D(x + 1000.0f, y, z, WarpDescriptor.Seed) * Strength;
        float warpZ = PerlinNoise3D(x, y + 1000.0f, z, WarpDescriptor.Seed) * Strength;
        
        return PerlinNoise3D(x + warpX, y + warpY, z + warpZ, WarpDescriptor.Seed);
    }

    void GenerateGradientTable(TArray<FVector>& GradientTable, int32 Size, int32 Seed)
    {
        GradientTable.SetNum(Size);
        FRandomStream RandomStream(Seed);
        
        for (int32 i = 0; i < Size; i++)
        {
            float angle = RandomStream.FRand() * 2.0f * PI;
            GradientTable[i] = FVector(FMath::Cos(angle), FMath::Sin(angle), 0.0f);
        }
    }

    void GeneratePermutationTable(TArray<int32>& PermutationTable, int32 Size, int32 Seed)
    {
        PermutationTable.SetNum(Size);
        FRandomStream RandomStream(Seed);
        
        for (int32 i = 0; i < Size; i++)
        {
            PermutationTable[i] = i;
        }
        
        // Fisher-Yates shuffle
        for (int32 i = Size - 1; i > 0; i--)
        {
            int32 j = RandomStream.RandRange(0, i);
            int32 temp = PermutationTable[i];
            PermutationTable[i] = PermutationTable[j];
            PermutationTable[j] = temp;
        }
    }
}
