// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage LOD Management System Header
// Bridge 4.5: Foliage - LOD Management

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"

// UE5.6 Foliage LOD includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "FoliageType.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"

// LOD and Performance includes
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Impostor Baker Plugin includes (UE5.6)
#include "ImpostorBaker/Public/ImpostorBakerModule.h"
#include "ImpostorBaker/Public/ImpostorBakerSettings.h"
#include "ImpostorBaker/Public/ImpostorBakerComponent.h"

// Performance and Culling includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "RenderingThread.h"
#include "RHI.h"
#include "RHIResources.h"

// Math and Utilities
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Misc/DateTime.h"

#include "AuracronFoliageLOD.generated.h"

// Forward declarations
class UAuracronFoliageLODManager;
class UAuracronFoliageBiomeManager;

// =============================================================================
// LOD TYPES AND ENUMS
// =============================================================================

// LOD types
UENUM(BlueprintType)
enum class EAuracronFoliageLODType : uint8
{
    DistanceBased           UMETA(DisplayName = "Distance Based"),
    Impostor                UMETA(DisplayName = "Impostor"),
    Billboard               UMETA(DisplayName = "Billboard"),
    Nanite                  UMETA(DisplayName = "Nanite"),
    Hybrid                  UMETA(DisplayName = "Hybrid"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Impostor types (based on UE5.6 Impostor Baker Plugin)
UENUM(BlueprintType)
enum class EAuracronImpostorType : uint8
{
    FullSphere              UMETA(DisplayName = "Full Sphere"),
    UpperHemisphere         UMETA(DisplayName = "Upper Hemisphere"),
    TraditionalBillboard    UMETA(DisplayName = "Traditional Billboard"),
    Custom                  UMETA(DisplayName = "Custom")
};

// LOD transition types
UENUM(BlueprintType)
enum class EAuracronLODTransitionType : uint8
{
    Instant                 UMETA(DisplayName = "Instant"),
    Fade                    UMETA(DisplayName = "Fade"),
    Dither                  UMETA(DisplayName = "Dither"),
    Crossfade               UMETA(DisplayName = "Crossfade"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Culling types
UENUM(BlueprintType)
enum class EAuracronCullingType : uint8
{
    Distance                UMETA(DisplayName = "Distance"),
    Frustum                 UMETA(DisplayName = "Frustum"),
    Occlusion               UMETA(DisplayName = "Occlusion"),
    Combined                UMETA(DisplayName = "Combined"),
    Adaptive                UMETA(DisplayName = "Adaptive"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Performance quality levels
UENUM(BlueprintType)
enum class EAuracronLODQualityLevel : uint8
{
    Low                     UMETA(DisplayName = "Low"),
    Medium                  UMETA(DisplayName = "Medium"),
    High                    UMETA(DisplayName = "High"),
    Epic                    UMETA(DisplayName = "Epic"),
    Cinematic               UMETA(DisplayName = "Cinematic"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// LOD CONFIGURATION DATA
// =============================================================================

/**
 * LOD Configuration Data
 * Configuration for LOD system behavior
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronLODConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD System")
    bool bEnableLODSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD System")
    EAuracronFoliageLODType DefaultLODType = EAuracronFoliageLODType::DistanceBased;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD System")
    EAuracronLODQualityLevel QualityLevel = EAuracronLODQualityLevel::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance LOD")
    float LOD0Distance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance LOD")
    float LOD1Distance = 2500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance LOD")
    float LOD2Distance = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance LOD")
    float LOD3Distance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    float StartCullDistance = 8000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    float EndCullDistance = 12000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    EAuracronCullingType CullingType = EAuracronCullingType::Combined;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transitions")
    EAuracronLODTransitionType TransitionType = EAuracronLODTransitionType::Fade;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transitions")
    float TransitionDuration = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transitions")
    float FadeMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncLODUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxLODUpdatesPerFrame = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceMonitoring = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bEnableImpostorGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    EAuracronImpostorType ImpostorType = EAuracronImpostorType::UpperHemisphere;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 ImpostorResolution = 2048;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 ImpostorFramesXY = 16;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    bool bEnableBillboardGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    int32 BillboardResolution = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    bool bCenterXYOnMeshPivot = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    float ReferencePlaneTop = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    float ReferencePlaneSides = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HISM Optimization")
    bool bEnableHISMOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HISM Optimization")
    int32 MaxInstancesPerCluster = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HISM Optimization")
    float ClusterRadius = 2000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HISM Optimization")
    bool bEnableGPUCulling = true;

    FAuracronLODConfiguration()
    {
        bEnableLODSystem = true;
        DefaultLODType = EAuracronFoliageLODType::DistanceBased;
        QualityLevel = EAuracronLODQualityLevel::High;
        LOD0Distance = 1000.0f;
        LOD1Distance = 2500.0f;
        LOD2Distance = 5000.0f;
        LOD3Distance = 10000.0f;
        StartCullDistance = 8000.0f;
        EndCullDistance = 12000.0f;
        CullingType = EAuracronCullingType::Combined;
        TransitionType = EAuracronLODTransitionType::Fade;
        TransitionDuration = 1.0f;
        FadeMultiplier = 1.0f;
        bEnableAsyncLODUpdates = true;
        MaxLODUpdatesPerFrame = 100;
        LODUpdateInterval = 0.1f;
        bEnablePerformanceMonitoring = true;
        bEnableImpostorGeneration = true;
        ImpostorType = EAuracronImpostorType::UpperHemisphere;
        ImpostorResolution = 2048;
        ImpostorFramesXY = 16;
        bEnableBillboardGeneration = true;
        BillboardResolution = 1024;
        bCenterXYOnMeshPivot = false;
        ReferencePlaneTop = 0.5f;
        ReferencePlaneSides = 0.5f;
        bEnableHISMOptimization = true;
        MaxInstancesPerCluster = 100;
        ClusterRadius = 2000.0f;
        bEnableGPUCulling = true;
    }
};

// =============================================================================
// IMPOSTOR DATA
// =============================================================================

/**
 * Impostor Data
 * Data for impostor generation and management
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronImpostorData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    FString ImpostorId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TSoftObjectPtr<UStaticMesh> SourceMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TSoftObjectPtr<UStaticMesh> ImpostorMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TSoftObjectPtr<UMaterialInterface> ImpostorMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    EAuracronImpostorType ImpostorType = EAuracronImpostorType::UpperHemisphere;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 Resolution = 2048;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 FramesXY = 16;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bCaptureUsingGBuffer = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bOrthographic = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float CameraDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 SceneCaptureResolution = 512;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TArray<FString> ColorMapsToRender;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TMap<FString, FString> ChannelPackedMasks;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float ConstantSpecular = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float ConstantRoughness = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float ConstantOpacity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    FLinearColor SubsurfaceColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bIsGenerated = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    FDateTime GenerationTime;

    FAuracronImpostorData()
    {
        ImpostorType = EAuracronImpostorType::UpperHemisphere;
        Resolution = 2048;
        FramesXY = 16;
        bCaptureUsingGBuffer = true;
        bOrthographic = true;
        CameraDistance = 1000.0f;
        SceneCaptureResolution = 512;
        ColorMapsToRender = {"BaseColor", "Normal"};
        ConstantSpecular = 0.5f;
        ConstantRoughness = 0.5f;
        ConstantOpacity = 1.0f;
        SubsurfaceColor = FLinearColor::White;
        bIsGenerated = false;
        GenerationTime = FDateTime::Now();
    }
};

// =============================================================================
// BILLBOARD DATA
// =============================================================================

/**
 * Billboard Data
 * Data for billboard generation and management
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronBillboardData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    FString BillboardId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    TSoftObjectPtr<UStaticMesh> SourceMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    TSoftObjectPtr<UStaticMesh> BillboardMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    TSoftObjectPtr<UMaterialInterface> BillboardMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    int32 Resolution = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    int32 HorizontalFrames = 8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    bool bCenterXYOnMeshPivot = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    float ReferencePlaneTop = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    float ReferencePlaneSides = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    bool bUseSpriteVertexShader = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    float DitherTransition = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    float VertexShaderThreshold = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    bool bIsGenerated = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    FDateTime GenerationTime;

    FAuracronBillboardData()
    {
        Resolution = 1024;
        HorizontalFrames = 8;
        bCenterXYOnMeshPivot = false;
        ReferencePlaneTop = 0.5f;
        ReferencePlaneSides = 0.5f;
        bUseSpriteVertexShader = false;
        DitherTransition = 0.5f;
        VertexShaderThreshold = 0.3f;
        bIsGenerated = false;
        GenerationTime = FDateTime::Now();
    }
};

// =============================================================================
// LOD PERFORMANCE DATA
// =============================================================================

/**
 * LOD Performance Data
 * Performance metrics for LOD system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronLODPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 VisibleInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CulledInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 LOD0Instances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 LOD1Instances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 LOD2Instances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 LOD3Instances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ImpostorInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 BillboardInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AverageFrameTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float CullingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float RenderTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 DrawCalls = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 Triangles = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float GPUMemoryUsageMB = 0.0f;

    FAuracronLODPerformanceData()
    {
        TotalInstances = 0;
        VisibleInstances = 0;
        CulledInstances = 0;
        LOD0Instances = 0;
        LOD1Instances = 0;
        LOD2Instances = 0;
        LOD3Instances = 0;
        ImpostorInstances = 0;
        BillboardInstances = 0;
        AverageFrameTime = 0.0f;
        LODUpdateTime = 0.0f;
        CullingTime = 0.0f;
        RenderTime = 0.0f;
        DrawCalls = 0;
        Triangles = 0;
        MemoryUsageMB = 0.0f;
        GPUMemoryUsageMB = 0.0f;
    }
};

// =============================================================================
// LOD INSTANCE DATA
// =============================================================================

/**
 * LOD Instance Data
 * Data for individual foliage instance LOD management
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronLODInstanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    FString FoliageTypeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    FString BiomeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    FTransform InstanceTransform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 CurrentLODLevel = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 TargetLODLevel = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float DistanceToCamera = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float FadeAmount = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bIsVisible = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bIsCulled = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bIsTransitioning = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float TransitionProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float LastUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TriangleCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float RenderCost = 0.0f;

    FAuracronLODInstanceData()
    {
        CurrentLODLevel = 0;
        TargetLODLevel = 0;
        DistanceToCamera = 0.0f;
        FadeAmount = 1.0f;
        bIsVisible = true;
        bIsCulled = false;
        bIsTransitioning = false;
        TransitionProgress = 0.0f;
        LastUpdateTime = 0.0f;
        TriangleCount = 0;
        RenderCost = 0.0f;
    }
};

// =============================================================================
// FOLIAGE LOD MANAGER
// =============================================================================

/**
 * Foliage LOD Manager
 * Manager for the foliage LOD system including distance-based LOD, impostors, and billboards
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronFoliageLODManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    static UAuracronFoliageLODManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void Initialize(const FAuracronLODConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void Tick(float DeltaTime);

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void SetConfiguration(const FAuracronLODConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronLODConfiguration GetConfiguration() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void SetQualityLevel(EAuracronLODQualityLevel QualityLevel);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    EAuracronLODQualityLevel GetQualityLevel() const;

    // Distance-based LOD
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdateDistanceBasedLOD(const FVector& CameraLocation);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    int32 CalculateLODLevel(float Distance) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    float CalculateFadeAmount(float Distance, int32 LODLevel) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void SetLODDistances(float LOD0, float LOD1, float LOD2, float LOD3);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<float> GetLODDistances() const;

    // Culling system
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdateCulling(const FVector& CameraLocation, const FVector& CameraDirection);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool ShouldCullInstance(const FAuracronLODInstanceData& InstanceData, const FVector& CameraLocation) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void SetCullDistances(float StartDistance, float EndDistance);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void GetCullDistances(float& StartDistance, float& EndDistance) const;

    // Impostor management
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool GenerateImpostor(UStaticMesh* SourceMesh, const FAuracronImpostorData& ImpostorSettings);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool RegisterImpostor(const FAuracronImpostorData& ImpostorData);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool UnregisterImpostor(const FString& ImpostorId);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronImpostorData GetImpostor(const FString& ImpostorId) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FAuracronImpostorData> GetAllImpostors() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool BatchGenerateImpostors(const TArray<UStaticMesh*>& SourceMeshes, const FAuracronImpostorData& Settings);

    // Billboard management
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool GenerateBillboard(UStaticMesh* SourceMesh, const FAuracronBillboardData& BillboardSettings);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool RegisterBillboard(const FAuracronBillboardData& BillboardData);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool UnregisterBillboard(const FString& BillboardId);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronBillboardData GetBillboard(const FString& BillboardId) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FAuracronBillboardData> GetAllBillboards() const;

    // Instance management
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool RegisterInstance(const FAuracronLODInstanceData& InstanceData);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool UnregisterInstance(const FString& InstanceId);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronLODInstanceData GetInstance(const FString& InstanceId) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FAuracronLODInstanceData> GetInstancesInRadius(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdateInstanceLOD(const FString& InstanceId, int32 NewLODLevel);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdateInstanceVisibility(const FString& InstanceId, bool bVisible);

    // HISM optimization
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void OptimizeHISMComponents();

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    UHierarchicalInstancedStaticMeshComponent* GetOrCreateHISMComponent(UStaticMesh* Mesh, int32 LODLevel);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdateHISMInstances(UHierarchicalInstancedStaticMeshComponent* Component);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void SetHISMCullingDistance(UHierarchicalInstancedStaticMeshComponent* Component, float StartDistance, float EndDistance);

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronLODPerformanceData GetPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdatePerformanceMetrics();

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    float GetAverageFrameTime() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    int32 GetTotalInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    int32 GetVisibleInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TMap<int32, int32> GetLODDistribution() const;

    // Biome integration
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void SetBiomeLODSettings(const FString& BiomeId, const FAuracronLODConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronLODConfiguration GetBiomeLODSettings(const FString& BiomeId) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void ApplyBiomeLODToInstances(const FString& BiomeId);

    // Transition management
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void StartLODTransition(const FString& InstanceId, int32 FromLOD, int32 ToLOD);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdateLODTransitions(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool IsInstanceTransitioning(const FString& InstanceId) const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void DrawDebugLODInfo(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void LogPerformanceStatistics() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnLODChanged, FString, InstanceId, int32, OldLOD, int32, NewLOD);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInstanceCulled, FString, InstanceId, bool, bCulled);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnImpostorGenerated, FString, ImpostorId, bool, bSuccess);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBillboardGenerated, FString, BillboardId, bool, bSuccess);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLODChanged OnLODChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnInstanceCulled OnInstanceCulled;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnImpostorGenerated OnImpostorGenerated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBillboardGenerated OnBillboardGenerated;

private:
    static UAuracronFoliageLODManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronLODConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // LOD data
    TMap<FString, FAuracronLODInstanceData> RegisteredInstances;
    TMap<FString, FAuracronImpostorData> RegisteredImpostors;
    TMap<FString, FAuracronBillboardData> RegisteredBillboards;
    TMap<FString, FAuracronLODConfiguration> BiomeLODSettings;

    // HISM components
    TMap<FString, TWeakObjectPtr<UHierarchicalInstancedStaticMeshComponent>> HISMComponents;

    // Performance data
    FAuracronLODPerformanceData PerformanceData;
    float LastPerformanceUpdate = 0.0f;
    float LastLODUpdate = 0.0f;

    // Transition data
    TMap<FString, float> TransitionTimers;

    // Camera data
    FVector LastCameraLocation = FVector::ZeroVector;
    FVector LastCameraDirection = FVector::ForwardVector;

    // Thread safety
    mutable FCriticalSection LODLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateInstanceId() const;
    FString GenerateImpostorId() const;
    FString GenerateBillboardId() const;
    void UpdateInstanceLODInternal(FAuracronLODInstanceData& InstanceData, const FVector& CameraLocation);
    void ApplyLODToHISMComponent(UHierarchicalInstancedStaticMeshComponent* Component, const FAuracronLODInstanceData& InstanceData);
    void UpdatePerformanceDataInternal();
    bool GenerateImpostorInternal(UStaticMesh* SourceMesh, const FAuracronImpostorData& Settings);
    bool GenerateBillboardInternal(UStaticMesh* SourceMesh, const FAuracronBillboardData& Settings);
    void ProcessLODTransition(const FString& InstanceId, FAuracronLODInstanceData& InstanceData, float DeltaTime);
};
