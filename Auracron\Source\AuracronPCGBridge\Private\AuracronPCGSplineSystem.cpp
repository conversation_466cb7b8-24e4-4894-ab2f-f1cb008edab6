// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Spline System Implementation
// Bridge 2.8: PCG Framework - Spline System

#include "AuracronPCGSplineSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSplineData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Spline includes
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/SplineActor.h"
#include "SplineMeshActor.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Async/ParallelFor.h"

// =============================================================================
// ADVANCED SPLINE CREATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedSplineCreatorSettings::UAuracronPCGAdvancedSplineCreatorSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Spline Creator");
    NodeMetadata.NodeDescription = TEXT("Creates splines from various sources with advanced features");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Spawner;
    NodeMetadata.Tags.Add(TEXT("Spline"));
    NodeMetadata.Tags.Add(TEXT("Creator"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("Path"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.2f, 0.8f);
}

void UAuracronPCGAdvancedSplineCreatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (PathFindingDescriptor.PathFindingMode != EAuracronPCGPathFindingMode::None)
    {
        FPCGPinProperties& StartPin = InputPins.Emplace_GetRef();
        StartPin.Label = TEXT("Start Points");
        StartPin.AllowedTypes = EPCGDataType::Point;
        StartPin.bAdvancedPin = true;

        FPCGPinProperties& EndPin = InputPins.Emplace_GetRef();
        EndPin.Label = TEXT("End Points");
        EndPin.AllowedTypes = EPCGDataType::Point;
        EndPin.bAdvancedPin = true;

        FPCGPinProperties& ObstaclesPin = InputPins.Emplace_GetRef();
        ObstaclesPin.Label = TEXT("Obstacles");
        ObstaclesPin.AllowedTypes = EPCGDataType::Spatial;
        ObstaclesPin.bAdvancedPin = true;
    }
}

void UAuracronPCGAdvancedSplineCreatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Spline;

    if (bOutputSplinePoints)
    {
        FPCGPinProperties& PointsPin = OutputPins.Emplace_GetRef();
        PointsPin.Label = TEXT("Spline Points");
        PointsPin.AllowedTypes = EPCGDataType::Point;
        PointsPin.bAdvancedPin = true;
    }

    if (bOutputDebugInfo)
    {
        FPCGPinProperties& DebugPin = OutputPins.Emplace_GetRef();
        DebugPin.Label = TEXT("Debug Info");
        DebugPin.AllowedTypes = EPCGDataType::Attribute;
        DebugPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAdvancedSplineCreatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                FPCGDataCollection& OutputData, 
                                                                                const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedSplineCreatorSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedSplineCreatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Spline Creator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 SplinesCreated = 0;
        TArray<UPCGSplineData*> CreatedSplines;

        // Process based on creation mode
        switch (Settings->CreationMode)
        {
            case EAuracronPCGSplineCreationMode::FromPoints:
                ProcessFromPoints(InputData, CreatedSplines, Settings, TotalProcessed, SplinesCreated);
                break;
            case EAuracronPCGSplineCreationMode::Procedural:
                ProcessProcedural(InputData, CreatedSplines, Settings, TotalProcessed, SplinesCreated);
                break;
            case EAuracronPCGSplineCreationMode::FromPath:
                ProcessFromPath(InputData, CreatedSplines, Settings, TotalProcessed, SplinesCreated);
                break;
            default:
                ProcessFromPoints(InputData, CreatedSplines, Settings, TotalProcessed, SplinesCreated);
                break;
        }

        // Create output data
        for (UPCGSplineData* SplineData : CreatedSplines)
        {
            if (SplineData)
            {
                FPCGTaggedData& SplineTaggedData = OutputData.TaggedData.Emplace_GetRef();
                SplineTaggedData.Data = SplineData;
                SplineTaggedData.Pin = TEXT("Output");

                // Generate spline points if requested
                if (Settings->bOutputSplinePoints)
                {
                    UPCGPointData* SplinePoints = CreateSplinePointsData(SplineData, Settings);
                    if (SplinePoints)
                    {
                        FPCGTaggedData& PointsTaggedData = OutputData.TaggedData.Emplace_GetRef();
                        PointsTaggedData.Data = SplinePoints;
                        PointsTaggedData.Pin = TEXT("Spline Points");
                    }
                }
            }
        }

        // Generate debug info if requested
        if (Settings->bOutputDebugInfo)
        {
            UPCGAttributeSet* DebugInfo = CreateDebugInfo(Settings, TotalProcessed, SplinesCreated);
            if (DebugInfo)
            {
                FPCGTaggedData& DebugTaggedData = OutputData.TaggedData.Emplace_GetRef();
                DebugTaggedData.Data = DebugInfo;
                DebugTaggedData.Pin = TEXT("Debug Info");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Spline Creator processed %d points and created %d splines"), 
                                  TotalProcessed, SplinesCreated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Spline Creator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGAdvancedSplineCreatorElement::ProcessFromPoints(const FPCGDataCollection& InputData,
                                                                 TArray<UPCGSplineData*>& OutSplines,
                                                                 const UAuracronPCGAdvancedSplineCreatorSettings* Settings,
                                                                 int32& OutTotalProcessed,
                                                                 int32& OutSplinesCreated) const
{
    for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
    {
        const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
        if (!InputPointData)
        {
            continue;
        }

        const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
        if (InputPoints.Num() < 2)
        {
            continue; // Need at least 2 points for a spline
        }

        // Extract point locations
        TArray<FVector> PointLocations;
        PointLocations.Reserve(InputPoints.Num());
        
        for (const FPCGPoint& Point : InputPoints)
        {
            PointLocations.Add(Point.Transform.GetLocation());
        }

        // Sort points by distance if requested
        if (Settings->bSortPointsByDistance)
        {
            SortPointsByDistance(PointLocations);
        }

        // Remove duplicate points if requested
        if (Settings->bRemoveDuplicatePoints)
        {
            RemoveDuplicatePoints(PointLocations, Settings->DuplicateThreshold);
        }

        // Simplify spline if requested
        if (Settings->bSimplifySpline)
        {
            SimplifyPointArray(PointLocations, Settings->SimplificationTolerance);
        }

        // Create spline from points
        UPCGSplineData* SplineData = CreateSplineDataFromPoints(PointLocations, Settings);
        if (SplineData)
        {
            OutSplines.Add(SplineData);
            OutSplinesCreated++;
        }

        OutTotalProcessed += InputPoints.Num();
    }
}

void FAuracronPCGAdvancedSplineCreatorElement::ProcessProcedural(const FPCGDataCollection& InputData,
                                                                 TArray<UPCGSplineData*>& OutSplines,
                                                                 const UAuracronPCGAdvancedSplineCreatorSettings* Settings,
                                                                 int32& OutTotalProcessed,
                                                                 int32& OutSplinesCreated) const
{
    // Generate procedural spline points
    TArray<FVector> ProceduralPoints;
    GenerateProceduralPoints(ProceduralPoints, Settings);

    if (ProceduralPoints.Num() >= 2)
    {
        UPCGSplineData* SplineData = CreateSplineDataFromPoints(ProceduralPoints, Settings);
        if (SplineData)
        {
            OutSplines.Add(SplineData);
            OutSplinesCreated++;
        }
    }

    OutTotalProcessed += ProceduralPoints.Num();
}

void FAuracronPCGAdvancedSplineCreatorElement::ProcessFromPath(const FPCGDataCollection& InputData,
                                                               TArray<UPCGSplineData*>& OutSplines,
                                                               const UAuracronPCGAdvancedSplineCreatorSettings* Settings,
                                                               int32& OutTotalProcessed,
                                                               int32& OutSplinesCreated) const
{
    // Get start and end points from input
    TArray<FVector> StartPoints, EndPoints;
    ExtractStartEndPoints(InputData, StartPoints, EndPoints);

    for (int32 i = 0; i < FMath::Min(StartPoints.Num(), EndPoints.Num()); i++)
    {
        // Find path between start and end points
        TArray<FVector> PathPoints = UAuracronPCGSplineSystemUtils::FindPath(
            StartPoints[i], EndPoints[i], Settings->PathFindingDescriptor);

        if (PathPoints.Num() >= 2)
        {
            UPCGSplineData* SplineData = CreateSplineDataFromPoints(PathPoints, Settings);
            if (SplineData)
            {
                OutSplines.Add(SplineData);
                OutSplinesCreated++;
            }
        }

        OutTotalProcessed += PathPoints.Num();
    }
}

void FAuracronPCGAdvancedSplineCreatorElement::SortPointsByDistance(TArray<FVector>& Points) const
{
    if (Points.Num() < 2)
    {
        return;
    }

    TArray<FVector> SortedPoints;
    SortedPoints.Reserve(Points.Num());
    
    // Start with the first point
    SortedPoints.Add(Points[0]);
    Points.RemoveAt(0);

    // Find the nearest point to the last added point
    while (Points.Num() > 0)
    {
        FVector LastPoint = SortedPoints.Last();
        int32 NearestIndex = 0;
        float NearestDistance = FVector::Dist(LastPoint, Points[0]);

        for (int32 i = 1; i < Points.Num(); i++)
        {
            float Distance = FVector::Dist(LastPoint, Points[i]);
            if (Distance < NearestDistance)
            {
                NearestDistance = Distance;
                NearestIndex = i;
            }
        }

        SortedPoints.Add(Points[NearestIndex]);
        Points.RemoveAt(NearestIndex);
    }

    Points = SortedPoints;
}

void FAuracronPCGAdvancedSplineCreatorElement::RemoveDuplicatePoints(TArray<FVector>& Points, float Threshold) const
{
    TArray<FVector> UniquePoints;
    UniquePoints.Reserve(Points.Num());

    for (const FVector& Point : Points)
    {
        bool bIsDuplicate = false;
        for (const FVector& UniquePoint : UniquePoints)
        {
            if (FVector::Dist(Point, UniquePoint) < Threshold)
            {
                bIsDuplicate = true;
                break;
            }
        }

        if (!bIsDuplicate)
        {
            UniquePoints.Add(Point);
        }
    }

    Points = UniquePoints;
}

void FAuracronPCGAdvancedSplineCreatorElement::SimplifyPointArray(TArray<FVector>& Points, float Tolerance) const
{
    if (Points.Num() < 3)
    {
        return;
    }

    // Douglas-Peucker algorithm for line simplification
    TArray<bool> KeepPoint;
    KeepPoint.SetNumZeroed(Points.Num());
    KeepPoint[0] = true;
    KeepPoint[Points.Num() - 1] = true;

    SimplifyRecursive(Points, KeepPoint, 0, Points.Num() - 1, Tolerance);

    // Build simplified array
    TArray<FVector> SimplifiedPoints;
    for (int32 i = 0; i < Points.Num(); i++)
    {
        if (KeepPoint[i])
        {
            SimplifiedPoints.Add(Points[i]);
        }
    }

    Points = SimplifiedPoints;
}

void FAuracronPCGAdvancedSplineCreatorElement::SimplifyRecursive(const TArray<FVector>& Points, TArray<bool>& KeepPoint, int32 StartIndex, int32 EndIndex, float Tolerance) const
{
    if (EndIndex - StartIndex <= 1)
    {
        return;
    }

    float MaxDistance = 0.0f;
    int32 MaxIndex = StartIndex;

    FVector LineStart = Points[StartIndex];
    FVector LineEnd = Points[EndIndex];

    for (int32 i = StartIndex + 1; i < EndIndex; i++)
    {
        float Distance = PointToLineDistance(Points[i], LineStart, LineEnd);
        if (Distance > MaxDistance)
        {
            MaxDistance = Distance;
            MaxIndex = i;
        }
    }

    if (MaxDistance > Tolerance)
    {
        KeepPoint[MaxIndex] = true;
        SimplifyRecursive(Points, KeepPoint, StartIndex, MaxIndex, Tolerance);
        SimplifyRecursive(Points, KeepPoint, MaxIndex, EndIndex, Tolerance);
    }
}

float FAuracronPCGAdvancedSplineCreatorElement::PointToLineDistance(const FVector& Point, const FVector& LineStart, const FVector& LineEnd) const
{
    FVector LineDirection = (LineEnd - LineStart).GetSafeNormal();
    FVector PointDirection = Point - LineStart;
    
    float ProjectedLength = FVector::DotProduct(PointDirection, LineDirection);
    FVector ProjectedPoint = LineStart + LineDirection * ProjectedLength;
    
    return FVector::Dist(Point, ProjectedPoint);
}

void FAuracronPCGAdvancedSplineCreatorElement::GenerateProceduralPoints(TArray<FVector>& OutPoints, const UAuracronPCGAdvancedSplineCreatorSettings* Settings) const
{
    OutPoints.Empty();
    OutPoints.Reserve(Settings->ProceduralPointCount);

    FRandomStream RandomStream(Settings->ProceduralSeed);
    float SegmentLength = Settings->ProceduralLength / FMath::Max(1, Settings->ProceduralPointCount - 1);

    for (int32 i = 0; i < Settings->ProceduralPointCount; i++)
    {
        float T = static_cast<float>(i) / FMath::Max(1, Settings->ProceduralPointCount - 1);
        
        // Base position along X axis
        FVector BasePosition = FVector(T * Settings->ProceduralLength, 0.0f, 0.0f);
        
        // Add variation
        FVector Variation = FVector(
            RandomStream.FRandRange(-Settings->ProceduralVariation, Settings->ProceduralVariation) * SegmentLength,
            RandomStream.FRandRange(-Settings->ProceduralVariation, Settings->ProceduralVariation) * SegmentLength,
            RandomStream.FRandRange(-Settings->ProceduralVariation, Settings->ProceduralVariation) * SegmentLength * 0.5f
        );

        OutPoints.Add(BasePosition + Variation);
    }
}

void FAuracronPCGAdvancedSplineCreatorElement::ExtractStartEndPoints(const FPCGDataCollection& InputData, TArray<FVector>& OutStartPoints, TArray<FVector>& OutEndPoints) const
{
    OutStartPoints.Empty();
    OutEndPoints.Empty();

    // Extract start points
    for (const FPCGTaggedData& TaggedData : InputData.GetInputsByPin(TEXT("Start Points")))
    {
        const UPCGPointData* PointData = Cast<UPCGPointData>(TaggedData.Data);
        if (PointData)
        {
            for (const FPCGPoint& Point : PointData->GetPoints())
            {
                OutStartPoints.Add(Point.Transform.GetLocation());
            }
        }
    }

    // Extract end points
    for (const FPCGTaggedData& TaggedData : InputData.GetInputsByPin(TEXT("End Points")))
    {
        const UPCGPointData* PointData = Cast<UPCGPointData>(TaggedData.Data);
        if (PointData)
        {
            for (const FPCGPoint& Point : PointData->GetPoints())
            {
                OutEndPoints.Add(Point.Transform.GetLocation());
            }
        }
    }
}

UPCGSplineData* FAuracronPCGAdvancedSplineCreatorElement::CreateSplineDataFromPoints(const TArray<FVector>& Points, const UAuracronPCGAdvancedSplineCreatorSettings* Settings) const
{
    if (Points.Num() < 2)
    {
        return nullptr;
    }

    UPCGSplineData* SplineData = NewObject<UPCGSplineData>();
    
    // Create spline component
    USplineComponent* SplineComponent = UAuracronPCGSplineSystemUtils::CreateSplineFromPoints(
        Points, Settings->bClosedSpline, Settings->DefaultTangentMode);
    
    if (SplineComponent)
    {
        // Initialize spline data from component
        AuracronPCGSplineSystemUtils::CreatePCGDataFromSpline(SplineComponent);
        
        // Apply tangent scale
        if (Settings->TangentScale != 1.0f)
        {
            for (int32 i = 0; i < SplineComponent->GetNumberOfSplinePoints(); i++)
            {
                FVector ArriveTangent = SplineComponent->GetArriveTangentAtSplinePoint(i, ESplineCoordinateSpace::Local);
                FVector LeaveTangent = SplineComponent->GetLeaveTangentAtSplinePoint(i, ESplineCoordinateSpace::Local);
                
                SplineComponent->SetTangentsAtSplinePoint(i, 
                    ArriveTangent * Settings->TangentScale, 
                    LeaveTangent * Settings->TangentScale, 
                    ESplineCoordinateSpace::Local);
            }
        }
    }

    return SplineData;
}

UPCGPointData* FAuracronPCGAdvancedSplineCreatorElement::CreateSplinePointsData(UPCGSplineData* SplineData, const UAuracronPCGAdvancedSplineCreatorSettings* Settings) const
{
    // Create point data from spline
    // Simplified implementation - in production you'd extract actual spline points
    UPCGPointData* PointData = NewObject<UPCGPointData>();
    return PointData;
}

UPCGAttributeSet* FAuracronPCGAdvancedSplineCreatorElement::CreateDebugInfo(const UAuracronPCGAdvancedSplineCreatorSettings* Settings, int32 TotalProcessed, int32 SplinesCreated) const
{
    UPCGAttributeSet* DebugInfo = NewObject<UPCGAttributeSet>();

    // Add debug information
    // Simplified implementation - in production you'd add proper debug attributes

    return DebugInfo;
}

// =============================================================================
// SPLINE POINT DISTRIBUTOR IMPLEMENTATION
// =============================================================================

UAuracronPCGSplinePointDistributorSettings::UAuracronPCGSplinePointDistributorSettings()
{
    NodeMetadata.NodeName = TEXT("Spline Point Distributor");
    NodeMetadata.NodeDescription = TEXT("Distributes points along splines using various distribution algorithms");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Sampler;
    NodeMetadata.Tags.Add(TEXT("Spline"));
    NodeMetadata.Tags.Add(TEXT("Distribution"));
    NodeMetadata.Tags.Add(TEXT("Points"));
    NodeMetadata.Tags.Add(TEXT("Sampling"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.8f);
}

void UAuracronPCGSplinePointDistributorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Spline;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGSplinePointDistributorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    FPCGPinProperties& DebugPin = OutputPins.Emplace_GetRef();
    DebugPin.Label = TEXT("Debug Info");
    DebugPin.AllowedTypes = EPCGDataType::Attribute;
    DebugPin.bAdvancedPin = true;
}

FAuracronPCGElementResult FAuracronPCGSplinePointDistributorElement::ProcessData(const FPCGDataCollection& InputData,
                                                                                 FPCGDataCollection& OutputData,
                                                                                 const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGSplinePointDistributorSettings* Settings = GetTypedSettings<UAuracronPCGSplinePointDistributorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Spline Point Distributor");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 PointsDistributed = 0;
        TArray<UPCGPointData*> DistributedPointData;

        // Process all input spline data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSplineData* InputSplineData = Cast<UPCGSplineData>(TaggedData.Data);
            if (!InputSplineData)
            {
                continue;
            }

            // Get spline component from spline data
            USplineComponent* SplineComponent = GetSplineComponentFromData(InputSplineData);
            if (!SplineComponent)
            {
                continue;
            }

            // Distribute points along spline
            UPCGPointData* PointData = DistributePointsAlongSpline(SplineComponent, Settings, PointsDistributed);
            if (PointData)
            {
                DistributedPointData.Add(PointData);
            }

            TotalProcessed++;
        }

        // Create output data
        for (UPCGPointData* PointData : DistributedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Spline Point Distributor processed %d splines and distributed %d points"),
                                  TotalProcessed, PointsDistributed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Spline Point Distributor error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

USplineComponent* FAuracronPCGSplinePointDistributorElement::GetSplineComponentFromData(const UPCGSplineData* SplineData) const
{
    // Extract spline component from PCG spline data
    // Simplified implementation - in production you'd properly extract the component
    return nullptr;
}

UPCGPointData* FAuracronPCGSplinePointDistributorElement::DistributePointsAlongSpline(USplineComponent* SplineComponent,
                                                                                      const UAuracronPCGSplinePointDistributorSettings* Settings,
                                                                                      int32& OutPointsDistributed) const
{
    if (!SplineComponent)
    {
        return nullptr;
    }

    UPCGPointData* PointData = NewObject<UPCGPointData>();
    TArray<FPCGPoint> DistributedPoints;

    // Calculate point count or spacing
    int32 ActualPointCount = Settings->PointCount;
    float SplineLength = UAuracronPCGSplineSystemUtils::CalculateSplineLength(SplineComponent);

    if (!Settings->bUsePointCountOverSpacing && Settings->PointSpacing > 0.0f)
    {
        ActualPointCount = FMath::CeilToInt(SplineLength / Settings->PointSpacing);
    }

    // Distribute points based on mode
    TArray<float> DistanceParameters;
    GenerateDistanceParameters(DistanceParameters, ActualPointCount, SplineLength, Settings);

    // Create points at calculated distances
    for (float Distance : DistanceParameters)
    {
        FPCGPoint Point;

        // Get transform at distance
        FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        FRotator Rotation = SplineComponent->GetRotationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        FVector Scale = SplineComponent->GetScaleAtDistanceAlongSpline(Distance);

        Point.Transform = FTransform(Rotation, Location, Scale);

        // Apply alignment
        if (Settings->bAlignToSpline)
        {
            FVector Direction = SplineComponent->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FRotator AlignedRotation = FRotationMatrix::MakeFromX(Direction).Rotator();
            Point.Transform.SetRotation(AlignedRotation.Quaternion());
        }

        // Apply offset
        ApplyPointOffset(Point, Settings);

        // Project to surface if requested
        if (Settings->bProjectToSurface)
        {
            ProjectPointToSurface(Point, Settings);
        }

        // Set point properties
        SetPointProperties(Point, SplineComponent, Distance, Settings);

        DistributedPoints.Add(Point);
        OutPointsDistributed++;
    }

    // Set points to point data
    PointData->GetMutablePoints() = DistributedPoints;

    return PointData;
}

void FAuracronPCGSplinePointDistributorElement::GenerateDistanceParameters(TArray<float>& OutDistances,
                                                                           int32 PointCount,
                                                                           float SplineLength,
                                                                           const UAuracronPCGSplinePointDistributorSettings* Settings) const
{
    OutDistances.Empty();
    OutDistances.Reserve(PointCount);

    switch (Settings->DistributionMode)
    {
        case EAuracronPCGSplineDistributionMode::Uniform:
            GenerateUniformDistances(OutDistances, PointCount, SplineLength, Settings);
            break;
        case EAuracronPCGSplineDistributionMode::Random:
            GenerateRandomDistances(OutDistances, PointCount, SplineLength, Settings);
            break;
        case EAuracronPCGSplineDistributionMode::Noise:
            GenerateNoiseDistances(OutDistances, PointCount, SplineLength, Settings);
            break;
        case EAuracronPCGSplineDistributionMode::Curve:
            GenerateCurveDistances(OutDistances, PointCount, SplineLength, Settings);
            break;
        default:
            GenerateUniformDistances(OutDistances, PointCount, SplineLength, Settings);
            break;
    }
}

void FAuracronPCGSplinePointDistributorElement::GenerateUniformDistances(TArray<float>& OutDistances,
                                                                         int32 PointCount,
                                                                         float SplineLength,
                                                                         const UAuracronPCGSplinePointDistributorSettings* Settings) const
{
    if (PointCount <= 1)
    {
        if (PointCount == 1)
        {
            OutDistances.Add(SplineLength * 0.5f);
        }
        return;
    }

    int32 ActualPointCount = PointCount;
    int32 StartOffset = Settings->bIncludeSplineStart ? 0 : 1;
    int32 EndOffset = Settings->bIncludeSplineEnd ? 0 : 1;

    if (!Settings->bIncludeSplineStart && !Settings->bIncludeSplineEnd)
    {
        ActualPointCount += 2; // Add extra points to maintain spacing
    }

    for (int32 i = StartOffset; i < ActualPointCount - EndOffset; i++)
    {
        float T = static_cast<float>(i) / FMath::Max(1, ActualPointCount - 1);
        OutDistances.Add(T * SplineLength);
    }
}

void FAuracronPCGSplinePointDistributorElement::GenerateRandomDistances(TArray<float>& OutDistances,
                                                                        int32 PointCount,
                                                                        float SplineLength,
                                                                        const UAuracronPCGSplinePointDistributorSettings* Settings) const
{
    FRandomStream RandomStream(Settings->RandomSeed);

    for (int32 i = 0; i < PointCount; i++)
    {
        float BaseT = static_cast<float>(i) / FMath::Max(1, PointCount - 1);
        float Variation = RandomStream.FRandRange(-Settings->RandomVariation, Settings->RandomVariation);
        float T = FMath::Clamp(BaseT + Variation, 0.0f, 1.0f);
        OutDistances.Add(T * SplineLength);
    }

    // Sort distances
    OutDistances.Sort();
}

void FAuracronPCGSplinePointDistributorElement::GenerateNoiseDistances(TArray<float>& OutDistances,
                                                                       int32 PointCount,
                                                                       float SplineLength,
                                                                       const UAuracronPCGSplinePointDistributorSettings* Settings) const
{
    for (int32 i = 0; i < PointCount; i++)
    {
        float BaseT = static_cast<float>(i) / FMath::Max(1, PointCount - 1);

        // Generate noise value
        float NoiseValue = 0.0f;
        for (int32 Octave = 0; Octave < Settings->NoiseOctaves; Octave++)
        {
            float Frequency = Settings->NoiseScale * FMath::Pow(2.0f, Octave);
            float Amplitude = FMath::Pow(0.5f, Octave);
            NoiseValue += FMath::PerlinNoise1D(BaseT * Frequency) * Amplitude;
        }

        float Offset = NoiseValue * Settings->NoiseStrength;
        float T = FMath::Clamp(BaseT + Offset, 0.0f, 1.0f);
        OutDistances.Add(T * SplineLength);
    }

    // Sort distances
    OutDistances.Sort();
}

void FAuracronPCGSplinePointDistributorElement::GenerateCurveDistances(TArray<float>& OutDistances,
                                                                       int32 PointCount,
                                                                       float SplineLength,
                                                                       const UAuracronPCGSplinePointDistributorSettings* Settings) const
{
    UCurveFloat* DistributionCurve = Settings->DistributionCurve.LoadSynchronous();
    if (!DistributionCurve)
    {
        // Fallback to uniform distribution
        GenerateUniformDistances(OutDistances, PointCount, SplineLength, Settings);
        return;
    }

    for (int32 i = 0; i < PointCount; i++)
    {
        float BaseT = static_cast<float>(i) / FMath::Max(1, PointCount - 1);
        float CurveValue = DistributionCurve->GetFloatValue(BaseT);
        float T = FMath::Clamp(CurveValue, 0.0f, 1.0f);
        OutDistances.Add(T * SplineLength);
    }

    // Sort distances
    OutDistances.Sort();
}

void FAuracronPCGSplinePointDistributorElement::ApplyPointOffset(FPCGPoint& Point, const UAuracronPCGSplinePointDistributorSettings* Settings) const
{
    FVector Offset = Settings->PointOffset;

    if (Settings->bRandomizeOffset)
    {
        FRandomStream RandomStream(Point.Seed);
        FVector RandomOffset = FVector(
            RandomStream.FRandRange(-Settings->OffsetVariation.X, Settings->OffsetVariation.X),
            RandomStream.FRandRange(-Settings->OffsetVariation.Y, Settings->OffsetVariation.Y),
            RandomStream.FRandRange(-Settings->OffsetVariation.Z, Settings->OffsetVariation.Z)
        );
        Offset += RandomOffset;
    }

    Point.Transform.AddToTranslation(Point.Transform.TransformVector(Offset));
}

void FAuracronPCGSplinePointDistributorElement::ProjectPointToSurface(FPCGPoint& Point, const UAuracronPCGSplinePointDistributorSettings* Settings) const
{
    // Simplified surface projection - in production you'd use proper collision/landscape sampling
    FVector StartLocation = Point.Transform.GetLocation() + FVector(0.0f, 0.0f, Settings->ProjectionDistance * 0.5f);
    FVector EndLocation = Point.Transform.GetLocation() - FVector(0.0f, 0.0f, Settings->ProjectionDistance * 0.5f);

    // Perform line trace (simplified)
    // In production, you'd use proper collision detection
    FVector ProjectedLocation = Point.Transform.GetLocation();
    ProjectedLocation.Z = 0.0f; // Simplified projection to Z=0

    Point.Transform.SetLocation(ProjectedLocation);
}

void FAuracronPCGSplinePointDistributorElement::SetPointProperties(FPCGPoint& Point,
                                                                   USplineComponent* SplineComponent,
                                                                   float Distance,
                                                                   const UAuracronPCGSplinePointDistributorSettings* Settings) const
{
    // Set point density
    Point.Density = 1.0f;

    // Set point scale based on spline width if requested
    if (Settings->bUseSplineWidth)
    {
        float Width = UAuracronPCGSplineSystemUtils::GetSplineWidthAtDistance(SplineComponent, Distance);
        Point.Transform.SetScale3D(FVector(Width / 100.0f)); // Normalize to reasonable scale
    }

    // Set metadata attributes
    // Simplified implementation - in production you'd properly handle metadata
}
