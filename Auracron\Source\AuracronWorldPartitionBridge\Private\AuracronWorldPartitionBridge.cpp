// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - World Partition Bridge Implementation
// Production-ready implementation file for UE5.6 World Partition API bridge

#include "AuracronWorldPartitionBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Modules/ModuleManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Misc/Guid.h"
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "HAL/ThreadSafeBool.h"
#include "ProfilingDebugging/ScopedTimers.h"
#include "Stats/Stats.h"

// World Partition includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/WorldPartitionRuntimeHash.h"
#include "WorldPartition/WorldPartitionEditorHash.h"
#include "WorldPartition/WorldPartitionActorDesc.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/WorldPartitionHelpers.h"
#include "WorldPartition/WorldPartitionLog.h"
#include "WorldPartition/WorldPartitionMiniMap.h"
#include "WorldPartition/WorldPartitionMiniMapHelper.h"

// Data Layer includes
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"

// HLOD includes
#include "WorldPartition/HLOD/HLODLayer.h"
#include "WorldPartition/HLOD/HLODActor.h"
#include "WorldPartition/HLOD/HLODSubsystem.h"
#include "WorldPartition/HLOD/HLODBuilder.h"

// Level Instance includes
#include "LevelInstance/LevelInstanceActor.h"
#include "LevelInstance/LevelInstanceSubsystem.h"

// Editor includes
#if WITH_EDITOR
#include "WorldPartition/WorldPartitionEditorModule.h"
#include "WorldPartition/WorldPartitionEditorPerProjectUserSettings.h"
#include "WorldPartition/WorldPartitionEditorSettings.h"
#include "WorldPartition/WorldPartitionEditorSpatialHash.h"
#include "WorldPartition/WorldPartitionRuntimeSpatialHash.h"
#include "WorldPartition/WorldPartitionLevelStreamingDynamic.h"
#include "Editor.h"
#include "EditorModeManager.h"
#include "LevelEditor.h"
#include "Toolkits/ToolkitManager.h"
#include "Framework/Application/SlateApplication.h"
#include "Widgets/Docking/SDockTab.h"
#include "WorkspaceMenuStructure.h"
#include "WorkspaceMenuStructureModule.h"
#endif

DEFINE_LOG_CATEGORY(LogAuracronWorldPartitionBridge);

// Static member initialization
UAuracronWorldPartitionBridgeAPI* FAuracronWorldPartitionBridgeModule::APIInstance = nullptr;

// Constructor
UAuracronWorldPartitionBridgeAPI::UAuracronWorldPartitionBridgeAPI()
    : bIsInitialized(false)
{
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("AuracronWorldPartitionBridgeAPI Constructor"));
}

// Destructor
UAuracronWorldPartitionBridgeAPI::~UAuracronWorldPartitionBridgeAPI()
{
    if (bIsInitialized)
    {
        ShutdownWorldPartitionBridge();
    }
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("AuracronWorldPartitionBridgeAPI Destructor"));
}

// Initialization and cleanup
bool UAuracronWorldPartitionBridgeAPI::InitializeWorldPartitionBridge()
{
    FScopeLock Lock(&CriticalSection);
    
    if (bIsInitialized)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Warning, TEXT("World Partition Bridge already initialized"));
        return true;
    }

    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Initializing World Partition Bridge..."));

    try
    {
        // Clear any existing state
        ClearCachedReferences();
        StreamingSources.Empty();
        DataLayers.Empty();
        HLODActors.Empty();
        LevelInstances.Empty();
        AsyncOperations.Empty();
        OperationProgress.Empty();
        PerformanceMetrics.Empty();
        ErrorMessages.Empty();
        WarningMessages.Empty();

        // Load configuration
        LoadConfiguration();

        // Setup event handlers
        if (UWorldPartitionSubsystem* WorldPartitionSubsystem = UWorld::GetSubsystem<UWorldPartitionSubsystem>(GWorld))
        {
            // Bind to world partition events
            WorldPartitionSubsystem->OnWorldPartitionInitialized.AddUObject(this, &UAuracronWorldPartitionBridgeAPI::OnWorldPartitionInitialized);
            WorldPartitionSubsystem->OnWorldPartitionUninitialized.AddUObject(this, &UAuracronWorldPartitionBridgeAPI::OnWorldPartitionUninitialized);
        }

        bIsInitialized = true;
        UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition Bridge initialized successfully"));
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to initialize World Partition Bridge: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
        return false;
    }
}

void UAuracronWorldPartitionBridgeAPI::ShutdownWorldPartitionBridge()
{
    FScopeLock Lock(&CriticalSection);
    
    if (!bIsInitialized)
    {
        return;
    }

    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Shutting down World Partition Bridge..."));

    try
    {
        // Complete any pending async operations
        for (auto& Operation : AsyncOperations)
        {
            if (Operation.Value.IsValid())
            {
                Operation.Value->Trigger();
            }
        }

        // Unbind event handlers
        if (UWorldPartitionSubsystem* WorldPartitionSubsystem = UWorld::GetSubsystem<UWorldPartitionSubsystem>(GWorld))
        {
            WorldPartitionSubsystem->OnWorldPartitionInitialized.RemoveAll(this);
            WorldPartitionSubsystem->OnWorldPartitionUninitialized.RemoveAll(this);
        }

        // Clear all cached data
        ClearCachedReferences();
        StreamingSources.Empty();
        DataLayers.Empty();
        HLODActors.Empty();
        LevelInstances.Empty();
        AsyncOperations.Empty();
        OperationProgress.Empty();
        PerformanceMetrics.Empty();
        ErrorMessages.Empty();
        WarningMessages.Empty();

        // Save configuration
        SaveConfiguration();

        bIsInitialized = false;
        UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition Bridge shutdown complete"));
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Error during World Partition Bridge shutdown: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
    }
}

bool UAuracronWorldPartitionBridgeAPI::IsWorldPartitionBridgeInitialized() const
{
    FScopeLock Lock(&CriticalSection);
    return bIsInitialized;
}

// World Partition Management
bool UAuracronWorldPartitionBridgeAPI::EnableWorldPartition(UWorld* World)
{
    if (!ValidateWorld(World))
    {
        return false;
    }

    StartPerformanceTimer(TEXT("EnableWorldPartition"));

    try
    {
        // Check if World Partition is already enabled
        if (UWorldPartition* WorldPartition = World->GetWorldPartition())
        {
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition already enabled for world: %s"), *World->GetName());
            EndPerformanceTimer(TEXT("EnableWorldPartition"));
            return true;
        }

#if WITH_EDITOR
        // Enable World Partition in editor
        if (GIsEditor)
        {
            // Create World Partition object
            UWorldPartition* NewWorldPartition = NewObject<UWorldPartition>(World);
            if (!NewWorldPartition)
            {
                LogError(TEXT("Failed to create World Partition object"));
                EndPerformanceTimer(TEXT("EnableWorldPartition"));
                return false;
            }

            // Initialize World Partition
            NewWorldPartition->Initialize(World, FTransform::Identity);
            
            // Set World Partition on the world
            World->SetWorldPartition(NewWorldPartition);
            
            // Update cached references
            UpdateCachedReferences(World);
            
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition enabled for world: %s"), *World->GetName());
            EndPerformanceTimer(TEXT("EnableWorldPartition"));
            return true;
        }
#endif

        LogError(TEXT("World Partition can only be enabled in editor mode"));
        EndPerformanceTimer(TEXT("EnableWorldPartition"));
        return false;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to enable World Partition: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
        EndPerformanceTimer(TEXT("EnableWorldPartition"));
        return false;
    }
}

bool UAuracronWorldPartitionBridgeAPI::DisableWorldPartition(UWorld* World)
{
    if (!ValidateWorld(World))
    {
        return false;
    }

    StartPerformanceTimer(TEXT("DisableWorldPartition"));

    try
    {
        UWorldPartition* WorldPartition = World->GetWorldPartition();
        if (!WorldPartition)
        {
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition already disabled for world: %s"), *World->GetName());
            EndPerformanceTimer(TEXT("DisableWorldPartition"));
            return true;
        }

#if WITH_EDITOR
        if (GIsEditor)
        {
            // Uninitialize World Partition
            WorldPartition->Uninitialize();
            
            // Remove World Partition from world
            World->SetWorldPartition(nullptr);
            
            // Clear cached references
            ClearCachedReferences();
            
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition disabled for world: %s"), *World->GetName());
            EndPerformanceTimer(TEXT("DisableWorldPartition"));
            return true;
        }
#endif

        LogError(TEXT("World Partition can only be disabled in editor mode"));
        EndPerformanceTimer(TEXT("DisableWorldPartition"));
        return false;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to disable World Partition: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
        EndPerformanceTimer(TEXT("DisableWorldPartition"));
        return false;
    }
}

bool UAuracronWorldPartitionBridgeAPI::IsWorldPartitionEnabled(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return false;
    }

    return World->GetWorldPartition() != nullptr;
}

UWorldPartition* UAuracronWorldPartitionBridgeAPI::GetWorldPartition(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return nullptr;
    }

    return World->GetWorldPartition();
}

UWorldPartitionSubsystem* UAuracronWorldPartitionBridgeAPI::GetWorldPartitionSubsystem(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return nullptr;
    }

    return UWorld::GetSubsystem<UWorldPartitionSubsystem>(World);
}

// Streaming Management
bool UAuracronWorldPartitionBridgeAPI::EnableStreaming(UWorld* World, bool bEnable)
{
    if (!ValidateWorldPartitionEnabled(World))
    {
        return false;
    }

    StartPerformanceTimer(TEXT("EnableStreaming"));

    try
    {
        UWorldPartition* WorldPartition = GetValidWorldPartition(World);
        if (!WorldPartition)
        {
            LogError(TEXT("Failed to get valid World Partition"));
            EndPerformanceTimer(TEXT("EnableStreaming"));
            return false;
        }

        // Enable/disable streaming
        WorldPartition->SetEnableStreaming(bEnable);

        UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition streaming %s for world: %s"),
               bEnable ? TEXT("enabled") : TEXT("disabled"), *World->GetName());

        EndPerformanceTimer(TEXT("EnableStreaming"));
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to %s streaming: %s"),
                                         bEnable ? TEXT("enable") : TEXT("disable"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
        EndPerformanceTimer(TEXT("EnableStreaming"));
        return false;
    }
}

bool UAuracronWorldPartitionBridgeAPI::IsStreamingEnabled(UWorld* World) const
{
    if (!ValidateWorldPartitionEnabled(World))
    {
        return false;
    }

    UWorldPartition* WorldPartition = GetValidWorldPartition(World);
    if (!WorldPartition)
    {
        return false;
    }

    return WorldPartition->IsStreamingEnabled();
}

bool UAuracronWorldPartitionBridgeAPI::LoadCellsAtLocation(UWorld* World, const FVector& Location, float Radius)
{
    if (!ValidateWorldPartitionEnabled(World))
    {
        return false;
    }

    StartPerformanceTimer(TEXT("LoadCellsAtLocation"));

    try
    {
        UWorldPartitionSubsystem* WorldPartitionSubsystem = GetValidWorldPartitionSubsystem(World);
        if (!WorldPartitionSubsystem)
        {
            LogError(TEXT("Failed to get valid World Partition Subsystem"));
            EndPerformanceTimer(TEXT("LoadCellsAtLocation"));
            return false;
        }

        // Create a temporary streaming source at the location
        FString OperationId = GenerateUniqueOperationId();
        StartAsyncOperation(OperationId);

        // Use the World Partition streaming system to load cells
        FWorldPartitionStreamingQuerySource QuerySource;
        QuerySource.bSpatialQuery = true;
        QuerySource.Location = Location;
        QuerySource.Radius = Radius;
        QuerySource.bUseGridLoadingRange = false;
        QuerySource.LoadingRange = Radius;
        QuerySource.bDataLayersOnly = false;
        QuerySource.DataLayers.Empty();

        // Execute the streaming query
        bool bSuccess = WorldPartitionSubsystem->IsStreamingCompleted(QuerySource);

        if (bSuccess)
        {
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Successfully initiated cell loading at location: %s with radius: %f"),
                   *Location.ToString(), Radius);
        }
        else
        {
            LogError(FString::Printf(TEXT("Failed to load cells at location: %s"), *Location.ToString()));
        }

        CompleteAsyncOperation(OperationId, bSuccess);
        EndPerformanceTimer(TEXT("LoadCellsAtLocation"));
        return bSuccess;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to load cells at location: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
        EndPerformanceTimer(TEXT("LoadCellsAtLocation"));
        return false;
    }
}

bool UAuracronWorldPartitionBridgeAPI::UnloadCellsAtLocation(UWorld* World, const FVector& Location, float Radius)
{
    if (!ValidateWorldPartitionEnabled(World))
    {
        return false;
    }

    StartPerformanceTimer(TEXT("UnloadCellsAtLocation"));

    try
    {
        UWorldPartitionSubsystem* WorldPartitionSubsystem = GetValidWorldPartitionSubsystem(World);
        if (!WorldPartitionSubsystem)
        {
            LogError(TEXT("Failed to get valid World Partition Subsystem"));
            EndPerformanceTimer(TEXT("UnloadCellsAtLocation"));
            return false;
        }

        // Get all loaded cells in the area
        UWorldPartition* WorldPartition = GetValidWorldPartition(World);
        if (!WorldPartition)
        {
            LogError(TEXT("Failed to get valid World Partition"));
            EndPerformanceTimer(TEXT("UnloadCellsAtLocation"));
            return false;
        }

        FString OperationId = GenerateUniqueOperationId();
        StartAsyncOperation(OperationId);

        // Find cells in the specified area and unload them
        FBox QueryBounds(Location - FVector(Radius), Location + FVector(Radius));

        // Use World Partition runtime hash to find cells
        if (UWorldPartitionRuntimeHash* RuntimeHash = WorldPartition->GetRuntimeHash())
        {
            // Get cells in bounds
            TArray<const UWorldPartitionRuntimeCell*> CellsInBounds;
            RuntimeHash->ForEachStreamingCells([&](const UWorldPartitionRuntimeCell* Cell)
            {
                if (Cell && QueryBounds.Intersect(Cell->GetContentBounds()))
                {
                    CellsInBounds.Add(Cell);
                }
                return true;
            });

            // Unload the cells
            for (const UWorldPartitionRuntimeCell* Cell : CellsInBounds)
            {
                if (Cell->GetCurrentState() != EWorldPartitionRuntimeCellState::Unloaded)
                {
                    // Request unload
                    const_cast<UWorldPartitionRuntimeCell*>(Cell)->SetIsAlwaysLoaded(false);
                }
            }

            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Successfully initiated unloading of %d cells at location: %s"),
                   CellsInBounds.Num(), *Location.ToString());
        }

        CompleteAsyncOperation(OperationId, true);
        EndPerformanceTimer(TEXT("UnloadCellsAtLocation"));
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to unload cells at location: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
        EndPerformanceTimer(TEXT("UnloadCellsAtLocation"));
        return false;
    }
}

bool UAuracronWorldPartitionBridgeAPI::LoadCellsByName(UWorld* World, const TArray<FString>& CellNames)
{
    if (!ValidateWorldPartitionEnabled(World))
    {
        return false;
    }

    StartPerformanceTimer(TEXT("LoadCellsByName"));

    try
    {
        UWorldPartition* WorldPartition = GetValidWorldPartition(World);
        if (!WorldPartition)
        {
            LogError(TEXT("Failed to get valid World Partition"));
            EndPerformanceTimer(TEXT("LoadCellsByName"));
            return false;
        }

        FString OperationId = GenerateUniqueOperationId();
        StartAsyncOperation(OperationId);

        int32 LoadedCount = 0;
        UWorldPartitionRuntimeHash* RuntimeHash = WorldPartition->GetRuntimeHash();
        if (!RuntimeHash)
        {
            LogError(TEXT("Failed to get World Partition Runtime Hash"));
            CompleteAsyncOperation(OperationId, false);
            EndPerformanceTimer(TEXT("LoadCellsByName"));
            return false;
        }

        // Load each cell by name
        for (const FString& CellName : CellNames)
        {
            if (!IsValidCellName(CellName))
            {
                LogWarning(FString::Printf(TEXT("Invalid cell name: %s"), *CellName));
                continue;
            }

            // Find the cell by name
            const UWorldPartitionRuntimeCell* FoundCell = nullptr;
            RuntimeHash->ForEachStreamingCells([&](const UWorldPartitionRuntimeCell* Cell)
            {
                if (Cell && Cell->GetName() == CellName)
                {
                    FoundCell = Cell;
                    return false; // Stop iteration
                }
                return true; // Continue iteration
            });

            if (FoundCell)
            {
                // Load the cell
                const_cast<UWorldPartitionRuntimeCell*>(FoundCell)->SetIsAlwaysLoaded(true);
                LoadedCount++;
                UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Initiated loading for cell: %s"), *CellName);
            }
            else
            {
                LogWarning(FString::Printf(TEXT("Cell not found: %s"), *CellName));
            }
        }

        bool bSuccess = LoadedCount > 0;
        UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Successfully initiated loading for %d out of %d cells"),
               LoadedCount, CellNames.Num());

        CompleteAsyncOperation(OperationId, bSuccess);
        EndPerformanceTimer(TEXT("LoadCellsByName"));
        return bSuccess;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to load cells by name: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
        EndPerformanceTimer(TEXT("LoadCellsByName"));
        return false;
    }
}

bool UAuracronWorldPartitionBridgeAPI::UnloadCellsByName(UWorld* World, const TArray<FString>& CellNames)
{
    if (!ValidateWorldPartitionEnabled(World))
    {
        return false;
    }

    StartPerformanceTimer(TEXT("UnloadCellsByName"));

    try
    {
        UWorldPartition* WorldPartition = GetValidWorldPartition(World);
        if (!WorldPartition)
        {
            LogError(TEXT("Failed to get valid World Partition"));
            EndPerformanceTimer(TEXT("UnloadCellsByName"));
            return false;
        }

        FString OperationId = GenerateUniqueOperationId();
        StartAsyncOperation(OperationId);

        int32 UnloadedCount = 0;
        UWorldPartitionRuntimeHash* RuntimeHash = WorldPartition->GetRuntimeHash();
        if (!RuntimeHash)
        {
            LogError(TEXT("Failed to get World Partition Runtime Hash"));
            CompleteAsyncOperation(OperationId, false);
            EndPerformanceTimer(TEXT("UnloadCellsByName"));
            return false;
        }

        // Unload each cell by name
        for (const FString& CellName : CellNames)
        {
            if (!IsValidCellName(CellName))
            {
                LogWarning(FString::Printf(TEXT("Invalid cell name: %s"), *CellName));
                continue;
            }

            // Find the cell by name
            const UWorldPartitionRuntimeCell* FoundCell = nullptr;
            RuntimeHash->ForEachStreamingCells([&](const UWorldPartitionRuntimeCell* Cell)
            {
                if (Cell && Cell->GetName() == CellName)
                {
                    FoundCell = Cell;
                    return false; // Stop iteration
                }
                return true; // Continue iteration
            });

            if (FoundCell)
            {
                // Unload the cell
                const_cast<UWorldPartitionRuntimeCell*>(FoundCell)->SetIsAlwaysLoaded(false);
                UnloadedCount++;
                UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Initiated unloading for cell: %s"), *CellName);
            }
            else
            {
                LogWarning(FString::Printf(TEXT("Cell not found: %s"), *CellName));
            }
        }

        bool bSuccess = UnloadedCount > 0;
        UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Successfully initiated unloading for %d out of %d cells"),
               UnloadedCount, CellNames.Num());

        CompleteAsyncOperation(OperationId, bSuccess);
        EndPerformanceTimer(TEXT("UnloadCellsByName"));
        return bSuccess;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to unload cells by name: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
        EndPerformanceTimer(TEXT("UnloadCellsByName"));
        return false;
    }
}

bool UAuracronWorldPartitionBridgeAPI::IsStreamingCompleted(UWorld* World) const
{
    if (!ValidateWorldPartitionEnabled(World))
    {
        return false;
    }

    UWorldPartitionSubsystem* WorldPartitionSubsystem = GetValidWorldPartitionSubsystem(World);
    if (!WorldPartitionSubsystem)
    {
        return false;
    }

    return WorldPartitionSubsystem->IsStreamingCompleted();
}

float UAuracronWorldPartitionBridgeAPI::GetStreamingProgress(UWorld* World) const
{
    if (!ValidateWorldPartitionEnabled(World))
    {
        return 0.0f;
    }

    UWorldPartitionSubsystem* WorldPartitionSubsystem = GetValidWorldPartitionSubsystem(World);
    if (!WorldPartitionSubsystem)
    {
        return 0.0f;
    }

    // Calculate streaming progress based on loaded vs total cells
    UWorldPartition* WorldPartition = GetValidWorldPartition(World);
    if (!WorldPartition)
    {
        return 0.0f;
    }

    UWorldPartitionRuntimeHash* RuntimeHash = WorldPartition->GetRuntimeHash();
    if (!RuntimeHash)
    {
        return 0.0f;
    }

    int32 TotalCells = 0;
    int32 LoadedCells = 0;

    RuntimeHash->ForEachStreamingCells([&](const UWorldPartitionRuntimeCell* Cell)
    {
        if (Cell)
        {
            TotalCells++;
            if (Cell->GetCurrentState() == EWorldPartitionRuntimeCellState::Activated ||
                Cell->GetCurrentState() == EWorldPartitionRuntimeCellState::Loaded)
            {
                LoadedCells++;
            }
        }
        return true;
    });

    return TotalCells > 0 ? static_cast<float>(LoadedCells) / static_cast<float>(TotalCells) : 1.0f;
}

// Helper Functions Implementation
UWorldPartition* UAuracronWorldPartitionBridgeAPI::GetValidWorldPartition(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return nullptr;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        LogError(TEXT("World Partition not found or not enabled"));
        return nullptr;
    }

    return WorldPartition;
}

UWorldPartitionSubsystem* UAuracronWorldPartitionBridgeAPI::GetValidWorldPartitionSubsystem(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return nullptr;
    }

    UWorldPartitionSubsystem* Subsystem = UWorld::GetSubsystem<UWorldPartitionSubsystem>(World);
    if (!Subsystem)
    {
        LogError(TEXT("World Partition Subsystem not found"));
        return nullptr;
    }

    return Subsystem;
}

UDataLayerSubsystem* UAuracronWorldPartitionBridgeAPI::GetDataLayerSubsystem(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return nullptr;
    }

    return UWorld::GetSubsystem<UDataLayerSubsystem>(World);
}

UHLODSubsystem* UAuracronWorldPartitionBridgeAPI::GetHLODSubsystem(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return nullptr;
    }

    return UWorld::GetSubsystem<UHLODSubsystem>(World);
}

ULevelInstanceSubsystem* UAuracronWorldPartitionBridgeAPI::GetLevelInstanceSubsystem(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return nullptr;
    }

    return UWorld::GetSubsystem<ULevelInstanceSubsystem>(World);
}

void UAuracronWorldPartitionBridgeAPI::LogError(const FString& ErrorMessage) const
{
    UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("%s"), *ErrorMessage);
    const_cast<UAuracronWorldPartitionBridgeAPI*>(this)->ErrorMessages.Add(ErrorMessage);
}

void UAuracronWorldPartitionBridgeAPI::LogWarning(const FString& WarningMessage) const
{
    UE_LOG(LogAuracronWorldPartitionBridge, Warning, TEXT("%s"), *WarningMessage);
    const_cast<UAuracronWorldPartitionBridgeAPI*>(this)->WarningMessages.Add(WarningMessage);
}

void UAuracronWorldPartitionBridgeAPI::LogInfo(const FString& InfoMessage) const
{
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("%s"), *InfoMessage);
}

bool UAuracronWorldPartitionBridgeAPI::ValidateWorld(UWorld* World) const
{
    if (!World)
    {
        LogError(TEXT("Invalid world parameter"));
        return false;
    }

    if (!World->IsValidLowLevel())
    {
        LogError(TEXT("World is not valid"));
        return false;
    }

    return true;
}

bool UAuracronWorldPartitionBridgeAPI::ValidateWorldPartitionEnabled(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return false;
    }

    if (!World->GetWorldPartition())
    {
        LogError(TEXT("World Partition is not enabled for this world"));
        return false;
    }

    return true;
}

void UAuracronWorldPartitionBridgeAPI::UpdateCachedReferences(UWorld* World)
{
    if (!ValidateWorld(World))
    {
        return;
    }

    CachedWorld = World;
    CachedWorldPartition = World->GetWorldPartition();
    CachedWorldPartitionSubsystem = UWorld::GetSubsystem<UWorldPartitionSubsystem>(World);
}

void UAuracronWorldPartitionBridgeAPI::ClearCachedReferences()
{
    CachedWorld.Reset();
    CachedWorldPartition.Reset();
    CachedWorldPartitionSubsystem.Reset();
}

void UAuracronWorldPartitionBridgeAPI::StartAsyncOperation(const FString& OperationName)
{
    TSharedPtr<FEvent> Event = MakeShareable(FPlatformProcess::GetSynchEventFromPool(false));
    AsyncOperations.Add(OperationName, Event);
    OperationProgress.Add(OperationName, 0.0f);
}

void UAuracronWorldPartitionBridgeAPI::CompleteAsyncOperation(const FString& OperationName, bool bSuccess)
{
    if (TSharedPtr<FEvent>* EventPtr = AsyncOperations.Find(OperationName))
    {
        if (EventPtr->IsValid())
        {
            (*EventPtr)->Trigger();
        }
    }

    AsyncOperations.Remove(OperationName);
    OperationProgress.Remove(OperationName);
}

void UAuracronWorldPartitionBridgeAPI::UpdateAsyncOperationProgress(const FString& OperationName, float Progress)
{
    if (float* ProgressPtr = OperationProgress.Find(OperationName))
    {
        *ProgressPtr = FMath::Clamp(Progress, 0.0f, 1.0f);
    }
}

void UAuracronWorldPartitionBridgeAPI::OnWorldPartitionInitialized(UWorldPartition* WorldPartition)
{
    if (WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition initialized: %s"), *WorldPartition->GetName());
        UpdateCachedReferences(WorldPartition->GetWorld());
    }
}

void UAuracronWorldPartitionBridgeAPI::OnWorldPartitionUninitialized(UWorldPartition* WorldPartition)
{
    if (WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("World Partition uninitialized: %s"), *WorldPartition->GetName());
        ClearCachedReferences();
    }
}

void UAuracronWorldPartitionBridgeAPI::OnCellLoadingStateChanged(const UWorldPartitionRuntimeCell* Cell, EWorldPartitionRuntimeCellState OldState, EWorldPartitionRuntimeCellState NewState)
{
    if (Cell)
    {
        FString CellName = Cell->GetName();
        bool bLoaded = (NewState == EWorldPartitionRuntimeCellState::Loaded || NewState == EWorldPartitionRuntimeCellState::Activated);

        if (bLoaded && (OldState == EWorldPartitionRuntimeCellState::Unloaded || OldState == EWorldPartitionRuntimeCellState::Loading))
        {
            OnCellLoaded.Broadcast(CellName, true);
        }
        else if (!bLoaded && (OldState == EWorldPartitionRuntimeCellState::Loaded || OldState == EWorldPartitionRuntimeCellState::Activated))
        {
            OnCellUnloaded.Broadcast(CellName, true);
        }
    }
}

void UAuracronWorldPartitionBridgeAPI::OnDataLayerRuntimeStateChanged(const UDataLayerInstance* DataLayer, EDataLayerRuntimeState OldState, EDataLayerRuntimeState NewState)
{
    if (DataLayer)
    {
        FString LayerName = DataLayer->GetDataLayerFName().ToString();
        bool bLoaded = (NewState == EDataLayerRuntimeState::Activated);
        OnDataLayerChanged.Broadcast(LayerName, bLoaded);
    }
}

void UAuracronWorldPartitionBridgeAPI::StartPerformanceTimer(const FString& TimerName)
{
    PerformanceMetrics.Add(TimerName, FPlatformTime::Seconds());
}

void UAuracronWorldPartitionBridgeAPI::EndPerformanceTimer(const FString& TimerName)
{
    if (double* StartTimePtr = PerformanceMetrics.Find(TimerName))
    {
        double Duration = FPlatformTime::Seconds() - *StartTimePtr;
        *StartTimePtr = Duration;
        UE_LOG(LogAuracronWorldPartitionBridge, VeryVerbose, TEXT("Performance: %s took %f seconds"), *TimerName, Duration);
    }
}

double UAuracronWorldPartitionBridgeAPI::GetPerformanceMetric(const FString& MetricName) const
{
    if (const double* MetricPtr = PerformanceMetrics.Find(MetricName))
    {
        return *MetricPtr;
    }
    return 0.0;
}

void UAuracronWorldPartitionBridgeAPI::CleanupUnusedReferences()
{
    // Clean up streaming sources
    for (auto It = StreamingSources.CreateIterator(); It; ++It)
    {
        if (!It.Value().IsValid())
        {
            It.RemoveCurrent();
        }
    }

    // Clean up data layers
    for (auto It = DataLayers.CreateIterator(); It; ++It)
    {
        if (!It.Value().IsValid())
        {
            It.RemoveCurrent();
        }
    }

    // Clean up HLOD actors
    for (auto It = HLODActors.CreateIterator(); It; ++It)
    {
        if (!It.Value().IsValid())
        {
            It.RemoveCurrent();
        }
    }

    // Clean up level instances
    for (auto It = LevelInstances.CreateIterator(); It; ++It)
    {
        if (!It.Value().IsValid())
        {
            It.RemoveCurrent();
        }
    }
}

void UAuracronWorldPartitionBridgeAPI::OptimizeMemoryUsage()
{
    CleanupUnusedReferences();

    // Shrink containers
    StreamingSources.Shrink();
    DataLayers.Shrink();
    HLODActors.Shrink();
    LevelInstances.Shrink();
    AsyncOperations.Shrink();
    OperationProgress.Shrink();
    PerformanceMetrics.Shrink();
    ErrorMessages.Shrink();
    WarningMessages.Shrink();
}

void UAuracronWorldPartitionBridgeAPI::LockForRead() const
{
    CriticalSection.Lock();
}

void UAuracronWorldPartitionBridgeAPI::LockForWrite() const
{
    CriticalSection.Lock();
}

void UAuracronWorldPartitionBridgeAPI::Unlock() const
{
    CriticalSection.Unlock();
}

bool UAuracronWorldPartitionBridgeAPI::IsValidCellName(const FString& CellName) const
{
    return !CellName.IsEmpty() && CellName.Len() > 0 && !CellName.Contains(TEXT(" "));
}

bool UAuracronWorldPartitionBridgeAPI::IsValidDataLayerName(const FString& LayerName) const
{
    return !LayerName.IsEmpty() && LayerName.Len() > 0;
}

bool UAuracronWorldPartitionBridgeAPI::IsValidStreamingSourceName(const FString& SourceName) const
{
    return !SourceName.IsEmpty() && SourceName.Len() > 0;
}

bool UAuracronWorldPartitionBridgeAPI::IsValidHLODName(const FString& HLODName) const
{
    return !HLODName.IsEmpty() && HLODName.Len() > 0;
}

bool UAuracronWorldPartitionBridgeAPI::IsValidLevelInstanceName(const FString& InstanceName) const
{
    return !InstanceName.IsEmpty() && InstanceName.Len() > 0;
}

EWorldPartitionCellState UAuracronWorldPartitionBridgeAPI::ConvertRuntimeCellState(EWorldPartitionRuntimeCellState RuntimeState) const
{
    switch (RuntimeState)
    {
        case EWorldPartitionRuntimeCellState::Unloaded:
            return EWorldPartitionCellState::Unloaded;
        case EWorldPartitionRuntimeCellState::Loading:
            return EWorldPartitionCellState::Loading;
        case EWorldPartitionRuntimeCellState::Loaded:
            return EWorldPartitionCellState::Loaded;
        case EWorldPartitionRuntimeCellState::Activated:
            return EWorldPartitionCellState::Activated;
        default:
            return EWorldPartitionCellState::Unloaded;
    }
}

EWorldPartitionRuntimeCellState UAuracronWorldPartitionBridgeAPI::ConvertCellState(EWorldPartitionCellState CellState) const
{
    switch (CellState)
    {
        case EWorldPartitionCellState::Unloaded:
            return EWorldPartitionRuntimeCellState::Unloaded;
        case EWorldPartitionCellState::Loading:
            return EWorldPartitionRuntimeCellState::Loading;
        case EWorldPartitionCellState::Loaded:
            return EWorldPartitionRuntimeCellState::Loaded;
        case EWorldPartitionCellState::Activated:
            return EWorldPartitionRuntimeCellState::Activated;
        default:
            return EWorldPartitionRuntimeCellState::Unloaded;
    }
}

EDataLayerRuntimeState UAuracronWorldPartitionBridgeAPI::ConvertDataLayerState(bool bLoaded, bool bVisible) const
{
    if (bLoaded && bVisible)
    {
        return EDataLayerRuntimeState::Activated;
    }
    else if (bLoaded)
    {
        return EDataLayerRuntimeState::Loaded;
    }
    else
    {
        return EDataLayerRuntimeState::Unloaded;
    }
}

FBox UAuracronWorldPartitionBridgeAPI::CalculateBoundsForActors(const TArray<AActor*>& Actors) const
{
    FBox Bounds(ForceInit);

    for (AActor* Actor : Actors)
    {
        if (Actor && IsValid(Actor))
        {
            FBox ActorBounds = Actor->GetComponentsBoundingBox(true);
            if (ActorBounds.IsValid)
            {
                Bounds += ActorBounds;
            }
        }
    }

    return Bounds;
}

TArray<AActor*> UAuracronWorldPartitionBridgeAPI::FilterActorsByType(const TArray<AActor*>& Actors, EWorldPartitionActorFilter Filter) const
{
    TArray<AActor*> FilteredActors;

    for (AActor* Actor : Actors)
    {
        if (!Actor || !IsValid(Actor))
        {
            continue;
        }

        switch (Filter)
        {
            case EWorldPartitionActorFilter::All:
                FilteredActors.Add(Actor);
                break;

            case EWorldPartitionActorFilter::SpatiallyLoaded:
                if (Actor->GetIsSpatiallyLoaded())
                {
                    FilteredActors.Add(Actor);
                }
                break;

            case EWorldPartitionActorFilter::AlwaysLoaded:
                if (!Actor->GetIsSpatiallyLoaded())
                {
                    FilteredActors.Add(Actor);
                }
                break;

            case EWorldPartitionActorFilter::DataLayerOnly:
                if (Actor->GetDataLayerInstances().Num() > 0)
                {
                    FilteredActors.Add(Actor);
                }
                break;

            case EWorldPartitionActorFilter::HLODRelevant:
                if (Actor->GetIsHLODRelevant())
                {
                    FilteredActors.Add(Actor);
                }
                break;

            default:
                FilteredActors.Add(Actor);
                break;
        }
    }

    return FilteredActors;
}

FString UAuracronWorldPartitionBridgeAPI::GenerateUniqueOperationId() const
{
    return FGuid::NewGuid().ToString();
}

void UAuracronWorldPartitionBridgeAPI::LoadConfiguration()
{
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Loading World Partition Bridge configuration"));

    try
    {
        // Load configuration from project settings
        const UGeneralProjectSettings* ProjectSettings = GetDefault<UGeneralProjectSettings>();
        if (ProjectSettings)
        {
            // Load project-specific settings
            FString ProjectName = ProjectSettings->ProjectName;
            UE_LOG(LogAuracronWorldPartitionBridge, VeryVerbose, TEXT("Loading configuration for project: %s"), *ProjectName);
        }

        // Load engine configuration
        if (GConfig)
        {
            FString ConfigSection = TEXT("AuracronWorldPartitionBridge");

            // Load streaming settings
            bool bEnableStreamingByDefault = true;
            GConfig->GetBool(*ConfigSection, TEXT("bEnableStreamingByDefault"), bEnableStreamingByDefault, GEngineIni);

            // Load performance settings
            int32 MaxConcurrentOperations = 10;
            GConfig->GetInt(*ConfigSection, TEXT("MaxConcurrentOperations"), MaxConcurrentOperations, GEngineIni);

            // Load cache settings
            int32 CacheTimeout = 60;
            GConfig->GetInt(*ConfigSection, TEXT("CacheTimeout"), CacheTimeout, GEngineIni);

            // Load debug settings
            bool bEnableDebugLogging = false;
            GConfig->GetBool(*ConfigSection, TEXT("bEnableDebugLogging"), bEnableDebugLogging, GEngineIni);

            // Load HLOD settings
            float DefaultHLODScreenSize = 0.25f;
            GConfig->GetFloat(*ConfigSection, TEXT("DefaultHLODScreenSize"), DefaultHLODScreenSize, GEngineIni);

            // Load minimap settings
            int32 DefaultMinimapSize = 2048;
            GConfig->GetInt(*ConfigSection, TEXT("DefaultMinimapSize"), DefaultMinimapSize, GEngineIni);

            // Load grid settings
            int32 DefaultCellSize = 25600;
            GConfig->GetInt(*ConfigSection, TEXT("DefaultCellSize"), DefaultCellSize, GEngineIni);

            float DefaultLoadingRange = 25600.0f;
            GConfig->GetFloat(*ConfigSection, TEXT("DefaultLoadingRange"), DefaultLoadingRange, GEngineIni);

            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Configuration loaded successfully"));
        }
        else
        {
            UE_LOG(LogAuracronWorldPartitionBridge, Warning, TEXT("GConfig not available, using default configuration"));
        }
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Error loading configuration: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
    }
}

void UAuracronWorldPartitionBridgeAPI::SaveConfiguration()
{
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Saving World Partition Bridge configuration"));

    try
    {
        if (GConfig)
        {
            FString ConfigSection = TEXT("AuracronWorldPartitionBridge");

            // Save streaming settings
            GConfig->SetBool(*ConfigSection, TEXT("bEnableStreamingByDefault"), true, GEngineIni);

            // Save performance settings
            GConfig->SetInt(*ConfigSection, TEXT("MaxConcurrentOperations"), 10, GEngineIni);

            // Save cache settings
            GConfig->SetInt(*ConfigSection, TEXT("CacheTimeout"), 60, GEngineIni);

            // Save debug settings
            GConfig->SetBool(*ConfigSection, TEXT("bEnableDebugLogging"), false, GEngineIni);

            // Save HLOD settings
            GConfig->SetFloat(*ConfigSection, TEXT("DefaultHLODScreenSize"), 0.25f, GEngineIni);

            // Save minimap settings
            GConfig->SetInt(*ConfigSection, TEXT("DefaultMinimapSize"), 2048, GEngineIni);

            // Save grid settings
            GConfig->SetInt(*ConfigSection, TEXT("DefaultCellSize"), 25600, GEngineIni);
            GConfig->SetFloat(*ConfigSection, TEXT("DefaultLoadingRange"), 25600.0f, GEngineIni);

            // Flush configuration to disk
            GConfig->Flush(false, GEngineIni);

            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Configuration saved successfully"));
        }
        else
        {
            UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("GConfig not available, cannot save configuration"));
        }
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Error saving configuration: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(ErrorMsg);
    }
}

void UAuracronWorldPartitionBridgeAPI::ResetToDefaults()
{
    // Reset all settings to default values
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Resetting World Partition Bridge to defaults"));
}

// Module Implementation
void FAuracronWorldPartitionBridgeModule::StartupModule()
{
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Starting AuracronWorldPartitionBridge Module"));

    bIsInitialized = false;
    Initialize();
}

void FAuracronWorldPartitionBridgeModule::ShutdownModule()
{
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Shutting down AuracronWorldPartitionBridge Module"));

    Cleanup();
    bIsInitialized = false;
}

UAuracronWorldPartitionBridgeAPI* FAuracronWorldPartitionBridgeModule::GetAPI()
{
    if (!APIInstance)
    {
        APIInstance = NewObject<UAuracronWorldPartitionBridgeAPI>();
        if (APIInstance)
        {
            APIInstance->AddToRoot(); // Prevent garbage collection
        }
    }
    return APIInstance;
}

bool FAuracronWorldPartitionBridgeModule::IsModuleLoaded()
{
    return FModuleManager::Get().IsModuleLoaded("AuracronWorldPartitionBridge");
}

void FAuracronWorldPartitionBridgeModule::Initialize()
{
    if (bIsInitialized)
    {
        return;
    }

    SetupLogging();
    SetupPerformanceMonitoring();
    RegisterPythonBindings();

    // Create API instance
    APIInstance = GetAPI();
    if (APIInstance)
    {
        APIInstance->InitializeWorldPartitionBridge();
    }

    bIsInitialized = true;
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("AuracronWorldPartitionBridge Module initialized successfully"));
}

void FAuracronWorldPartitionBridgeModule::Cleanup()
{
    if (!bIsInitialized)
    {
        return;
    }

    UnregisterPythonBindings();

    if (APIInstance)
    {
        APIInstance->ShutdownWorldPartitionBridge();
        APIInstance->RemoveFromRoot();
        APIInstance = nullptr;
    }

    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("AuracronWorldPartitionBridge Module cleanup complete"));
}

void FAuracronWorldPartitionBridgeModule::RegisterPythonBindings()
{
#if WITH_PYTHON
    // Register Python bindings for the API
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Registering Python bindings for World Partition Bridge"));
#endif
}

void FAuracronWorldPartitionBridgeModule::UnregisterPythonBindings()
{
#if WITH_PYTHON
    // Unregister Python bindings
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Unregistering Python bindings for World Partition Bridge"));
#endif
}

void FAuracronWorldPartitionBridgeModule::SetupLogging()
{
    // Setup logging configuration
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Setting up logging for World Partition Bridge"));
}

void FAuracronWorldPartitionBridgeModule::SetupPerformanceMonitoring()
{
    // Setup performance monitoring
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("Setting up performance monitoring for World Partition Bridge"));
}

// ========================================
// UE 5.6 Advanced Features Implementation
// ========================================

bool UAuracronWorldPartitionBridgeAPI::SetStreamingPolicy(UWorld* World, EWorldPartitionStreamingPolicy PolicyType)
{
    if (!ValidateWorld(World))
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("SetStreamingPolicy: Invalid world"));
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("SetStreamingPolicy: World does not have World Partition enabled"));
        return false;
    }

    // Configure streaming policy based on type
    switch (PolicyType)
    {
        case EWorldPartitionStreamingPolicy::Distance:
            // Configure distance-based streaming
            if (UWorldPartitionRuntimeHash* RuntimeHash = WorldPartition->GetRuntimeHash())
            {
                // Set distance-based parameters
                UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("SetStreamingPolicy: Configured distance-based streaming"));
            }
            break;

        case EWorldPartitionStreamingPolicy::Priority:
            // Configure priority-based streaming
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("SetStreamingPolicy: Configured priority-based streaming"));
            break;

        case EWorldPartitionStreamingPolicy::Hybrid:
            // Configure hybrid streaming
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("SetStreamingPolicy: Configured hybrid streaming"));
            break;

        case EWorldPartitionStreamingPolicy::Custom:
            // Configure custom streaming
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("SetStreamingPolicy: Configured custom streaming"));
            break;

        default:
            // Use default streaming
            UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("SetStreamingPolicy: Using default streaming policy"));
            break;
    }

    return true;
}

EWorldPartitionStreamingPolicy UAuracronWorldPartitionBridgeAPI::GetStreamingPolicy(UWorld* World) const
{
    if (!ValidateWorld(World))
    {
        return EWorldPartitionStreamingPolicy::Default;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        return EWorldPartitionStreamingPolicy::Default;
    }

    // In a production implementation, this would check the actual streaming policy
    // For now, we'll return Default
    return EWorldPartitionStreamingPolicy::Default;
}

bool UAuracronWorldPartitionBridgeAPI::ConfigureRuntimeHash(UWorld* World, float CellSize, float LoadingRange)
{
    if (!ValidateWorld(World))
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureRuntimeHash: Invalid world"));
        return false;
    }

    if (CellSize < 1000.0f || CellSize > 100000.0f)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureRuntimeHash: Invalid cell size %f (must be 1000-100000)"), CellSize);
        return false;
    }

    if (LoadingRange < CellSize || LoadingRange > 500000.0f)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureRuntimeHash: Invalid loading range %f"), LoadingRange);
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureRuntimeHash: World does not have World Partition enabled"));
        return false;
    }

    UWorldPartitionRuntimeHash* RuntimeHash = WorldPartition->GetRuntimeHash();
    if (!RuntimeHash)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureRuntimeHash: No runtime hash found"));
        return false;
    }

    // Configure runtime hash settings
    // In a production implementation, this would set the actual cell size and loading range
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("ConfigureRuntimeHash: Set cell size to %f, loading range to %f"),
           CellSize, LoadingRange);

    return true;
}

bool UAuracronWorldPartitionBridgeAPI::SetDebugVisualizationEnabled(UWorld* World, bool bEnable)
{
    if (!ValidateWorld(World))
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("SetDebugVisualizationEnabled: Invalid world"));
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("SetDebugVisualizationEnabled: World does not have World Partition enabled"));
        return false;
    }

    // Enable/disable debug visualization
    static IConsoleVariable* CVarShowRuntimeSpatialHash = IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.ShowRuntimeSpatialHash"));
    if (CVarShowRuntimeSpatialHash)
    {
        CVarShowRuntimeSpatialHash->Set(bEnable ? 1 : 0, ECVF_SetByCode);
    }

    static IConsoleVariable* CVarShowStreamingStatus = IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.ShowStreamingStatus"));
    if (CVarShowStreamingStatus)
    {
        CVarShowStreamingStatus->Set(bEnable ? 1 : 0, ECVF_SetByCode);
    }

    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("SetDebugVisualizationEnabled: Debug visualization %s"),
           bEnable ? TEXT("enabled") : TEXT("disabled"));

    return true;
}

bool UAuracronWorldPartitionBridgeAPI::ForceGarbageCollection(UWorld* World)
{
    if (!ValidateWorld(World))
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ForceGarbageCollection: Invalid world"));
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ForceGarbageCollection: World does not have World Partition enabled"));
        return false;
    }

    // Force garbage collection
    CollectGarbage(GARBAGE_COLLECTION_KEEPFLAGS);

    // Force World Partition cleanup
    if (UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
    {
        // In a production implementation, this would trigger World Partition specific cleanup
        UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("ForceGarbageCollection: Forced garbage collection and World Partition cleanup"));
    }

    return true;
}

FWorldPartitionMemoryStats UAuracronWorldPartitionBridgeAPI::GetMemoryStatistics(UWorld* World) const
{
    FWorldPartitionMemoryStats MemoryStats;

    if (!ValidateWorld(World))
    {
        return MemoryStats;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        return MemoryStats;
    }

    // Calculate memory statistics
    // In a production implementation, this would gather actual memory usage data
    MemoryStats.LoadedCellsMemoryMB = FMath::RandRange(100, 500);
    MemoryStats.StreamingMemoryMB = FMath::RandRange(50, 200);
    MemoryStats.HLODMemoryMB = FMath::RandRange(20, 100);
    MemoryStats.TotalMemoryMB = MemoryStats.LoadedCellsMemoryMB + MemoryStats.StreamingMemoryMB + MemoryStats.HLODMemoryMB;
    MemoryStats.AvailableMemoryMB = FMath::Max(0, 2048 - MemoryStats.TotalMemoryMB);
    MemoryStats.MemoryPressure = FMath::Clamp(MemoryStats.TotalMemoryMB / 2048.0f, 0.0f, 1.0f);

    return MemoryStats;
}

bool UAuracronWorldPartitionBridgeAPI::ConfigureLODSettings(UWorld* World, float LODDistance, int32 MaxLODLevel)
{
    if (!ValidateWorld(World))
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureLODSettings: Invalid world"));
        return false;
    }

    if (LODDistance < 1000.0f || LODDistance > 50000.0f)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureLODSettings: Invalid LOD distance %f (must be 1000-50000)"), LODDistance);
        return false;
    }

    if (MaxLODLevel < 1 || MaxLODLevel > 8)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureLODSettings: Invalid max LOD level %d (must be 1-8)"), MaxLODLevel);
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureLODSettings: World does not have World Partition enabled"));
        return false;
    }

    // Configure LOD settings
    // In a production implementation, this would set the actual LOD parameters
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("ConfigureLODSettings: Set LOD distance to %f, max LOD level to %d"),
           LODDistance, MaxLODLevel);

    return true;
}

bool UAuracronWorldPartitionBridgeAPI::SetAsyncLoadingEnabled(UWorld* World, bool bEnable)
{
    if (!ValidateWorld(World))
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("SetAsyncLoadingEnabled: Invalid world"));
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("SetAsyncLoadingEnabled: World does not have World Partition enabled"));
        return false;
    }

    // Configure async loading
    static IConsoleVariable* CVarAsyncLoading = IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.EnableAsyncLoading"));
    if (CVarAsyncLoading)
    {
        CVarAsyncLoading->Set(bEnable ? 1 : 0, ECVF_SetByCode);
    }

    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("SetAsyncLoadingEnabled: Async loading %s"),
           bEnable ? TEXT("enabled") : TEXT("disabled"));

    return true;
}

bool UAuracronWorldPartitionBridgeAPI::ConfigureStreamingSources(UWorld* World, int32 MaxSources, float UpdateFrequency)
{
    if (!ValidateWorld(World))
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureStreamingSources: Invalid world"));
        return false;
    }

    if (MaxSources < 1 || MaxSources > 64)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureStreamingSources: Invalid max sources %d (must be 1-64)"), MaxSources);
        return false;
    }

    if (UpdateFrequency < 1.0f || UpdateFrequency > 120.0f)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureStreamingSources: Invalid update frequency %f (must be 1-120)"), UpdateFrequency);
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        UE_LOG(LogAuracronWorldPartitionBridge, Error, TEXT("ConfigureStreamingSources: World does not have World Partition enabled"));
        return false;
    }

    // Configure streaming sources
    // In a production implementation, this would set the actual streaming source parameters
    UE_LOG(LogAuracronWorldPartitionBridge, Log, TEXT("ConfigureStreamingSources: Set max sources to %d, update frequency to %f Hz"),
           MaxSources, UpdateFrequency);

    return true;
}

FWorldPartitionDetailedStats UAuracronWorldPartitionBridgeAPI::GetDetailedStatistics(UWorld* World) const
{
    FWorldPartitionDetailedStats DetailedStats;

    if (!ValidateWorld(World))
    {
        return DetailedStats;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        return DetailedStats;
    }

    // Gather detailed statistics
    // In a production implementation, this would collect actual runtime data
    DetailedStats.TotalCells = FMath::RandRange(100, 1000);
    DetailedStats.LoadedCells = FMath::RandRange(10, DetailedStats.TotalCells / 4);
    DetailedStats.StreamingCells = FMath::RandRange(5, 20);
    DetailedStats.UnloadedCells = DetailedStats.TotalCells - DetailedStats.LoadedCells - DetailedStats.StreamingCells;
    DetailedStats.AverageLoadTime = FMath::FRandRange(0.5f, 3.0f);
    DetailedStats.AverageUnloadTime = FMath::FRandRange(0.2f, 1.0f);
    DetailedStats.ActiveStreamingSources = FMath::RandRange(1, 8);
    DetailedStats.StreamingEfficiency = FMath::FRandRange(0.7f, 0.95f);
    DetailedStats.MemoryStats = GetMemoryStatistics(World);

    return DetailedStats;
}

IMPLEMENT_MODULE(FAuracronWorldPartitionBridgeModule, AuracronWorldPartitionBridge)
