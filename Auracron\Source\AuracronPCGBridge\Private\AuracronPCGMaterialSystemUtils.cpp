// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Material System Utilities Implementation
// Bridge 2.11: PCG Framework - Material Assignment

#include "AuracronPCGMaterialSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Engine/Texture.h"
#include "Curves/CurveFloat.h"
#include "Curves/CurveLinearColor.h"

// =============================================================================
// MATERIAL SYSTEM UTILITIES IMPLEMENTATION
// =============================================================================

UMaterialInterface* UAuracronPCGMaterialSystemUtils::SelectMaterial(const FPCGPoint& Point, const FAuracronPCGMaterialSelectionDescriptor& SelectionDescriptor)
{
    if (SelectionDescriptor.MaterialOptions.Num() == 0)
    {
        return AuracronPCGMaterialSystemUtils::LoadMaterialSafe(SelectionDescriptor.FallbackMaterial);
    }

    int32 MaterialIndex = 0;

    switch (SelectionDescriptor.SelectionMode)
    {
        case EAuracronPCGMaterialSelectionMode::ByAttribute:
            MaterialIndex = GetMaterialIndexByAttribute(Point, SelectionDescriptor.AttributeName, SelectionDescriptor.MaterialTags);
            break;
        case EAuracronPCGMaterialSelectionMode::ByDistance:
            return GetMaterialByDistance(Point.Transform.GetLocation(), SelectionDescriptor.ReferenceLocation, 
                                       SelectionDescriptor.DistanceThresholds, SelectionDescriptor.MaterialOptions);
        case EAuracronPCGMaterialSelectionMode::ByHeight:
            MaterialIndex = GetMaterialIndexByHeight(Point.Transform.GetLocation(), SelectionDescriptor.HeightThresholds);
            break;
        case EAuracronPCGMaterialSelectionMode::BySlope:
            MaterialIndex = GetMaterialIndexBySlope(Point.Transform.GetRotation().GetUpVector(), SelectionDescriptor.SlopeThresholds);
            break;
        case EAuracronPCGMaterialSelectionMode::ByRandom:
            MaterialIndex = GetRandomMaterialIndex(Point, SelectionDescriptor);
            break;
        default:
            MaterialIndex = 0;
            break;
    }

    // Clamp index to valid range
    MaterialIndex = FMath::Clamp(MaterialIndex, 0, SelectionDescriptor.MaterialOptions.Num() - 1);
    
    return AuracronPCGMaterialSystemUtils::LoadMaterialSafe(SelectionDescriptor.MaterialOptions[MaterialIndex]);
}

TArray<UMaterialInterface*> UAuracronPCGMaterialSystemUtils::SelectMultipleMaterials(const FPCGPoint& Point, const TArray<FAuracronPCGMaterialSelectionDescriptor>& SelectionDescriptors, const TArray<float>& Weights)
{
    TArray<UMaterialInterface*> SelectedMaterials;
    
    for (int32 i = 0; i < SelectionDescriptors.Num(); i++)
    {
        UMaterialInterface* Material = SelectMaterial(Point, SelectionDescriptors[i]);
        if (Material)
        {
            SelectedMaterials.Add(Material);
        }
    }
    
    return SelectedMaterials;
}

int32 UAuracronPCGMaterialSystemUtils::GetMaterialIndexByAttribute(const FPCGPoint& Point, const FString& AttributeName, const TArray<FString>& MaterialTags)
{
    // Simplified attribute access - in production you'd use proper metadata
    FString AttributeValue = AttributeName; // Placeholder
    
    for (int32 i = 0; i < MaterialTags.Num(); i++)
    {
        if (MaterialTags[i].Equals(AttributeValue, ESearchCase::IgnoreCase))
        {
            return i;
        }
    }
    
    return 0; // Default to first material
}

UMaterialInterface* UAuracronPCGMaterialSystemUtils::GetMaterialByDistance(const FVector& Position, const FVector& ReferenceLocation, const TArray<float>& DistanceThresholds, const TArray<TSoftObjectPtr<UMaterialInterface>>& Materials)
{
    if (Materials.Num() == 0)
    {
        return nullptr;
    }

    float Distance = FVector::Dist(Position, ReferenceLocation);
    
    for (int32 i = 0; i < DistanceThresholds.Num() && i < Materials.Num(); i++)
    {
        if (Distance <= DistanceThresholds[i])
        {
            return AuracronPCGMaterialSystemUtils::LoadMaterialSafe(Materials[i]);
        }
    }
    
    // Return last material if beyond all thresholds
    return AuracronPCGMaterialSystemUtils::LoadMaterialSafe(Materials.Last());
}

UMaterialInstanceDynamic* UAuracronPCGMaterialSystemUtils::BlendMaterials(const TArray<UMaterialInterface*>& SourceMaterials, const TArray<float>& BlendWeights, const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor)
{
    if (SourceMaterials.Num() == 0)
    {
        return nullptr;
    }

    if (SourceMaterials.Num() == 1)
    {
        return UMaterialInstanceDynamic::Create(SourceMaterials[0], nullptr);
    }

    // Create dynamic material instance from first material
    UMaterialInstanceDynamic* BlendedMaterial = UMaterialInstanceDynamic::Create(SourceMaterials[0], nullptr);
    if (!BlendedMaterial)
    {
        return nullptr;
    }

    // Apply blending parameters
    BlendedMaterial->SetScalarParameterValue(TEXT("BlendStrength"), BlendingDescriptor.BlendStrength);
    BlendedMaterial->SetScalarParameterValue(TEXT("TransitionWidth"), BlendingDescriptor.TransitionWidth);

    // Set blend weights
    for (int32 i = 0; i < BlendWeights.Num() && i < 4; i++) // Limit to 4 blend layers
    {
        FString WeightParamName = FString::Printf(TEXT("BlendWeight%d"), i);
        BlendedMaterial->SetScalarParameterValue(*WeightParamName, BlendWeights[i]);
    }

    // Set additional material textures if available
    for (int32 i = 1; i < SourceMaterials.Num() && i < 4; i++)
    {
        FString TextureParamName = FString::Printf(TEXT("BlendTexture%d"), i);
        // In production, you'd extract textures from source materials
        // BlendedMaterial->SetTextureParameterValue(*TextureParamName, ExtractedTexture);
    }

    return BlendedMaterial;
}

float UAuracronPCGMaterialSystemUtils::CalculateBlendWeight(const FPCGPoint& Point, const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor)
{
    float BlendWeight = BlendingDescriptor.BlendStrength;
    
    // Apply transition curve if available
    if (BlendingDescriptor.TransitionCurve.IsValid())
    {
        UCurveFloat* Curve = AuracronPCGMaterialSystemUtils::LoadCurveSafe(BlendingDescriptor.TransitionCurve);
        if (Curve)
        {
            BlendWeight *= Curve->GetFloatValue(Point.Density);
        }
    }
    
    return FMath::Clamp(BlendWeight, 0.0f, 1.0f);
}

UMaterialInstanceDynamic* UAuracronPCGMaterialSystemUtils::CreateDynamicMaterialInstance(UMaterialInterface* BaseMaterial, const TArray<FAuracronPCGMaterialParameterDescriptor>& Parameters)
{
    if (!BaseMaterial)
    {
        return nullptr;
    }

    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, nullptr);
    if (!DynamicMaterial)
    {
        return nullptr;
    }

    // Set default parameter values
    for (const FAuracronPCGMaterialParameterDescriptor& Parameter : Parameters)
    {
        switch (Parameter.ParameterType)
        {
            case EAuracronPCGMaterialParameterType::Scalar:
                AuracronPCGMaterialSystemUtils::SetScalarParameter(DynamicMaterial, Parameter.ParameterName, Parameter.ScalarValue);
                break;
            case EAuracronPCGMaterialParameterType::Vector:
                AuracronPCGMaterialSystemUtils::SetVectorParameter(DynamicMaterial, Parameter.ParameterName, Parameter.VectorValue);
                break;
            case EAuracronPCGMaterialParameterType::Color:
                AuracronPCGMaterialSystemUtils::SetColorParameter(DynamicMaterial, Parameter.ParameterName, Parameter.ColorValue);
                break;
            case EAuracronPCGMaterialParameterType::Texture:
                if (Parameter.TextureValue.IsValid())
                {
                    UTexture* Texture = AuracronPCGMaterialSystemUtils::LoadTextureSafe(Parameter.TextureValue);
                    AuracronPCGMaterialSystemUtils::SetTextureParameter(DynamicMaterial, Parameter.ParameterName, Texture);
                }
                break;
            default:
                break;
        }
    }

    return DynamicMaterial;
}

FVector2D UAuracronPCGMaterialSystemUtils::GenerateUVCoordinates(const FVector& Position, const FAuracronPCGUVGenerationDescriptor& UVDescriptor)
{
    switch (UVDescriptor.GenerationMode)
    {
        case EAuracronPCGUVGenerationMode::WorldSpace:
            return GenerateWorldSpaceUV(Position, UVDescriptor.UVScale, UVDescriptor.UVOffset);
        case EAuracronPCGUVGenerationMode::Planar:
            return GeneratePlanarUV(Position, UVDescriptor.ProjectionAxis, UVDescriptor.UVScale);
        case EAuracronPCGUVGenerationMode::Cylindrical:
            return GenerateCylindricalUV(Position, UVDescriptor.ProjectionCenter, UVDescriptor.CylinderRadius, UVDescriptor.ProjectionAxis);
        case EAuracronPCGUVGenerationMode::Spherical:
            return GenerateSphericalUV(Position, UVDescriptor.ProjectionCenter, UVDescriptor.SphereRadius);
        default:
            return GenerateWorldSpaceUV(Position, UVDescriptor.UVScale, UVDescriptor.UVOffset);
    }
}

FVector2D UAuracronPCGMaterialSystemUtils::GenerateWorldSpaceUV(const FVector& Position, const FVector2D& Scale, const FVector2D& Offset)
{
    FVector2D UV;
    UV.X = Position.X * Scale.X + Offset.X;
    UV.Y = Position.Y * Scale.Y + Offset.Y;
    return UV;
}

FVector2D UAuracronPCGMaterialSystemUtils::GeneratePlanarUV(const FVector& Position, const FVector& ProjectionAxis, const FVector2D& Scale)
{
    FVector2D UV;
    
    // Project onto plane perpendicular to projection axis
    if (FMath::Abs(ProjectionAxis.Z) > 0.9f) // Z-axis projection
    {
        UV.X = Position.X * Scale.X;
        UV.Y = Position.Y * Scale.Y;
    }
    else if (FMath::Abs(ProjectionAxis.Y) > 0.9f) // Y-axis projection
    {
        UV.X = Position.X * Scale.X;
        UV.Y = Position.Z * Scale.Y;
    }
    else // X-axis projection
    {
        UV.X = Position.Y * Scale.X;
        UV.Y = Position.Z * Scale.Y;
    }
    
    return UV;
}

FVector2D UAuracronPCGMaterialSystemUtils::GenerateCylindricalUV(const FVector& Position, const FVector& Center, float Radius, const FVector& Axis)
{
    FVector RelativePos = Position - Center;
    
    // Project onto cylinder
    FVector2D UV;
    UV.X = FMath::Atan2(RelativePos.Y, RelativePos.X) / (2.0f * PI) + 0.5f; // Angle around axis
    UV.Y = FVector::DotProduct(RelativePos, Axis.GetSafeNormal()) / Radius; // Height along axis
    
    return UV;
}

FVector2D UAuracronPCGMaterialSystemUtils::GenerateSphericalUV(const FVector& Position, const FVector& Center, float Radius)
{
    FVector RelativePos = (Position - Center).GetSafeNormal();
    
    FVector2D UV;
    UV.X = FMath::Atan2(RelativePos.Y, RelativePos.X) / (2.0f * PI) + 0.5f; // Longitude
    UV.Y = FMath::Acos(RelativePos.Z) / PI; // Latitude
    
    return UV;
}

TArray<FVector2D> UAuracronPCGMaterialSystemUtils::GenerateTriplanarUV(const FVector& Position, const FVector& Normal, float BlendSharpness)
{
    TArray<FVector2D> TriplanarUVs;
    
    // Generate UVs for each axis
    FVector2D UVX = FVector2D(Position.Y, Position.Z); // YZ plane
    FVector2D UVY = FVector2D(Position.X, Position.Z); // XZ plane
    FVector2D UVZ = FVector2D(Position.X, Position.Y); // XY plane
    
    TriplanarUVs.Add(UVX);
    TriplanarUVs.Add(UVY);
    TriplanarUVs.Add(UVZ);
    
    return TriplanarUVs;
}

void UAuracronPCGMaterialSystemUtils::SetMaterialParameter(UMaterialInstanceDynamic* MaterialInstance, const FAuracronPCGMaterialParameterDescriptor& ParameterDescriptor, const FPCGPoint& Point)
{
    if (!MaterialInstance)
    {
        return;
    }

    switch (ParameterDescriptor.ParameterType)
    {
        case EAuracronPCGMaterialParameterType::Scalar:
        {
            float Value = ParameterDescriptor.ScalarValue;
            if (ParameterDescriptor.bUseAttributeAsSource)
            {
                Value = GetAttributeAsFloat(Point, ParameterDescriptor.SourceAttributeName, ParameterDescriptor.ScalarValue);
            }
            AuracronPCGMaterialSystemUtils::SetScalarParameter(MaterialInstance, ParameterDescriptor.ParameterName, Value);
            break;
        }
        case EAuracronPCGMaterialParameterType::Vector:
        {
            FVector Value = ParameterDescriptor.VectorValue;
            if (ParameterDescriptor.bUseAttributeAsSource)
            {
                Value = GetAttributeAsVector(Point, ParameterDescriptor.SourceAttributeName, ParameterDescriptor.VectorValue);
            }
            AuracronPCGMaterialSystemUtils::SetVectorParameter(MaterialInstance, ParameterDescriptor.ParameterName, Value);
            break;
        }
        case EAuracronPCGMaterialParameterType::Color:
        {
            FLinearColor Value = ParameterDescriptor.ColorValue;
            if (ParameterDescriptor.bUseAttributeAsSource)
            {
                Value = GetAttributeAsColor(Point, ParameterDescriptor.SourceAttributeName, ParameterDescriptor.ColorValue);
            }
            AuracronPCGMaterialSystemUtils::SetColorParameter(MaterialInstance, ParameterDescriptor.ParameterName, Value);
            break;
        }
        default:
            break;
    }
}

float UAuracronPCGMaterialSystemUtils::GetAttributeAsFloat(const FPCGPoint& Point, const FString& AttributeName, float DefaultValue)
{
    // Simplified attribute access - in production you'd use proper metadata
    if (AttributeName == TEXT("Density"))
    {
        return Point.Density;
    }
    else if (AttributeName == TEXT("X"))
    {
        return Point.Transform.GetLocation().X;
    }
    else if (AttributeName == TEXT("Y"))
    {
        return Point.Transform.GetLocation().Y;
    }
    else if (AttributeName == TEXT("Z"))
    {
        return Point.Transform.GetLocation().Z;
    }
    
    return DefaultValue;
}

FVector UAuracronPCGMaterialSystemUtils::GetAttributeAsVector(const FPCGPoint& Point, const FString& AttributeName, const FVector& DefaultValue)
{
    // Simplified attribute access - in production you'd use proper metadata
    if (AttributeName == TEXT("Position"))
    {
        return Point.Transform.GetLocation();
    }
    else if (AttributeName == TEXT("Scale"))
    {
        return Point.Transform.GetScale3D();
    }
    
    return DefaultValue;
}

FLinearColor UAuracronPCGMaterialSystemUtils::GetAttributeAsColor(const FPCGPoint& Point, const FString& AttributeName, const FLinearColor& DefaultValue)
{
    // Simplified attribute access - in production you'd use proper metadata
    if (AttributeName == TEXT("Color"))
    {
        return FLinearColor(Point.Color);
    }
    
    return DefaultValue;
}

bool UAuracronPCGMaterialSystemUtils::ValidateMaterialSelectionDescriptor(const FAuracronPCGMaterialSelectionDescriptor& SelectionDescriptor)
{
    // Validate material options
    if (SelectionDescriptor.MaterialOptions.Num() == 0 && !SelectionDescriptor.FallbackMaterial.IsValid())
    {
        return false;
    }

    // Validate thresholds
    if (SelectionDescriptor.SelectionMode == EAuracronPCGMaterialSelectionMode::ByDistance && SelectionDescriptor.DistanceThresholds.Num() == 0)
    {
        return false;
    }

    if (SelectionDescriptor.SelectionMode == EAuracronPCGMaterialSelectionMode::ByHeight && SelectionDescriptor.HeightThresholds.Num() == 0)
    {
        return false;
    }

    if (SelectionDescriptor.SelectionMode == EAuracronPCGMaterialSelectionMode::BySlope && SelectionDescriptor.SlopeThresholds.Num() == 0)
    {
        return false;
    }

    return true;
}

bool UAuracronPCGMaterialSystemUtils::ValidateMaterialBlendingDescriptor(const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor)
{
    // Validate blend strength
    if (BlendingDescriptor.BlendStrength < 0.0f || BlendingDescriptor.BlendStrength > 1.0f)
    {
        return false;
    }

    // Validate transition width
    if (BlendingDescriptor.TransitionWidth < 0.0f)
    {
        return false;
    }

    return true;
}

bool UAuracronPCGMaterialSystemUtils::ValidateUVGenerationDescriptor(const FAuracronPCGUVGenerationDescriptor& UVDescriptor)
{
    // Validate UV scale
    if (UVDescriptor.UVScale.X <= 0.0f || UVDescriptor.UVScale.Y <= 0.0f)
    {
        return false;
    }

    // Validate radius values
    if (UVDescriptor.CylinderRadius <= 0.0f || UVDescriptor.SphereRadius <= 0.0f)
    {
        return false;
    }

    return true;
}

FAuracronPCGMaterialSelectionDescriptor UAuracronPCGMaterialSystemUtils::CreateDefaultMaterialSelectionDescriptor(EAuracronPCGMaterialSelectionMode SelectionMode)
{
    FAuracronPCGMaterialSelectionDescriptor Descriptor;
    Descriptor.SelectionMode = SelectionMode;
    
    switch (SelectionMode)
    {
        case EAuracronPCGMaterialSelectionMode::ByAttribute:
            Descriptor.AttributeName = TEXT("MaterialType");
            break;
        case EAuracronPCGMaterialSelectionMode::ByDistance:
            Descriptor.DistanceThresholds = {100.0f, 500.0f, 1000.0f};
            break;
        case EAuracronPCGMaterialSelectionMode::ByHeight:
            Descriptor.HeightThresholds = {0.0f, 100.0f, 500.0f};
            break;
        case EAuracronPCGMaterialSelectionMode::BySlope:
            Descriptor.SlopeThresholds = {15.0f, 30.0f, 45.0f};
            break;
        default:
            break;
    }
    
    return Descriptor;
}

// Helper functions for material index calculation
int32 UAuracronPCGMaterialSystemUtils::GetMaterialIndexByHeight(const FVector& Position, const TArray<float>& HeightThresholds)
{
    float Height = Position.Z;
    
    for (int32 i = 0; i < HeightThresholds.Num(); i++)
    {
        if (Height <= HeightThresholds[i])
        {
            return i;
        }
    }
    
    return HeightThresholds.Num(); // Return last index + 1 if above all thresholds
}

int32 UAuracronPCGMaterialSystemUtils::GetMaterialIndexBySlope(const FVector& Normal, const TArray<float>& SlopeThresholds)
{
    float SlopeAngle = FMath::Acos(FVector::DotProduct(Normal, FVector::UpVector)) * 180.0f / PI;
    
    for (int32 i = 0; i < SlopeThresholds.Num(); i++)
    {
        if (SlopeAngle <= SlopeThresholds[i])
        {
            return i;
        }
    }
    
    return SlopeThresholds.Num(); // Return last index + 1 if above all thresholds
}

int32 UAuracronPCGMaterialSystemUtils::GetRandomMaterialIndex(const FPCGPoint& Point, const FAuracronPCGMaterialSelectionDescriptor& SelectionDescriptor)
{
    FVector Position = Point.Transform.GetLocation();
    int32 Seed = SelectionDescriptor.RandomSeed;
    
    if (SelectionDescriptor.bDeterministicSelection)
    {
        Seed += FMath::FloorToInt(Position.X + Position.Y + Position.Z);
    }
    
    FRandomStream RandomStream(Seed);
    return RandomStream.RandRange(0, SelectionDescriptor.MaterialOptions.Num() - 1);
}
