// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Spline System Utilities Implementation
// Bridge 2.8: PCG Framework - Spline System

#include "AuracronPCGSplineSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Spline includes
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/SplineActor.h"
#include "SplineMeshActor.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

// =============================================================================
// SPLINE SYSTEM UTILITIES IMPLEMENTATION
// =============================================================================

USplineComponent* UAuracronPCGSplineSystemUtils::CreateSplineFromPoints(const TArray<FVector>& Points, bool bClosedSpline, EAuracronPCGSplineTangentMode TangentMode)
{
    if (Points.Num() < 2)
    {
        return nullptr;
    }

    USplineComponent* SplineComponent = NewObject<USplineComponent>();
    if (!SplineComponent)
    {
        return nullptr;
    }

    // Clear existing points
    SplineComponent->ClearSplinePoints();

    // Add points to spline
    for (int32 i = 0; i < Points.Num(); i++)
    {
        SplineComponent->AddSplinePoint(Points[i], ESplineCoordinateSpace::World);
    }

    // Set closed loop
    SplineComponent->SetClosedLoop(bClosedSpline);

    // Configure tangent modes
    ESplineTangentMode UnrealTangentMode = ConvertTangentMode(TangentMode);
    for (int32 i = 0; i < SplineComponent->GetNumberOfSplinePoints(); i++)
    {
        SplineComponent->SetTangentAtSplinePoint(i, FVector::ZeroVector, ESplineCoordinateSpace::Local);
        SplineComponent->SetSplinePointType(i, UnrealTangentMode);
    }

    // Update spline
    SplineComponent->UpdateSpline();

    return SplineComponent;
}

USplineComponent* UAuracronPCGSplineSystemUtils::CreateSplineFromPointData(const UPCGPointData* PointData, bool bClosedSpline)
{
    if (!PointData)
    {
        return nullptr;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    TArray<FVector> Locations;
    Locations.Reserve(Points.Num());

    for (const FPCGPoint& Point : Points)
    {
        Locations.Add(Point.Transform.GetLocation());
    }

    return CreateSplineFromPoints(Locations, bClosedSpline);
}

bool UAuracronPCGSplineSystemUtils::ValidateSplinePoints(const TArray<FVector>& Points, float MinDistance)
{
    if (Points.Num() < 2)
    {
        return false;
    }

    // Check for minimum distance between consecutive points
    for (int32 i = 1; i < Points.Num(); i++)
    {
        float Distance = FVector::Dist(Points[i-1], Points[i]);
        if (Distance < MinDistance)
        {
            return false;
        }
    }

    return true;
}

TArray<FVector> UAuracronPCGSplineSystemUtils::DistributePointsAlongSpline(USplineComponent* SplineComponent, int32 PointCount, EAuracronPCGSplineDistributionMode DistributionMode)
{
    TArray<FVector> DistributedPoints;
    
    if (!SplineComponent || PointCount <= 0)
    {
        return DistributedPoints;
    }

    float SplineLength = CalculateSplineLength(SplineComponent);
    DistributedPoints.Reserve(PointCount);

    switch (DistributionMode)
    {
        case EAuracronPCGSplineDistributionMode::Uniform:
        {
            for (int32 i = 0; i < PointCount; i++)
            {
                float T = static_cast<float>(i) / FMath::Max(1, PointCount - 1);
                float Distance = T * SplineLength;
                FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
                DistributedPoints.Add(Location);
            }
            break;
        }
        case EAuracronPCGSplineDistributionMode::Random:
        {
            FRandomStream RandomStream(12345);
            for (int32 i = 0; i < PointCount; i++)
            {
                float RandomT = RandomStream.FRand();
                float Distance = RandomT * SplineLength;
                FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
                DistributedPoints.Add(Location);
            }
            break;
        }
        default:
        {
            // Default to uniform distribution
            for (int32 i = 0; i < PointCount; i++)
            {
                float T = static_cast<float>(i) / FMath::Max(1, PointCount - 1);
                float Distance = T * SplineLength;
                FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
                DistributedPoints.Add(Location);
            }
            break;
        }
    }

    return DistributedPoints;
}

TArray<FTransform> UAuracronPCGSplineSystemUtils::DistributeTransformsAlongSpline(USplineComponent* SplineComponent, int32 PointCount, bool bAlignToSpline)
{
    TArray<FTransform> DistributedTransforms;
    
    if (!SplineComponent || PointCount <= 0)
    {
        return DistributedTransforms;
    }

    float SplineLength = CalculateSplineLength(SplineComponent);
    DistributedTransforms.Reserve(PointCount);

    for (int32 i = 0; i < PointCount; i++)
    {
        float T = static_cast<float>(i) / FMath::Max(1, PointCount - 1);
        float Distance = T * SplineLength;
        
        FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        FRotator Rotation = FRotator::ZeroRotator;
        
        if (bAlignToSpline)
        {
            FVector Direction = SplineComponent->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            Rotation = FRotationMatrix::MakeFromX(Direction).Rotator();
        }
        
        FVector Scale = SplineComponent->GetScaleAtDistanceAlongSpline(Distance);
        
        DistributedTransforms.Add(FTransform(Rotation, Location, Scale));
    }

    return DistributedTransforms;
}

float UAuracronPCGSplineSystemUtils::CalculateSplineLength(USplineComponent* SplineComponent)
{
    if (!SplineComponent)
    {
        return 0.0f;
    }

    return SplineComponent->GetSplineLength();
}

USplineMeshComponent* UAuracronPCGSplineSystemUtils::CreateSplineMeshComponent(USplineComponent* SplineComponent, UStaticMesh* StaticMesh)
{
    if (!SplineComponent || !StaticMesh)
    {
        return nullptr;
    }

    USplineMeshComponent* SplineMeshComponent = NewObject<USplineMeshComponent>();
    if (!SplineMeshComponent)
    {
        return nullptr;
    }

    // Configure spline mesh component
    SplineMeshComponent->SetStaticMesh(StaticMesh);
    SplineMeshComponent->SetSplineUpDir(FVector::UpVector);

    // Set spline mesh parameters based on spline component
    FVector StartPos = SplineComponent->GetLocationAtSplinePoint(0, ESplineCoordinateSpace::Local);
    FVector StartTangent = SplineComponent->GetTangentAtSplinePoint(0, ESplineCoordinateSpace::Local);
    FVector EndPos = SplineComponent->GetLocationAtSplinePoint(1, ESplineCoordinateSpace::Local);
    FVector EndTangent = SplineComponent->GetTangentAtSplinePoint(1, ESplineCoordinateSpace::Local);

    SplineMeshComponent->SetStartAndEnd(StartPos, StartTangent, EndPos, EndTangent);

    return SplineMeshComponent;
}

bool UAuracronPCGSplineSystemUtils::GenerateSplineMeshes(USplineComponent* SplineComponent, const TArray<FAuracronPCGSplineMeshDescriptor>& MeshDescriptors)
{
    if (!SplineComponent || MeshDescriptors.Num() == 0)
    {
        return false;
    }

    // Generate meshes for each descriptor
    for (const FAuracronPCGSplineMeshDescriptor& MeshDescriptor : MeshDescriptors)
    {
        UStaticMesh* StaticMesh = MeshDescriptor.StaticMesh.LoadSynchronous();
        if (!StaticMesh)
        {
            continue;
        }

        switch (MeshDescriptor.MeshMode)
        {
            case EAuracronPCGSplineMeshMode::SplineMeshComponent:
                GenerateSplineMeshComponents(SplineComponent, MeshDescriptor);
                break;
            case EAuracronPCGSplineMeshMode::InstancedMesh:
                GenerateInstancedMeshes(SplineComponent, MeshDescriptor);
                break;
            default:
                GenerateSplineMeshComponents(SplineComponent, MeshDescriptor);
                break;
        }
    }

    return true;
}

TArray<FVector> UAuracronPCGSplineSystemUtils::FindPath(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor)
{
    TArray<FVector> Path;

    switch (PathFindingDescriptor.PathFindingMode)
    {
        case EAuracronPCGPathFindingMode::AStar:
            AuracronPCGSplineSystemUtils::PerformAStarPathFinding(Start, End, PathFindingDescriptor, Path);
            break;
        case EAuracronPCGPathFindingMode::Dijkstra:
            AuracronPCGSplineSystemUtils::PerformDijkstraPathFinding(Start, End, PathFindingDescriptor, Path);
            break;
        default:
            // Simple direct path
            Path.Add(Start);
            Path.Add(End);
            break;
    }

    // Smooth path if requested
    if (PathFindingDescriptor.bSmoothPath && Path.Num() > 2)
    {
        Path = SmoothPath(Path, PathFindingDescriptor.SmoothingStrength, PathFindingDescriptor.SmoothingIterations);
    }

    return Path;
}

bool UAuracronPCGSplineSystemUtils::IsPathValid(const TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor)
{
    if (Path.Num() < 2)
    {
        return false;
    }

    // Check path constraints
    for (int32 i = 1; i < Path.Num(); i++)
    {
        FVector Segment = Path[i] - Path[i-1];
        float SegmentLength = Segment.Size();
        
        if (SegmentLength < PathFindingDescriptor.MinWidth)
        {
            return false;
        }

        // Check slope constraint
        float Slope = FMath::Abs(FMath::Atan2(Segment.Z, FVector2D(Segment.X, Segment.Y).Size()));
        float SlopeDegrees = FMath::RadiansToDegrees(Slope);
        
        if (SlopeDegrees > PathFindingDescriptor.MaxSlope)
        {
            return false;
        }
    }

    return true;
}

TArray<FVector> UAuracronPCGSplineSystemUtils::SmoothPath(const TArray<FVector>& Path, float SmoothingStrength, int32 Iterations)
{
    if (Path.Num() < 3)
    {
        return Path;
    }

    TArray<FVector> SmoothedPath = Path;

    for (int32 Iter = 0; Iter < Iterations; Iter++)
    {
        TArray<FVector> NewPath = SmoothedPath;

        // Apply smoothing to interior points
        for (int32 i = 1; i < SmoothedPath.Num() - 1; i++)
        {
            FVector PrevPoint = SmoothedPath[i-1];
            FVector CurrentPoint = SmoothedPath[i];
            FVector NextPoint = SmoothedPath[i+1];

            FVector SmoothedPoint = (PrevPoint + CurrentPoint + NextPoint) / 3.0f;
            NewPath[i] = FMath::Lerp(CurrentPoint, SmoothedPoint, SmoothingStrength);
        }

        SmoothedPath = NewPath;
    }

    return SmoothedPath;
}

float UAuracronPCGSplineSystemUtils::CalculateSplineCurvature(USplineComponent* SplineComponent, float Distance)
{
    if (!SplineComponent)
    {
        return 0.0f;
    }

    // Sample points around the distance to calculate curvature
    float SampleDistance = 10.0f;
    FVector P1 = SplineComponent->GetLocationAtDistanceAlongSpline(Distance - SampleDistance, ESplineCoordinateSpace::World);
    FVector P2 = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
    FVector P3 = SplineComponent->GetLocationAtDistanceAlongSpline(Distance + SampleDistance, ESplineCoordinateSpace::World);

    // Calculate curvature using three points
    FVector V1 = (P2 - P1).GetSafeNormal();
    FVector V2 = (P3 - P2).GetSafeNormal();
    
    float DotProduct = FVector::DotProduct(V1, V2);
    float Angle = FMath::Acos(FMath::Clamp(DotProduct, -1.0f, 1.0f));
    
    return Angle / SampleDistance;
}

FVector UAuracronPCGSplineSystemUtils::GetSplineDirectionAtDistance(USplineComponent* SplineComponent, float Distance)
{
    if (!SplineComponent)
    {
        return FVector::ForwardVector;
    }

    return SplineComponent->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
}

float UAuracronPCGSplineSystemUtils::GetSplineWidthAtDistance(USplineComponent* SplineComponent, float Distance)
{
    if (!SplineComponent)
    {
        return 100.0f;
    }

    // Get scale at distance and use X component as width
    FVector Scale = SplineComponent->GetScaleAtDistanceAlongSpline(Distance);
    return Scale.X * 100.0f; // Convert to reasonable width units
}

bool UAuracronPCGSplineSystemUtils::OptimizeSpline(USplineComponent* SplineComponent, float Tolerance)
{
    if (!SplineComponent)
    {
        return false;
    }

    // Optimize spline tangents
    AuracronPCGSplineSystemUtils::OptimizeSplineTangents(SplineComponent, 0.5f);

    return true;
}

bool UAuracronPCGSplineSystemUtils::SimplifySpline(USplineComponent* SplineComponent, float Tolerance)
{
    if (!SplineComponent)
    {
        return false;
    }

    // Get current spline points
    TArray<FVector> Points;
    int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
    
    for (int32 i = 0; i < NumPoints; i++)
    {
        Points.Add(SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World));
    }

    // Simplify using Douglas-Peucker algorithm (simplified implementation)
    TArray<FVector> SimplifiedPoints;
    SimplifyPointArray(Points, SimplifiedPoints, Tolerance);

    // Rebuild spline with simplified points
    if (SimplifiedPoints.Num() >= 2 && SimplifiedPoints.Num() < Points.Num())
    {
        SplineComponent->ClearSplinePoints();
        for (const FVector& Point : SimplifiedPoints)
        {
            SplineComponent->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }
        SplineComponent->UpdateSpline();
        return true;
    }

    return false;
}

int32 UAuracronPCGSplineSystemUtils::GetOptimalPointCount(float SplineLength, float TargetSpacing)
{
    if (TargetSpacing <= 0.0f)
    {
        return 2;
    }

    return FMath::Max(2, FMath::CeilToInt(SplineLength / TargetSpacing));
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

ESplineTangentMode UAuracronPCGSplineSystemUtils::ConvertTangentMode(EAuracronPCGSplineTangentMode TangentMode)
{
    switch (TangentMode)
    {
        case EAuracronPCGSplineTangentMode::Auto:
            return ESplineTangentMode::Auto;
        case EAuracronPCGSplineTangentMode::User:
            return ESplineTangentMode::User;
        case EAuracronPCGSplineTangentMode::Break:
            return ESplineTangentMode::Break;
        case EAuracronPCGSplineTangentMode::Linear:
            return ESplineTangentMode::Linear;
        case EAuracronPCGSplineTangentMode::Constant:
            return ESplineTangentMode::Constant;
        default:
            return ESplineTangentMode::Auto;
    }
}

void UAuracronPCGSplineSystemUtils::GenerateSplineMeshComponents(USplineComponent* SplineComponent, const FAuracronPCGSplineMeshDescriptor& MeshDescriptor)
{
    // Generate spline mesh components for each segment
    int32 NumSegments = SplineComponent->GetNumberOfSplineSegments();
    
    for (int32 i = 0; i < NumSegments; i++)
    {
        UStaticMesh* StaticMesh = MeshDescriptor.StaticMesh.LoadSynchronous();
        if (StaticMesh)
        {
            USplineMeshComponent* SplineMeshComponent = CreateSplineMeshComponent(SplineComponent, StaticMesh);
            if (SplineMeshComponent)
            {
                // Configure segment-specific parameters
                FVector StartPos = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::Local);
                FVector StartTangent = SplineComponent->GetTangentAtSplinePoint(i, ESplineCoordinateSpace::Local);
                FVector EndPos = SplineComponent->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::Local);
                FVector EndTangent = SplineComponent->GetTangentAtSplinePoint(i + 1, ESplineCoordinateSpace::Local);

                SplineMeshComponent->SetStartAndEnd(StartPos, StartTangent, EndPos, EndTangent);
            }
        }
    }
}

void UAuracronPCGSplineSystemUtils::GenerateInstancedMeshes(USplineComponent* SplineComponent, const FAuracronPCGSplineMeshDescriptor& MeshDescriptor)
{
    // Generate instanced meshes along spline
    // Simplified implementation - in production you'd create proper instanced mesh components
}

void UAuracronPCGSplineSystemUtils::SimplifyPointArray(const TArray<FVector>& InputPoints, TArray<FVector>& OutputPoints, float Tolerance)
{
    // Simplified Douglas-Peucker implementation
    OutputPoints = InputPoints;
    
    if (InputPoints.Num() < 3)
    {
        return;
    }

    // For now, just remove every other point if tolerance is met
    // In production, you'd implement proper Douglas-Peucker algorithm
    TArray<FVector> SimplifiedPoints;
    SimplifiedPoints.Add(InputPoints[0]);
    
    for (int32 i = 1; i < InputPoints.Num() - 1; i += 2)
    {
        SimplifiedPoints.Add(InputPoints[i]);
    }
    
    SimplifiedPoints.Add(InputPoints.Last());
    OutputPoints = SimplifiedPoints;
}
