// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Attribute System Implementation
// Bridge 2.5: PCG Framework - Attribute System

#include "AuracronPCGAttributeSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "PCGMetadataAttribute.h"
#include "PCGMetadataAttributeTraits.h"
#include "PCGMetadataAccessor.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Async/ParallelFor.h"
#include "Curves/CurveFloat.h"

// =============================================================================
// ATTRIBUTE CREATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeCreatorSettings::UAuracronPCGAttributeCreatorSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Creator");
    NodeMetadata.NodeDescription = TEXT("Creates new attributes with specified properties and default values");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Attribute;
    NodeMetadata.Tags.Add(TEXT("Attribute"));
    NodeMetadata.Tags.Add(TEXT("Create"));
    NodeMetadata.Tags.Add(TEXT("Metadata"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.6f, 0.8f);
}

void UAuracronPCGAttributeCreatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeCreatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
}

FAuracronPCGElementResult FAuracronPCGAttributeCreatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                           FPCGDataCollection& OutputData, 
                                                                           const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAttributeCreatorSettings* Settings = GetTypedSettings<UAuracronPCGAttributeCreatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Creator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 AttributesCreated = 0;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSpatialData* InputSpatialData = Cast<UPCGSpatialData>(TaggedData.Data);
            if (!InputSpatialData)
            {
                continue;
            }

            // Convert to point data if necessary
            const UPCGPointData* InputPointData = InputSpatialData->ToPointData(nullptr);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            // Ensure metadata exists
            if (!OutputPointData->Metadata)
            {
                if (Settings->bCreateMetadataIfMissing)
                {
                    OutputPointData->Metadata = NewObject<UPCGMetadata>();
                    OutputPointData->Metadata->Initialize(OutputPointData->GetPoints().Num());
                }
                else
                {
                    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("No metadata found and creation disabled"));
                    continue;
                }
            }

            // Create attributes
            if (Settings->bUseBatchCreation)
            {
                // Batch creation
                for (int32 i = 0; i < Settings->BatchCount; i++)
                {
                    FAuracronPCGAttributeDescriptor BatchDescriptor;
                    BatchDescriptor.AttributeName = FString::Printf(TEXT("%s%d"), *Settings->AttributePrefix, i);
                    BatchDescriptor.AttributeType = Settings->BatchType;
                    BatchDescriptor.DefaultValue = TEXT("0");

                    if (CreateAttributeFromDescriptor(OutputPointData->Metadata, BatchDescriptor, Settings))
                    {
                        AttributesCreated++;
                    }
                }
            }
            else
            {
                // Individual creation
                for (const FAuracronPCGAttributeDescriptor& Descriptor : Settings->AttributesToCreate)
                {
                    if (CreateAttributeFromDescriptor(OutputPointData->Metadata, Descriptor, Settings))
                    {
                        AttributesCreated++;
                    }
                }
            }

            TotalProcessed += InputPointData->GetPoints().Num();

            // Add to output
            FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
            OutputTaggedData.Data = OutputPointData;
            OutputTaggedData.Pin = TEXT("Output");
            OutputTaggedData.Tags = TaggedData.Tags;
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Creator processed %d points and created %d attributes"), 
                                  TotalProcessed, AttributesCreated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Creator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGAttributeCreatorElement::CreateAttributeFromDescriptor(UPCGMetadata* Metadata, 
                                                                        const FAuracronPCGAttributeDescriptor& Descriptor,
                                                                        const UAuracronPCGAttributeCreatorSettings* Settings) const
{
    if (!Metadata)
    {
        return false;
    }

    // Check if attribute already exists
    if (Metadata->HasAttribute(FName(*Descriptor.AttributeName)))
    {
        if (!Settings->bOverwriteExisting)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Attribute '%s' already exists and overwrite is disabled"), 
                                      *Descriptor.AttributeName);
            return false;
        }
        else
        {
            // Delete existing attribute
            Metadata->DeleteAttribute(FName(*Descriptor.AttributeName));
        }
    }

    // Create attribute based on type
    bool bSuccess = false;
    switch (Descriptor.AttributeType)
    {
        case EAuracronPCGAttributeType::Float:
        {
            float DefaultValue = FCString::Atof(*Descriptor.DefaultValue);
            bSuccess = Metadata->CreateFloatAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/true);
            break;
        }
        case EAuracronPCGAttributeType::Double:
        {
            double DefaultValue = FCString::Atod(*Descriptor.DefaultValue);
            bSuccess = Metadata->CreateDoubleAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/true);
            break;
        }
        case EAuracronPCGAttributeType::Int32:
        {
            int32 DefaultValue = FCString::Atoi(*Descriptor.DefaultValue);
            bSuccess = Metadata->CreateIntegerAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/false);
            break;
        }
        case EAuracronPCGAttributeType::Int64:
        {
            int64 DefaultValue = FCString::Atoi64(*Descriptor.DefaultValue);
            bSuccess = Metadata->CreateInteger64Attribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/false);
            break;
        }
        case EAuracronPCGAttributeType::Vector2:
        {
            FVector2D DefaultValue = FVector2D::ZeroVector;
            // Parse vector2 from string (simplified)
            TArray<FString> Components;
            Descriptor.DefaultValue.ParseIntoArray(Components, TEXT(","), true);
            if (Components.Num() >= 2)
            {
                DefaultValue.X = FCString::Atof(*Components[0].TrimStartAndEnd());
                DefaultValue.Y = FCString::Atof(*Components[1].TrimStartAndEnd());
            }
            bSuccess = Metadata->CreateVector2Attribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/true);
            break;
        }
        case EAuracronPCGAttributeType::Vector:
        {
            FVector DefaultValue = FVector::ZeroVector;
            // Parse vector from string (simplified)
            TArray<FString> Components;
            Descriptor.DefaultValue.ParseIntoArray(Components, TEXT(","), true);
            if (Components.Num() >= 3)
            {
                DefaultValue.X = FCString::Atof(*Components[0].TrimStartAndEnd());
                DefaultValue.Y = FCString::Atof(*Components[1].TrimStartAndEnd());
                DefaultValue.Z = FCString::Atof(*Components[2].TrimStartAndEnd());
            }
            bSuccess = Metadata->CreateVectorAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/true);
            break;
        }
        case EAuracronPCGAttributeType::Vector4:
        {
            FVector4 DefaultValue = FVector4::Zero();
            // Parse vector4 from string (simplified)
            TArray<FString> Components;
            Descriptor.DefaultValue.ParseIntoArray(Components, TEXT(","), true);
            if (Components.Num() >= 4)
            {
                DefaultValue.X = FCString::Atof(*Components[0].TrimStartAndEnd());
                DefaultValue.Y = FCString::Atof(*Components[1].TrimStartAndEnd());
                DefaultValue.Z = FCString::Atof(*Components[2].TrimStartAndEnd());
                DefaultValue.W = FCString::Atof(*Components[3].TrimStartAndEnd());
            }
            bSuccess = Metadata->CreateVector4Attribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/true);
            break;
        }
        case EAuracronPCGAttributeType::Rotator:
        {
            FRotator DefaultValue = FRotator::ZeroRotator;
            // Parse rotator from string (simplified)
            TArray<FString> Components;
            Descriptor.DefaultValue.ParseIntoArray(Components, TEXT(","), true);
            if (Components.Num() >= 3)
            {
                DefaultValue.Pitch = FCString::Atof(*Components[0].TrimStartAndEnd());
                DefaultValue.Yaw = FCString::Atof(*Components[1].TrimStartAndEnd());
                DefaultValue.Roll = FCString::Atof(*Components[2].TrimStartAndEnd());
            }
            bSuccess = Metadata->CreateRotatorAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/true);
            break;
        }
        case EAuracronPCGAttributeType::Quat:
        {
            FQuat DefaultValue = FQuat::Identity;
            bSuccess = Metadata->CreateQuatAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/true);
            break;
        }
        case EAuracronPCGAttributeType::Transform:
        {
            FTransform DefaultValue = FTransform::Identity;
            bSuccess = Metadata->CreateTransformAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/true);
            break;
        }
        case EAuracronPCGAttributeType::String:
        {
            bSuccess = Metadata->CreateStringAttribute(FName(*Descriptor.AttributeName), Descriptor.DefaultValue, /*bAllowsInterpolation=*/false);
            break;
        }
        case EAuracronPCGAttributeType::Name:
        {
            FName DefaultValue(*Descriptor.DefaultValue);
            bSuccess = Metadata->CreateNameAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/false);
            break;
        }
        case EAuracronPCGAttributeType::Boolean:
        {
            bool DefaultValue = Descriptor.DefaultValue.ToBool();
            bSuccess = Metadata->CreateBoolAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/false);
            break;
        }
        case EAuracronPCGAttributeType::SoftObjectPath:
        {
            FSoftObjectPath DefaultValue(Descriptor.DefaultValue);
            bSuccess = Metadata->CreateSoftObjectPathAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/false);
            break;
        }
        case EAuracronPCGAttributeType::SoftClassPath:
        {
            FSoftClassPath DefaultValue(Descriptor.DefaultValue);
            bSuccess = Metadata->CreateSoftClassPathAttribute(FName(*Descriptor.AttributeName), DefaultValue, /*bAllowsInterpolation=*/false);
            break;
        }
        default:
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Unsupported attribute type for '%s'"), *Descriptor.AttributeName);
            return false;
    }

    if (bSuccess && Settings->bValidateOnCreation)
    {
        // Validate the created attribute
        TArray<FString> ValidationErrors;
        if (!UAuracronPCGAttributeSystemUtils::ValidateAttribute(Metadata, Descriptor, ValidationErrors))
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Attribute '%s' created but failed validation: %s"), 
                                      *Descriptor.AttributeName, *FString::Join(ValidationErrors, TEXT(", ")));
        }
    }

    return bSuccess;
}

// =============================================================================
// ATTRIBUTE MODIFIER IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeModifierSettings::UAuracronPCGAttributeModifierSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Modifier");
    NodeMetadata.NodeDescription = TEXT("Modifies existing attributes with various operations and transformations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Attribute;
    NodeMetadata.Tags.Add(TEXT("Attribute"));
    NodeMetadata.Tags.Add(TEXT("Modify"));
    NodeMetadata.Tags.Add(TEXT("Transform"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.4f, 0.8f);
}

void UAuracronPCGAttributeModifierSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeModifierSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
}

FAuracronPCGElementResult FAuracronPCGAttributeModifierElement::ProcessData(const FPCGDataCollection& InputData,
                                                                            FPCGDataCollection& OutputData,
                                                                            const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAttributeModifierSettings* Settings = GetTypedSettings<UAuracronPCGAttributeModifierSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Modifier");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 AttributesModified = 0;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSpatialData* InputSpatialData = Cast<UPCGSpatialData>(TaggedData.Data);
            if (!InputSpatialData)
            {
                continue;
            }

            // Convert to point data if necessary
            const UPCGPointData* InputPointData = InputSpatialData->ToPointData(nullptr);
            if (!InputPointData || !InputPointData->Metadata)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            // Process operations
            if (Settings->bUseParallelProcessing && InputPointData->GetPoints().Num() > Settings->BatchSize)
            {
                // Parallel processing
                ProcessOperationsParallel(OutputPointData, Settings);
            }
            else
            {
                // Sequential processing
                ProcessOperationsSequential(OutputPointData, Settings);
            }

            AttributesModified += Settings->Operations.Num();
            TotalProcessed += InputPointData->GetPoints().Num();

            // Add to output
            FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
            OutputTaggedData.Data = OutputPointData;
            OutputTaggedData.Pin = TEXT("Output");
            OutputTaggedData.Tags = TaggedData.Tags;
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Modifier processed %d points and modified %d attributes"),
                                  TotalProcessed, AttributesModified);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Modifier error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGAttributeModifierElement::ProcessOperationsSequential(UPCGPointData* PointData,
                                                                        const UAuracronPCGAttributeModifierSettings* Settings) const
{
    if (!PointData || !PointData->Metadata)
    {
        return;
    }

    for (const FAuracronPCGAttributeOperation& Operation : Settings->Operations)
    {
        ProcessSingleOperation(PointData, Operation, Settings);
    }
}

void FAuracronPCGAttributeModifierElement::ProcessOperationsParallel(UPCGPointData* PointData,
                                                                      const UAuracronPCGAttributeModifierSettings* Settings) const
{
    if (!PointData || !PointData->Metadata)
    {
        return;
    }

    // For parallel processing, we need to ensure thread safety
    // For now, process operations sequentially but points in parallel
    for (const FAuracronPCGAttributeOperation& Operation : Settings->Operations)
    {
        ProcessSingleOperationParallel(PointData, Operation, Settings);
    }
}

void FAuracronPCGAttributeModifierElement::ProcessSingleOperation(UPCGPointData* PointData,
                                                                   const FAuracronPCGAttributeOperation& Operation,
                                                                   const UAuracronPCGAttributeModifierSettings* Settings) const
{
    UPCGMetadata* Metadata = PointData->Metadata;
    if (!Metadata)
    {
        return;
    }

    // Check if source attribute exists
    if (!Metadata->HasAttribute(FName(*Operation.SourceAttribute)))
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Source attribute '%s' not found"), *Operation.SourceAttribute);
        return;
    }

    // Create target attribute if it doesn't exist
    if (!Metadata->HasAttribute(FName(*Operation.TargetAttribute)) && Operation.bCreateIfNotExists)
    {
        // Copy attribute structure from source
        const UPCGMetadataAttributeBase* SourceAttr = Metadata->GetConstAttribute(FName(*Operation.SourceAttribute));
        if (SourceAttr)
        {
            // Create target attribute with same type as source
            // This is a simplified implementation - in production you'd handle all types
            if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
            {
                Metadata->CreateFloatAttribute(FName(*Operation.TargetAttribute), 0.0f, true);
            }
            else if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<FVector>::Id)
            {
                Metadata->CreateVectorAttribute(FName(*Operation.TargetAttribute), FVector::ZeroVector, true);
            }
            // Add more type handling as needed
        }
    }

    // Apply operation based on type
    switch (Operation.Operation)
    {
        case EAuracronPCGAttributeAggregation::Sum:
        case EAuracronPCGAttributeAggregation::Average:
        case EAuracronPCGAttributeAggregation::Min:
        case EAuracronPCGAttributeAggregation::Max:
            ApplyAggregationOperation(PointData, Operation);
            break;
        default:
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Unsupported operation type"));
            break;
    }
}

void FAuracronPCGAttributeModifierElement::ProcessSingleOperationParallel(UPCGPointData* PointData,
                                                                           const FAuracronPCGAttributeOperation& Operation,
                                                                           const UAuracronPCGAttributeModifierSettings* Settings) const
{
    // For now, delegate to sequential processing
    // In production, you'd implement proper parallel processing with thread safety
    ProcessSingleOperation(PointData, Operation, Settings);
}

void FAuracronPCGAttributeModifierElement::ApplyAggregationOperation(UPCGPointData* PointData,
                                                                      const FAuracronPCGAttributeOperation& Operation) const
{
    // Simplified aggregation implementation
    // In production, you'd handle all attribute types and operations properly
    UPCGMetadata* Metadata = PointData->Metadata;
    if (!Metadata)
    {
        return;
    }

    const UPCGMetadataAttributeBase* SourceAttr = Metadata->GetConstAttribute(FName(*Operation.SourceAttribute));
    UPCGMetadataAttributeBase* TargetAttr = Metadata->GetMutableAttribute(FName(*Operation.TargetAttribute));

    if (!SourceAttr || !TargetAttr)
    {
        return;
    }

    // Handle float attributes as example
    if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id &&
        TargetAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
    {
        const UPCGMetadataAttribute<float>* FloatSourceAttr = static_cast<const UPCGMetadataAttribute<float>*>(SourceAttr);
        UPCGMetadataAttribute<float>* FloatTargetAttr = static_cast<UPCGMetadataAttribute<float>*>(TargetAttr);

        const TArray<FPCGPoint>& Points = PointData->GetPoints();
        for (int32 i = 0; i < Points.Num(); i++)
        {
            float SourceValue = FloatSourceAttr->GetValueFromItemKey(Points[i].MetadataEntry);
            float TargetValue = SourceValue; // Simplified - apply actual operation

            switch (Operation.Operation)
            {
                case EAuracronPCGAttributeAggregation::Sum:
                    TargetValue = SourceValue + FCString::Atof(*Operation.OperationParameter);
                    break;
                case EAuracronPCGAttributeAggregation::Average:
                    TargetValue = (SourceValue + FCString::Atof(*Operation.OperationParameter)) * 0.5f;
                    break;
                case EAuracronPCGAttributeAggregation::Min:
                    TargetValue = FMath::Min(SourceValue, FCString::Atof(*Operation.OperationParameter));
                    break;
                case EAuracronPCGAttributeAggregation::Max:
                    TargetValue = FMath::Max(SourceValue, FCString::Atof(*Operation.OperationParameter));
                    break;
                default:
                    TargetValue = SourceValue;
                    break;
            }

            FloatTargetAttr->SetValueFromItemKey(Points[i].MetadataEntry, TargetValue);
        }
    }
}

// =============================================================================
// ATTRIBUTE SYSTEM UTILITIES IMPLEMENTATION
// =============================================================================

bool UAuracronPCGAttributeSystemUtils::CreateAttribute(UPCGMetadata* Metadata, const FAuracronPCGAttributeDescriptor& Descriptor)
{
    if (!Metadata)
    {
        return false;
    }

    // Check if attribute already exists
    if (Metadata->HasAttribute(FName(*Descriptor.AttributeName)))
    {
        return false;
    }

    // Create attribute based on type
    switch (Descriptor.AttributeType)
    {
        case EAuracronPCGAttributeType::Float:
        {
            float DefaultValue = FCString::Atof(*Descriptor.DefaultValue);
            return Metadata->CreateFloatAttribute(FName(*Descriptor.AttributeName), DefaultValue, true);
        }
        case EAuracronPCGAttributeType::Vector:
        {
            FVector DefaultValue = FVector::ZeroVector;
            TArray<FString> Components;
            Descriptor.DefaultValue.ParseIntoArray(Components, TEXT(","), true);
            if (Components.Num() >= 3)
            {
                DefaultValue.X = FCString::Atof(*Components[0].TrimStartAndEnd());
                DefaultValue.Y = FCString::Atof(*Components[1].TrimStartAndEnd());
                DefaultValue.Z = FCString::Atof(*Components[2].TrimStartAndEnd());
            }
            return Metadata->CreateVectorAttribute(FName(*Descriptor.AttributeName), DefaultValue, true);
        }
        case EAuracronPCGAttributeType::String:
        {
            return Metadata->CreateStringAttribute(FName(*Descriptor.AttributeName), Descriptor.DefaultValue, false);
        }
        // Add more types as needed
        default:
            return false;
    }
}

bool UAuracronPCGAttributeSystemUtils::ValidateAttribute(const UPCGMetadata* Metadata, const FAuracronPCGAttributeDescriptor& Descriptor, TArray<FString>& ValidationErrors)
{
    ValidationErrors.Empty();

    if (!Metadata)
    {
        ValidationErrors.Add(TEXT("Metadata is null"));
        return false;
    }

    if (!Metadata->HasAttribute(FName(*Descriptor.AttributeName)))
    {
        ValidationErrors.Add(FString::Printf(TEXT("Attribute '%s' does not exist"), *Descriptor.AttributeName));
        return false;
    }

    // Validate based on validation rule
    switch (Descriptor.ValidationRule)
    {
        case EAuracronPCGAttributeValidation::Range:
        {
            // Validate range for numeric types
            if (AuracronPCGAttributeSystemUtils::IsAttributeTypeNumeric(Descriptor.AttributeType))
            {
                float Value = FCString::Atof(*Descriptor.DefaultValue);
                if (Value < Descriptor.ValueRange.X || Value > Descriptor.ValueRange.Y)
                {
                    ValidationErrors.Add(FString::Printf(TEXT("Value %f is outside range [%f, %f]"),
                                                         Value, Descriptor.ValueRange.X, Descriptor.ValueRange.Y));
                }
            }
            break;
        }
        case EAuracronPCGAttributeValidation::NotNull:
        case EAuracronPCGAttributeValidation::NotEmpty:
        {
            if (Descriptor.DefaultValue.IsEmpty())
            {
                ValidationErrors.Add(TEXT("Value cannot be empty"));
            }
            break;
        }
        case EAuracronPCGAttributeValidation::Pattern:
        {
            if (!AuracronPCGAttributeSystemUtils::MatchesPattern(Descriptor.DefaultValue, Descriptor.ValidationPattern))
            {
                ValidationErrors.Add(FString::Printf(TEXT("Value '%s' does not match pattern '%s'"),
                                                     *Descriptor.DefaultValue, *Descriptor.ValidationPattern));
            }
            break;
        }
        default:
            break;
    }

    return ValidationErrors.Num() == 0;
}

bool UAuracronPCGAttributeSystemUtils::CopyAttribute(UPCGMetadata* SourceMetadata, UPCGMetadata* TargetMetadata, const FString& AttributeName, const FString& NewAttributeName)
{
    if (!SourceMetadata || !TargetMetadata)
    {
        return false;
    }

    FName SourceAttrName(*AttributeName);
    FName TargetAttrName(NewAttributeName.IsEmpty() ? *AttributeName : *NewAttributeName);

    if (!SourceMetadata->HasAttribute(SourceAttrName))
    {
        return false;
    }

    const UPCGMetadataAttributeBase* SourceAttr = SourceMetadata->GetConstAttribute(SourceAttrName);
    if (!SourceAttr)
    {
        return false;
    }

    // Copy attribute based on type
    if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
    {
        const UPCGMetadataAttribute<float>* FloatAttr = static_cast<const UPCGMetadataAttribute<float>*>(SourceAttr);
        return TargetMetadata->CreateFloatAttribute(TargetAttrName, FloatAttr->GetValue(PCGInvalidEntryKey), FloatAttr->AllowsInterpolation());
    }
    else if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<FVector>::Id)
    {
        const UPCGMetadataAttribute<FVector>* VectorAttr = static_cast<const UPCGMetadataAttribute<FVector>*>(SourceAttr);
        return TargetMetadata->CreateVectorAttribute(TargetAttrName, VectorAttr->GetValue(PCGInvalidEntryKey), VectorAttr->AllowsInterpolation());
    }
    // Add more types as needed

    return false;
}

TArray<FString> UAuracronPCGAttributeSystemUtils::GetAttributeNames(const UPCGMetadata* Metadata, EAuracronPCGAttributeType TypeFilter)
{
    TArray<FString> AttributeNames;

    if (!Metadata)
    {
        return AttributeNames;
    }

    TArray<FName> Names;
    TArray<EPCGMetadataTypes> Types;
    Metadata->GetAttributes(Names, Types);

    for (int32 i = 0; i < Names.Num(); i++)
    {
        // Filter by type if specified
        if (TypeFilter != EAuracronPCGAttributeType::Float) // Using Float as "no filter"
        {
            // Convert EPCGMetadataTypes to EAuracronPCGAttributeType and compare
            // This is a simplified implementation
        }

        AttributeNames.Add(Names[i].ToString());
    }

    return AttributeNames;
}

EAuracronPCGAttributeType UAuracronPCGAttributeSystemUtils::GetAttributeType(const UPCGMetadata* Metadata, const FString& AttributeName)
{
    if (!Metadata || !Metadata->HasAttribute(FName(*AttributeName)))
    {
        return EAuracronPCGAttributeType::Float; // Default
    }

    const UPCGMetadataAttributeBase* Attr = Metadata->GetConstAttribute(FName(*AttributeName));
    if (!Attr)
    {
        return EAuracronPCGAttributeType::Float;
    }

    // Convert PCG type to our enum
    if (Attr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
    {
        return EAuracronPCGAttributeType::Float;
    }
    else if (Attr->GetTypeId() == PCG::Private::MetadataTypes<double>::Id)
    {
        return EAuracronPCGAttributeType::Double;
    }
    else if (Attr->GetTypeId() == PCG::Private::MetadataTypes<int32>::Id)
    {
        return EAuracronPCGAttributeType::Int32;
    }
    else if (Attr->GetTypeId() == PCG::Private::MetadataTypes<FVector>::Id)
    {
        return EAuracronPCGAttributeType::Vector;
    }
    else if (Attr->GetTypeId() == PCG::Private::MetadataTypes<FString>::Id)
    {
        return EAuracronPCGAttributeType::String;
    }
    // Add more type mappings as needed

    return EAuracronPCGAttributeType::Float;
}

// =============================================================================
// ATTRIBUTE SYSTEM UTILITY FUNCTIONS
// =============================================================================

namespace AuracronPCGAttributeSystemUtils
{
    bool IsAttributeTypeNumeric(EAuracronPCGAttributeType Type)
    {
        switch (Type)
        {
            case EAuracronPCGAttributeType::Float:
            case EAuracronPCGAttributeType::Double:
            case EAuracronPCGAttributeType::Int32:
            case EAuracronPCGAttributeType::Int64:
                return true;
            default:
                return false;
        }
    }

    bool IsAttributeTypeVector(EAuracronPCGAttributeType Type)
    {
        switch (Type)
        {
            case EAuracronPCGAttributeType::Vector2:
            case EAuracronPCGAttributeType::Vector:
            case EAuracronPCGAttributeType::Vector4:
                return true;
            default:
                return false;
        }
    }

    float InterpolateFloat(float A, float B, float Alpha, EAuracronPCGAttributeInterpolation Method, const UCurveFloat* Curve)
    {
        // Clamp alpha
        Alpha = FMath::Clamp(Alpha, 0.0f, 1.0f);

        switch (Method)
        {
            case EAuracronPCGAttributeInterpolation::Linear:
                return FMath::Lerp(A, B, Alpha);
            case EAuracronPCGAttributeInterpolation::Cubic:
                return FMath::CubicInterp(A, 0.0f, B, 0.0f, Alpha);
            case EAuracronPCGAttributeInterpolation::Smoothstep:
                return FMath::Lerp(A, B, FMath::SmoothStep(0.0f, 1.0f, Alpha));
            case EAuracronPCGAttributeInterpolation::Smootherstep:
            {
                float SmootherAlpha = Alpha * Alpha * Alpha * (Alpha * (Alpha * 6.0f - 15.0f) + 10.0f);
                return FMath::Lerp(A, B, SmootherAlpha);
            }
            case EAuracronPCGAttributeInterpolation::Curve:
                if (Curve)
                {
                    float CurveAlpha = Curve->GetFloatValue(Alpha);
                    return FMath::Lerp(A, B, CurveAlpha);
                }
                return FMath::Lerp(A, B, Alpha);
            default:
                return A;
        }
    }

    FVector InterpolateVector(const FVector& A, const FVector& B, float Alpha, EAuracronPCGAttributeInterpolation Method)
    {
        Alpha = FMath::Clamp(Alpha, 0.0f, 1.0f);

        switch (Method)
        {
            case EAuracronPCGAttributeInterpolation::Linear:
                return FMath::Lerp(A, B, Alpha);
            case EAuracronPCGAttributeInterpolation::Cubic:
                return FMath::CubicInterp(A, FVector::ZeroVector, B, FVector::ZeroVector, Alpha);
            case EAuracronPCGAttributeInterpolation::Smoothstep:
                return FMath::Lerp(A, B, FMath::SmoothStep(0.0f, 1.0f, Alpha));
            case EAuracronPCGAttributeInterpolation::Smootherstep:
            {
                float SmootherAlpha = Alpha * Alpha * Alpha * (Alpha * (Alpha * 6.0f - 15.0f) + 10.0f);
                return FMath::Lerp(A, B, SmootherAlpha);
            }
            default:
                return A;
        }
    }

    bool MatchesPattern(const FString& Text, const FString& Pattern)
    {
        // Simplified pattern matching - in production you'd use proper regex
        if (Pattern == TEXT(".*"))
        {
            return true;
        }

        if (Pattern.Contains(TEXT("*")))
        {
            // Simple wildcard matching
            FString PatternCopy = Pattern;
            PatternCopy = PatternCopy.Replace(TEXT("*"), TEXT(""));
            return Text.Contains(PatternCopy);
        }

        return Text == Pattern;
    }

    TArray<FString> ParseAttributeList(const FString& AttributeListString)
    {
        TArray<FString> AttributeList;
        AttributeListString.ParseIntoArray(AttributeList, TEXT(","), true);

        // Trim whitespace
        for (FString& Attribute : AttributeList)
        {
            Attribute = Attribute.TrimStartAndEnd();
        }

        return AttributeList;
    }

    FString FormatAttributeValue(const FString& Value, EAuracronPCGAttributeType Type)
    {
        switch (Type)
        {
            case EAuracronPCGAttributeType::Float:
            case EAuracronPCGAttributeType::Double:
            {
                float FloatValue = FCString::Atof(*Value);
                return FString::Printf(TEXT("%.3f"), FloatValue);
            }
            case EAuracronPCGAttributeType::Int32:
            case EAuracronPCGAttributeType::Int64:
            {
                int32 IntValue = FCString::Atoi(*Value);
                return FString::Printf(TEXT("%d"), IntValue);
            }
            default:
                return Value;
        }
    }

    void OptimizeMetadataStorage(UPCGMetadata* Metadata)
    {
        if (!Metadata)
        {
            return;
        }

        // Simplified optimization - in production you'd implement proper optimization
        // This could include compacting sparse attributes, removing unused entries, etc.
    }
}
