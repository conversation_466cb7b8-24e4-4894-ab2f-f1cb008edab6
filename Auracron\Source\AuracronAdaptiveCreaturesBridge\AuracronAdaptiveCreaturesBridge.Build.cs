using UnrealBuildTool;

public class AuracronAdaptiveCreaturesBridge : ModuleRules
{
    public AuracronAdaptiveCreaturesBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "AIModule",
            "GameplayTasks",
            "NavigationSystem",
            "MassEntity",
            "MassEntityTestSuite",
            "MassGameplay",
            "MassMovement",
            "MassNavigation",
            "MassReplication",
            "MassSimulation",
            "MassSpawner",
            "StateTreeModule",
            "StateTreeEditorModule",
            "GameplayStateTreeModule",
            "StructUtils",
            "SmartObjectsModule",
            "ZoneGraph",
            "ZoneGraphAnnotations",
            "Perception",
            "BehaviorTreeModule",
            "BlackboardModule",
            "UMG",
            "Slate",
            "SlateCore",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "PythonScriptPlugin"
        });

        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "RenderCore",
            "RHI",
            "Renderer",
            "Chaos",
            "ChaosCore",
            "PhysicsCore",
            "GeometryCollectionEngine",
            "FieldSystemEngine",
            "Niagara",
            "NiagaraCore",
            "NiagaraShader",
            "CinematicCamera",
            "LevelSequence",
            "MovieScene",
            "MovieSceneTracks",
            "AnimGraphRuntime",
            "AnimationCore",
            "AnimationBlueprintLibrary"
        });

        // Enable RTTI for this module
        bUseRTTI = true;
        
        // Enable exceptions for this module
        bEnableExceptions = true;
        
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronAdaptiveCreaturesBridge/Public"
        });
        
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronAdaptiveCreaturesBridge/Private"
        });
    }
}
