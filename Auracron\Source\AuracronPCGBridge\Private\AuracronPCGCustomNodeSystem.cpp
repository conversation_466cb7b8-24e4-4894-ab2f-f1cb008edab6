// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Custom Node System Implementation
// Bridge 2.13: PCG Framework - Custom Node Creation

#include "AuracronPCGCustomNodeSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// =============================================================================
// CUSTOM NODE FACTORY IMPLEMENTATION
// =============================================================================

TMap<FString, FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeFactory::RegisteredTemplates;

UClass* UAuracronPCGCustomNodeFactory::CreateCustomNodeClass(const FAuracronPCGCustomNodeTemplate& Template)
{
    // Validate template first
    FString ErrorMessage;
    if (!ValidateTemplate(Template, ErrorMessage))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create custom node class: %s"), *ErrorMessage);
        return nullptr;
    }

    // In production, you'd create a dynamic class based on the template
    // For now, return a base class
    return UPCGSettings::StaticClass();
}

UPCGSettings* UAuracronPCGCustomNodeFactory::CreateCustomNodeInstance(const FAuracronPCGCustomNodeTemplate& Template)
{
    UClass* NodeClass = CreateCustomNodeClass(Template);
    if (!NodeClass)
    {
        return nullptr;
    }

    UPCGSettings* NodeInstance = NewObject<UPCGSettings>(GetTransientPackage(), NodeClass);
    if (NodeInstance)
    {
        // Configure the node instance based on template
        ConfigureNodeInstance(NodeInstance, Template);
    }

    return NodeInstance;
}

bool UAuracronPCGCustomNodeFactory::RegisterCustomNodeTemplate(const FAuracronPCGCustomNodeTemplate& Template)
{
    FString ErrorMessage;
    if (!ValidateTemplate(Template, ErrorMessage))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to register template '%s': %s"), *Template.TemplateName, *ErrorMessage);
        return false;
    }

    RegisteredTemplates.Add(Template.TemplateName, Template);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered custom node template: %s"), *Template.TemplateName);
    return true;
}

bool UAuracronPCGCustomNodeFactory::UnregisterCustomNodeTemplate(const FString& TemplateName)
{
    if (RegisteredTemplates.Contains(TemplateName))
    {
        RegisteredTemplates.Remove(TemplateName);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Unregistered custom node template: %s"), *TemplateName);
        return true;
    }

    return false;
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeFactory::GetRegisteredTemplates()
{
    TArray<FAuracronPCGCustomNodeTemplate> Templates;
    RegisteredTemplates.GenerateValueArray(Templates);
    return Templates;
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeFactory::GetTemplate(const FString& TemplateName)
{
    if (RegisteredTemplates.Contains(TemplateName))
    {
        return RegisteredTemplates[TemplateName];
    }

    return FAuracronPCGCustomNodeTemplate();
}

bool UAuracronPCGCustomNodeFactory::ValidateTemplate(const FAuracronPCGCustomNodeTemplate& Template, FString& OutErrorMessage)
{
    // Validate template name
    if (Template.TemplateName.IsEmpty())
    {
        OutErrorMessage = TEXT("Template name cannot be empty");
        return false;
    }

    // Validate display name
    if (Template.DisplayName.IsEmpty())
    {
        OutErrorMessage = TEXT("Display name cannot be empty");
        return false;
    }

    // Validate parameters
    for (const FAuracronPCGCustomParameterDescriptor& Parameter : Template.Parameters)
    {
        if (!ValidateParameter(Parameter, OutErrorMessage))
        {
            return false;
        }
    }

    // Validate pins
    for (const FAuracronPCGCustomPinDescriptor& Pin : Template.InputPins)
    {
        if (!ValidatePin(Pin, OutErrorMessage))
        {
            return false;
        }
    }

    for (const FAuracronPCGCustomPinDescriptor& Pin : Template.OutputPins)
    {
        if (!ValidatePin(Pin, OutErrorMessage))
        {
            return false;
        }
    }

    // Validate execution function
    if (Template.ExecutionFunction.IsEmpty() && !Template.BlueprintImplementation.IsValid() && Template.NativeClassName.IsEmpty())
    {
        OutErrorMessage = TEXT("Template must have either execution function, blueprint implementation, or native class name");
        return false;
    }

    return true;
}

TArray<FString> UAuracronPCGCustomNodeFactory::GetTemplateValidationErrors(const FAuracronPCGCustomNodeTemplate& Template)
{
    TArray<FString> Errors;

    // Check template name
    if (Template.TemplateName.IsEmpty())
    {
        Errors.Add(TEXT("Template name cannot be empty"));
    }

    // Check display name
    if (Template.DisplayName.IsEmpty())
    {
        Errors.Add(TEXT("Display name cannot be empty"));
    }

    // Check parameters
    for (int32 i = 0; i < Template.Parameters.Num(); i++)
    {
        const FAuracronPCGCustomParameterDescriptor& Parameter = Template.Parameters[i];
        FString ParameterError;
        if (!ValidateParameter(Parameter, ParameterError))
        {
            Errors.Add(FString::Printf(TEXT("Parameter %d: %s"), i, *ParameterError));
        }
    }

    // Check pins
    for (int32 i = 0; i < Template.InputPins.Num(); i++)
    {
        const FAuracronPCGCustomPinDescriptor& Pin = Template.InputPins[i];
        FString PinError;
        if (!ValidatePin(Pin, PinError))
        {
            Errors.Add(FString::Printf(TEXT("Input Pin %d: %s"), i, *PinError));
        }
    }

    for (int32 i = 0; i < Template.OutputPins.Num(); i++)
    {
        const FAuracronPCGCustomPinDescriptor& Pin = Template.OutputPins[i];
        FString PinError;
        if (!ValidatePin(Pin, PinError))
        {
            Errors.Add(FString::Printf(TEXT("Output Pin %d: %s"), i, *PinError));
        }
    }

    // Check execution
    if (Template.ExecutionFunction.IsEmpty() && !Template.BlueprintImplementation.IsValid() && Template.NativeClassName.IsEmpty())
    {
        Errors.Add(TEXT("Template must have either execution function, blueprint implementation, or native class name"));
    }

    return Errors;
}

UBlueprint* UAuracronPCGCustomNodeFactory::CreateBlueprintFromTemplate(const FAuracronPCGCustomNodeTemplate& Template)
{
    // In production, you'd create a blueprint class based on the template
    // This is a simplified implementation
    return nullptr;
}

bool UAuracronPCGCustomNodeFactory::CompileBlueprintNode(UBlueprint* Blueprint)
{
    if (!Blueprint)
    {
        return false;
    }

    // In production, you'd compile the blueprint
    // FBlueprintEditorUtils::RefreshAllNodes(Blueprint);
    // FKismetEditorUtilities::CompileBlueprint(Blueprint);
    
    return true;
}

bool UAuracronPCGCustomNodeFactory::SaveTemplateToFile(const FAuracronPCGCustomNodeTemplate& Template, const FString& FilePath)
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    
    // Serialize template to JSON
    SerializeTemplateToJson(Template, JsonObject);
    
    // Write to file
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    return FFileHelper::SaveStringToFile(OutputString, *FilePath);
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeFactory::LoadTemplateFromFile(const FString& FilePath)
{
    FAuracronPCGCustomNodeTemplate Template;
    
    FString FileContent;
    if (FFileHelper::LoadFileToString(FileContent, *FilePath))
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);
        
        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            DeserializeTemplateFromJson(JsonObject, Template);
        }
    }
    
    return Template;
}

TArray<FString> UAuracronPCGCustomNodeFactory::GetAvailableTemplateFiles(const FString& Directory)
{
    TArray<FString> TemplateFiles;
    IFileManager::Get().FindFiles(TemplateFiles, *(Directory / TEXT("*.json")), true, false);
    return TemplateFiles;
}

// Helper functions
void UAuracronPCGCustomNodeFactory::ConfigureNodeInstance(UPCGSettings* NodeInstance, const FAuracronPCGCustomNodeTemplate& Template)
{
    if (!NodeInstance)
    {
        return;
    }

    // Configure node metadata (simplified)
    // In production, you'd set actual properties based on template parameters
}

bool UAuracronPCGCustomNodeFactory::ValidateParameter(const FAuracronPCGCustomParameterDescriptor& Parameter, FString& OutErrorMessage)
{
    if (Parameter.ParameterName.IsEmpty())
    {
        OutErrorMessage = TEXT("Parameter name cannot be empty");
        return false;
    }

    if (Parameter.bHasMinValue && Parameter.bHasMaxValue && Parameter.MinValue > Parameter.MaxValue)
    {
        OutErrorMessage = TEXT("Parameter min value cannot be greater than max value");
        return false;
    }

    return true;
}

bool UAuracronPCGCustomNodeFactory::ValidatePin(const FAuracronPCGCustomPinDescriptor& Pin, FString& OutErrorMessage)
{
    if (Pin.PinName.IsEmpty())
    {
        OutErrorMessage = TEXT("Pin name cannot be empty");
        return false;
    }

    return true;
}

void UAuracronPCGCustomNodeFactory::SerializeTemplateToJson(const FAuracronPCGCustomNodeTemplate& Template, TSharedPtr<FJsonObject> JsonObject)
{
    JsonObject->SetStringField(TEXT("TemplateName"), Template.TemplateName);
    JsonObject->SetStringField(TEXT("DisplayName"), Template.DisplayName);
    JsonObject->SetStringField(TEXT("Description"), Template.Description);
    JsonObject->SetNumberField(TEXT("TemplateType"), static_cast<int32>(Template.TemplateType));
    JsonObject->SetNumberField(TEXT("Category"), static_cast<int32>(Template.Category));
    
    // Serialize tags
    TArray<TSharedPtr<FJsonValue>> TagsArray;
    for (const FString& Tag : Template.Tags)
    {
        TagsArray.Add(MakeShareable(new FJsonValueString(Tag)));
    }
    JsonObject->SetArrayField(TEXT("Tags"), TagsArray);
    
    // Serialize parameters
    TArray<TSharedPtr<FJsonValue>> ParametersArray;
    for (const FAuracronPCGCustomParameterDescriptor& Parameter : Template.Parameters)
    {
        TSharedPtr<FJsonObject> ParameterObject = MakeShareable(new FJsonObject);
        SerializeParameterToJson(Parameter, ParameterObject);
        ParametersArray.Add(MakeShareable(new FJsonValueObject(ParameterObject)));
    }
    JsonObject->SetArrayField(TEXT("Parameters"), ParametersArray);
    
    // Add other fields as needed...
}

void UAuracronPCGCustomNodeFactory::DeserializeTemplateFromJson(TSharedPtr<FJsonObject> JsonObject, FAuracronPCGCustomNodeTemplate& Template)
{
    Template.TemplateName = JsonObject->GetStringField(TEXT("TemplateName"));
    Template.DisplayName = JsonObject->GetStringField(TEXT("DisplayName"));
    Template.Description = JsonObject->GetStringField(TEXT("Description"));
    Template.TemplateType = static_cast<EAuracronPCGCustomNodeTemplateType>(JsonObject->GetIntegerField(TEXT("TemplateType")));
    Template.Category = static_cast<EAuracronPCGNodeCategory>(JsonObject->GetIntegerField(TEXT("Category")));
    
    // Deserialize tags
    const TArray<TSharedPtr<FJsonValue>>* TagsArray;
    if (JsonObject->TryGetArrayField(TEXT("Tags"), TagsArray))
    {
        for (const TSharedPtr<FJsonValue>& TagValue : *TagsArray)
        {
            Template.Tags.Add(TagValue->AsString());
        }
    }
    
    // Deserialize parameters
    const TArray<TSharedPtr<FJsonValue>>* ParametersArray;
    if (JsonObject->TryGetArrayField(TEXT("Parameters"), ParametersArray))
    {
        for (const TSharedPtr<FJsonValue>& ParameterValue : *ParametersArray)
        {
            FAuracronPCGCustomParameterDescriptor Parameter;
            DeserializeParameterFromJson(ParameterValue->AsObject(), Parameter);
            Template.Parameters.Add(Parameter);
        }
    }
    
    // Add other fields as needed...
}

void UAuracronPCGCustomNodeFactory::SerializeParameterToJson(const FAuracronPCGCustomParameterDescriptor& Parameter, TSharedPtr<FJsonObject> JsonObject)
{
    JsonObject->SetStringField(TEXT("ParameterName"), Parameter.ParameterName);
    JsonObject->SetStringField(TEXT("DisplayName"), Parameter.DisplayName);
    JsonObject->SetStringField(TEXT("Description"), Parameter.Description);
    JsonObject->SetNumberField(TEXT("ParameterType"), static_cast<int32>(Parameter.ParameterType));
    JsonObject->SetStringField(TEXT("DefaultValue"), Parameter.DefaultValue);
    JsonObject->SetBoolField(TEXT("bIsRequired"), Parameter.bIsRequired);
    JsonObject->SetBoolField(TEXT("bHasMinValue"), Parameter.bHasMinValue);
    JsonObject->SetNumberField(TEXT("MinValue"), Parameter.MinValue);
    JsonObject->SetBoolField(TEXT("bHasMaxValue"), Parameter.bHasMaxValue);
    JsonObject->SetNumberField(TEXT("MaxValue"), Parameter.MaxValue);
    JsonObject->SetBoolField(TEXT("bIsAdvanced"), Parameter.bIsAdvanced);
    JsonObject->SetBoolField(TEXT("bIsHidden"), Parameter.bIsHidden);
    JsonObject->SetStringField(TEXT("Category"), Parameter.Category);
    JsonObject->SetStringField(TEXT("Tooltip"), Parameter.Tooltip);
}

void UAuracronPCGCustomNodeFactory::DeserializeParameterFromJson(TSharedPtr<FJsonObject> JsonObject, FAuracronPCGCustomParameterDescriptor& Parameter)
{
    Parameter.ParameterName = JsonObject->GetStringField(TEXT("ParameterName"));
    Parameter.DisplayName = JsonObject->GetStringField(TEXT("DisplayName"));
    Parameter.Description = JsonObject->GetStringField(TEXT("Description"));
    Parameter.ParameterType = static_cast<EAuracronPCGCustomParameterType>(JsonObject->GetIntegerField(TEXT("ParameterType")));
    Parameter.DefaultValue = JsonObject->GetStringField(TEXT("DefaultValue"));
    Parameter.bIsRequired = JsonObject->GetBoolField(TEXT("bIsRequired"));
    Parameter.bHasMinValue = JsonObject->GetBoolField(TEXT("bHasMinValue"));
    Parameter.MinValue = JsonObject->GetNumberField(TEXT("MinValue"));
    Parameter.bHasMaxValue = JsonObject->GetBoolField(TEXT("bHasMaxValue"));
    Parameter.MaxValue = JsonObject->GetNumberField(TEXT("MaxValue"));
    Parameter.bIsAdvanced = JsonObject->GetBoolField(TEXT("bIsAdvanced"));
    Parameter.bIsHidden = JsonObject->GetBoolField(TEXT("bIsHidden"));
    Parameter.Category = JsonObject->GetStringField(TEXT("Category"));
    Parameter.Tooltip = JsonObject->GetStringField(TEXT("Tooltip"));
}

// =============================================================================
// CUSTOM NODE BUILDER IMPLEMENTATION
// =============================================================================

UAuracronPCGCustomNodeBuilder::UAuracronPCGCustomNodeBuilder()
{
    Reset();
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetNodeName(const FString& Name)
{
    CurrentTemplate.TemplateName = Name;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetDisplayName(const FString& DisplayName)
{
    CurrentTemplate.DisplayName = DisplayName;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetDescription(const FString& Description)
{
    CurrentTemplate.Description = Description;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetTemplateType(EAuracronPCGCustomNodeTemplateType TemplateType)
{
    CurrentTemplate.TemplateType = TemplateType;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetCategory(EAuracronPCGNodeCategory Category)
{
    CurrentTemplate.Category = Category;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::AddTag(const FString& Tag)
{
    CurrentTemplate.Tags.AddUnique(Tag);
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetNodeColor(const FLinearColor& Color)
{
    CurrentTemplate.NodeColor = Color;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::AddParameter(const FAuracronPCGCustomParameterDescriptor& Parameter)
{
    CurrentTemplate.Parameters.Add(Parameter);
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::AddInputPin(const FAuracronPCGCustomPinDescriptor& Pin)
{
    FAuracronPCGCustomPinDescriptor InputPin = Pin;
    InputPin.bIsInput = true;
    CurrentTemplate.InputPins.Add(InputPin);
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::AddOutputPin(const FAuracronPCGCustomPinDescriptor& Pin)
{
    FAuracronPCGCustomPinDescriptor OutputPin = Pin;
    OutputPin.bIsInput = false;
    CurrentTemplate.OutputPins.Add(OutputPin);
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetExecutionMode(EAuracronPCGCustomNodeExecutionMode ExecutionMode)
{
    CurrentTemplate.ExecutionMode = ExecutionMode;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetExecutionFunction(const FString& FunctionName)
{
    CurrentTemplate.ExecutionFunction = FunctionName;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetBlueprintImplementation(UBlueprint* Blueprint)
{
    CurrentTemplate.BlueprintImplementation = Blueprint;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetNativeClassName(const FString& ClassName)
{
    CurrentTemplate.NativeClassName = ClassName;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetValidationLevel(EAuracronPCGCustomNodeValidationLevel ValidationLevel)
{
    CurrentTemplate.ValidationLevel = ValidationLevel;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetAuthor(const FString& Author)
{
    CurrentTemplate.Author = Author;
    return this;
}

UAuracronPCGCustomNodeBuilder* UAuracronPCGCustomNodeBuilder::SetVersion(const FString& Version)
{
    CurrentTemplate.Version = Version;
    return this;
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeBuilder::BuildTemplate()
{
    return CurrentTemplate;
}

UClass* UAuracronPCGCustomNodeBuilder::BuildNodeClass()
{
    return UAuracronPCGCustomNodeFactory::CreateCustomNodeClass(CurrentTemplate);
}

UPCGSettings* UAuracronPCGCustomNodeBuilder::BuildNodeInstance()
{
    return UAuracronPCGCustomNodeFactory::CreateCustomNodeInstance(CurrentTemplate);
}

void UAuracronPCGCustomNodeBuilder::Reset()
{
    CurrentTemplate = FAuracronPCGCustomNodeTemplate();
}

// =============================================================================
// CUSTOM NODE REGISTRY IMPLEMENTATION
// =============================================================================

UAuracronPCGCustomNodeRegistry* UAuracronPCGCustomNodeRegistry::Instance = nullptr;

UAuracronPCGCustomNodeRegistry* UAuracronPCGCustomNodeRegistry::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGCustomNodeRegistry>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

bool UAuracronPCGCustomNodeRegistry::RegisterTemplate(const FAuracronPCGCustomNodeTemplate& Template)
{
    FString ErrorMessage;
    if (!UAuracronPCGCustomNodeFactory::ValidateTemplate(Template, ErrorMessage))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to register template '%s': %s"), *Template.TemplateName, *ErrorMessage);
        return false;
    }

    Templates.Add(Template.TemplateName, Template);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered custom node template: %s"), *Template.TemplateName);
    return true;
}

bool UAuracronPCGCustomNodeRegistry::UnregisterTemplate(const FString& TemplateName)
{
    if (Templates.Contains(TemplateName))
    {
        Templates.Remove(TemplateName);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Unregistered custom node template: %s"), *TemplateName);
        return true;
    }

    return false;
}

bool UAuracronPCGCustomNodeRegistry::IsTemplateRegistered(const FString& TemplateName) const
{
    return Templates.Contains(TemplateName);
}

FAuracronPCGCustomNodeTemplate UAuracronPCGCustomNodeRegistry::GetTemplate(const FString& TemplateName) const
{
    if (Templates.Contains(TemplateName))
    {
        return Templates[TemplateName];
    }

    return FAuracronPCGCustomNodeTemplate();
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeRegistry::GetAllTemplates() const
{
    TArray<FAuracronPCGCustomNodeTemplate> AllTemplates;
    Templates.GenerateValueArray(AllTemplates);
    return AllTemplates;
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeRegistry::GetTemplatesByCategory(EAuracronPCGNodeCategory Category) const
{
    TArray<FAuracronPCGCustomNodeTemplate> FilteredTemplates;

    for (const auto& TemplatePair : Templates)
    {
        if (TemplatePair.Value.Category == Category)
        {
            FilteredTemplates.Add(TemplatePair.Value);
        }
    }

    return FilteredTemplates;
}

TArray<FAuracronPCGCustomNodeTemplate> UAuracronPCGCustomNodeRegistry::GetTemplatesByType(EAuracronPCGCustomNodeTemplateType TemplateType) const
{
    TArray<FAuracronPCGCustomNodeTemplate> FilteredTemplates;

    for (const auto& TemplatePair : Templates)
    {
        if (TemplatePair.Value.TemplateType == TemplateType)
        {
            FilteredTemplates.Add(TemplatePair.Value);
        }
    }

    return FilteredTemplates;
}

TArray<FString> UAuracronPCGCustomNodeRegistry::GetTemplateNames() const
{
    TArray<FString> TemplateNames;
    Templates.GetKeys(TemplateNames);
    return TemplateNames;
}

UPCGSettings* UAuracronPCGCustomNodeRegistry::CreateNodeInstance(const FString& TemplateName)
{
    if (!Templates.Contains(TemplateName))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Template '%s' not found in registry"), *TemplateName);
        return nullptr;
    }

    const FAuracronPCGCustomNodeTemplate& Template = Templates[TemplateName];
    return UAuracronPCGCustomNodeFactory::CreateCustomNodeInstance(Template);
}

bool UAuracronPCGCustomNodeRegistry::RegisterNodeInstance(const FString& InstanceName, UPCGSettings* NodeInstance)
{
    if (!NodeInstance)
    {
        return false;
    }

    NodeInstances.Add(InstanceName, NodeInstance);
    return true;
}

UPCGSettings* UAuracronPCGCustomNodeRegistry::GetNodeInstance(const FString& InstanceName) const
{
    if (NodeInstances.Contains(InstanceName))
    {
        return NodeInstances[InstanceName];
    }

    return nullptr;
}

bool UAuracronPCGCustomNodeRegistry::ValidateAllTemplates(TArray<FString>& OutErrors) const
{
    OutErrors.Empty();

    for (const auto& TemplatePair : Templates)
    {
        TArray<FString> TemplateErrors = UAuracronPCGCustomNodeFactory::GetTemplateValidationErrors(TemplatePair.Value);
        for (const FString& Error : TemplateErrors)
        {
            OutErrors.Add(FString::Printf(TEXT("Template '%s': %s"), *TemplatePair.Key, *Error));
        }
    }

    return OutErrors.Num() == 0;
}

void UAuracronPCGCustomNodeRegistry::RefreshRegistry()
{
    // Refresh all templates and instances
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Refreshing custom node registry"));

    // Validate all templates
    TArray<FString> ValidationErrors;
    ValidateAllTemplates(ValidationErrors);

    if (ValidationErrors.Num() > 0)
    {
        for (const FString& Error : ValidationErrors)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Validation error: %s"), *Error);
        }
    }
}

void UAuracronPCGCustomNodeRegistry::ClearRegistry()
{
    Templates.Empty();
    NodeInstances.Empty();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleared custom node registry"));
}
