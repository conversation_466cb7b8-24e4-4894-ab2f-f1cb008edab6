// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Testing Framework Implementation
// Bridge 2.18: PCG Framework - Testing e Validation

#include "AuracronPCGTestingFramework.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformProcess.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/ScopedTimers.h"

// =============================================================================
// TEST RUNNER IMPLEMENTATION
// =============================================================================

UAuracronPCGTestRunner* UAuracronPCGTestRunner::Instance = nullptr;

UAuracronPCGTestRunner* UAuracronPCGTestRunner::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGTestRunner>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronPCGTestRunner::RunTestSuite(const FAuracronPCGTestSuite& TestSuite)
{
    if (!TestSuite.bEnabled)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Test suite '%s' is disabled"), *TestSuite.SuiteName);
        return;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Running test suite: %s"), *TestSuite.SuiteName);
    
    TArray<FAuracronPCGTestResult> SuiteResults;
    bIsRunning = true;

    for (const FAuracronPCGTestCase& TestCase : TestSuite.TestCases)
    {
        if (!TestCase.bEnabled)
        {
            continue;
        }

        // Check if we should stop on first failure
        if (Configuration.bStopOnFirstFailure && SuiteResults.Num() > 0)
        {
            bool bHasFailure = false;
            for (const FAuracronPCGTestResult& Result : SuiteResults)
            {
                if (Result.Result == EAuracronPCGTestResult::Failed || Result.Result == EAuracronPCGTestResult::Error)
                {
                    bHasFailure = true;
                    break;
                }
            }
            if (bHasFailure)
            {
                AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Stopping test suite execution due to failure"));
                break;
            }
        }

        FAuracronPCGTestResult Result = ExecuteTestCase(TestCase);
        SuiteResults.Add(Result);
        TestResults.Add(Result);
        
        OnTestCompleted.Broadcast(TestCase.TestName, Result);
    }

    bIsRunning = false;
    OnTestSuiteCompleted.Broadcast(TestSuite.SuiteName, SuiteResults);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Test suite '%s' completed: %d tests run"), 
                              *TestSuite.SuiteName, SuiteResults.Num());
}

void UAuracronPCGTestRunner::RunTestCase(const FAuracronPCGTestCase& TestCase)
{
    if (!TestCase.bEnabled)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Test case '%s' is disabled"), *TestCase.TestName);
        return;
    }

    FAuracronPCGTestResult Result = ExecuteTestCase(TestCase);
    TestResults.Add(Result);
    
    OnTestCompleted.Broadcast(TestCase.TestName, Result);
}

FAuracronPCGTestResult UAuracronPCGTestRunner::ExecuteTestCase(const FAuracronPCGTestCase& TestCase)
{
    FAuracronPCGTestResult Result;
    Result.TestName = TestCase.TestName;
    Result.StartTime = FDateTime::Now();
    Result.Result = EAuracronPCGTestResult::Running;

    OnTestStarted.Broadcast(TestCase.TestName, TestCase.TestType);

    // Validate test case
    if (!ValidateTestCase(TestCase))
    {
        Result.Result = EAuracronPCGTestResult::Error;
        Result.ErrorMessage = TEXT("Test case validation failed");
        Result.EndTime = FDateTime::Now();
        Result.ExecutionTimeSeconds = (Result.EndTime - Result.StartTime).GetTotalSeconds();
        return Result;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Executing test: %s"), *TestCase.TestName);

    try
    {
        // Measure memory before test
        FPlatformMemoryStats MemoryStatsBefore = FPlatformMemory::GetStats();
        
        // Execute test based on type
        bool bTestPassed = false;
        switch (TestCase.TestType)
        {
            case EAuracronPCGTestType::UnitTest:
                bTestPassed = ExecuteUnitTest(TestCase);
                break;
            case EAuracronPCGTestType::IntegrationTest:
                bTestPassed = ExecuteIntegrationTest(TestCase);
                break;
            case EAuracronPCGTestType::PerformanceTest:
                bTestPassed = ExecutePerformanceTest(TestCase);
                break;
            case EAuracronPCGTestType::ValidationTest:
                bTestPassed = ExecuteValidationTest(TestCase);
                break;
            case EAuracronPCGTestType::RegressionTest:
                bTestPassed = ExecuteRegressionTest(TestCase);
                break;
            case EAuracronPCGTestType::StressTest:
                bTestPassed = ExecuteStressTest(TestCase);
                break;
            default:
                bTestPassed = ExecuteFunctionalTest(TestCase);
                break;
        }

        // Measure memory after test
        FPlatformMemoryStats MemoryStatsAfter = FPlatformMemory::GetStats();
        Result.MemoryUsageMB = static_cast<float>(MemoryStatsAfter.UsedPhysical - MemoryStatsBefore.UsedPhysical) / (1024.0f * 1024.0f);

        Result.Result = bTestPassed ? EAuracronPCGTestResult::Passed : EAuracronPCGTestResult::Failed;
        
        if (!bTestPassed && Result.ErrorMessage.IsEmpty())
        {
            Result.ErrorMessage = TEXT("Test failed without specific error message");
        }
    }
    catch (const std::exception& e)
    {
        Result.Result = EAuracronPCGTestResult::Error;
        Result.ErrorMessage = FString::Printf(TEXT("Exception during test execution: %s"), UTF8_TO_TCHAR(e.what()));
    }
    catch (...)
    {
        Result.Result = EAuracronPCGTestResult::Error;
        Result.ErrorMessage = TEXT("Unknown exception during test execution");
    }

    Result.EndTime = FDateTime::Now();
    Result.ExecutionTimeSeconds = (Result.EndTime - Result.StartTime).GetTotalSeconds();

    // Check for timeout
    if (Result.ExecutionTimeSeconds > TestCase.TimeoutSeconds)
    {
        Result.Result = EAuracronPCGTestResult::Timeout;
        Result.ErrorMessage = FString::Printf(TEXT("Test timed out after %.2f seconds"), Result.ExecutionTimeSeconds);
    }

    LogTestResult(Result);
    return Result;
}

bool UAuracronPCGTestRunner::ValidateTestCase(const FAuracronPCGTestCase& TestCase)
{
    if (TestCase.TestName.IsEmpty())
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Test case has empty name"));
        return false;
    }

    if (TestCase.TimeoutSeconds <= 0.0f)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Test case '%s' has invalid timeout"), *TestCase.TestName);
        return false;
    }

    return true;
}

void UAuracronPCGTestRunner::LogTestResult(const FAuracronPCGTestResult& Result)
{
    FString ResultString;
    switch (Result.Result)
    {
        case EAuracronPCGTestResult::Passed:
            ResultString = TEXT("PASSED");
            break;
        case EAuracronPCGTestResult::Failed:
            ResultString = TEXT("FAILED");
            break;
        case EAuracronPCGTestResult::Error:
            ResultString = TEXT("ERROR");
            break;
        case EAuracronPCGTestResult::Timeout:
            ResultString = TEXT("TIMEOUT");
            break;
        case EAuracronPCGTestResult::Skipped:
            ResultString = TEXT("SKIPPED");
            break;
        default:
            ResultString = TEXT("UNKNOWN");
            break;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Test '%s': %s (%.3fs, %.2fMB)"), 
                              *Result.TestName, *ResultString, Result.ExecutionTimeSeconds, Result.MemoryUsageMB);

    if (!Result.ErrorMessage.IsEmpty())
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Test '%s' error: %s"), *Result.TestName, *Result.ErrorMessage);
    }

    for (const FString& Warning : Result.WarningMessages)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Test '%s' warning: %s"), *Result.TestName, *Warning);
    }
}

void UAuracronPCGTestRunner::RunAllTests()
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Running all registered test suites"));
    
    for (const FAuracronPCGTestSuite& TestSuite : RegisteredTestSuites)
    {
        RunTestSuite(TestSuite);
    }
}

void UAuracronPCGTestRunner::RunTestsByType(EAuracronPCGTestType TestType)
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Running tests by type: %d"), (int32)TestType);
    
    for (const FAuracronPCGTestSuite& TestSuite : RegisteredTestSuites)
    {
        FAuracronPCGTestSuite FilteredSuite = TestSuite;
        FilteredSuite.TestCases.RemoveAll([TestType](const FAuracronPCGTestCase& TestCase)
        {
            return TestCase.TestType != TestType;
        });
        
        if (FilteredSuite.TestCases.Num() > 0)
        {
            RunTestSuite(FilteredSuite);
        }
    }
}

void UAuracronPCGTestRunner::RunTestsByCategory(const FString& Category)
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Running tests by category: %s"), *Category);
    
    for (const FAuracronPCGTestSuite& TestSuite : RegisteredTestSuites)
    {
        FAuracronPCGTestSuite FilteredSuite = TestSuite;
        FilteredSuite.TestCases.RemoveAll([Category](const FAuracronPCGTestCase& TestCase)
        {
            return TestCase.Category != Category;
        });
        
        if (FilteredSuite.TestCases.Num() > 0)
        {
            RunTestSuite(FilteredSuite);
        }
    }
}

void UAuracronPCGTestRunner::RegisterTestSuite(const FAuracronPCGTestSuite& TestSuite)
{
    RegisteredTestSuites.Add(TestSuite);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Registered test suite: %s (%d tests)"), 
                              *TestSuite.SuiteName, TestSuite.TestCases.Num());
}

void UAuracronPCGTestRunner::UnregisterTestSuite(const FString& SuiteName)
{
    RegisteredTestSuites.RemoveAll([SuiteName](const FAuracronPCGTestSuite& TestSuite)
    {
        return TestSuite.SuiteName == SuiteName;
    });
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Unregistered test suite: %s"), *SuiteName);
}

TArray<FAuracronPCGTestResult> UAuracronPCGTestRunner::GetTestResults() const
{
    return TestResults;
}

TArray<FAuracronPCGTestResult> UAuracronPCGTestRunner::GetFailedTests() const
{
    TArray<FAuracronPCGTestResult> FailedTests;
    for (const FAuracronPCGTestResult& Result : TestResults)
    {
        if (Result.Result == EAuracronPCGTestResult::Failed || 
            Result.Result == EAuracronPCGTestResult::Error ||
            Result.Result == EAuracronPCGTestResult::Timeout)
        {
            FailedTests.Add(Result);
        }
    }
    return FailedTests;
}

TArray<FAuracronPCGTestResult> UAuracronPCGTestRunner::GetPassedTests() const
{
    TArray<FAuracronPCGTestResult> PassedTests;
    for (const FAuracronPCGTestResult& Result : TestResults)
    {
        if (Result.Result == EAuracronPCGTestResult::Passed)
        {
            PassedTests.Add(Result);
        }
    }
    return PassedTests;
}

int32 UAuracronPCGTestRunner::GetTotalTestCount() const
{
    return TestResults.Num();
}

int32 UAuracronPCGTestRunner::GetPassedTestCount() const
{
    return GetPassedTests().Num();
}

int32 UAuracronPCGTestRunner::GetFailedTestCount() const
{
    return GetFailedTests().Num();
}

float UAuracronPCGTestRunner::GetSuccessRate() const
{
    int32 TotalTests = GetTotalTestCount();
    if (TotalTests == 0)
    {
        return 0.0f;
    }
    
    return static_cast<float>(GetPassedTestCount()) / static_cast<float>(TotalTests);
}

float UAuracronPCGTestRunner::GetTotalExecutionTime() const
{
    float TotalTime = 0.0f;
    for (const FAuracronPCGTestResult& Result : TestResults)
    {
        TotalTime += Result.ExecutionTimeSeconds;
    }
    return TotalTime;
}

// =============================================================================
// PERFORMANCE BENCHMARK IMPLEMENTATION
// =============================================================================

FAuracronPCGPerformanceMetrics UAuracronPCGPerformanceBenchmark::BenchmarkNode(UPCGSettings* NodeSettings, int32 Iterations)
{
    FAuracronPCGPerformanceMetrics Metrics;

    if (!NodeSettings || Iterations <= 0)
    {
        return Metrics;
    }

    TArray<float> ExecutionTimes;
    TArray<float> MemoryUsages;

    for (int32 i = 0; i < Iterations; i++)
    {
        FPlatformMemoryStats MemoryBefore = FPlatformMemory::GetStats();

        float ExecutionTime = MeasureExecutionTime([NodeSettings]()
        {
            // Simulate node execution
            FPlatformProcess::Sleep(FMath::RandRange(0.01f, 0.1f)); // 10-100ms
        });

        FPlatformMemoryStats MemoryAfter = FPlatformMemory::GetStats();
        float MemoryUsage = static_cast<float>(MemoryAfter.UsedPhysical - MemoryBefore.UsedPhysical) / (1024.0f * 1024.0f);

        ExecutionTimes.Add(ExecutionTime * 1000.0f); // Convert to ms
        MemoryUsages.Add(MemoryUsage);
    }

    return CalculateStatistics(ExecutionTimes, MemoryUsages);
}

FAuracronPCGPerformanceMetrics UAuracronPCGPerformanceBenchmark::BenchmarkGraph(UPCGGraph* Graph, int32 Iterations)
{
    FAuracronPCGPerformanceMetrics Metrics;

    if (!Graph || Iterations <= 0)
    {
        return Metrics;
    }

    TArray<float> ExecutionTimes;
    TArray<float> MemoryUsages;

    for (int32 i = 0; i < Iterations; i++)
    {
        FPlatformMemoryStats MemoryBefore = FPlatformMemory::GetStats();

        float ExecutionTime = MeasureExecutionTime([Graph]()
        {
            // Simulate graph execution
            FPlatformProcess::Sleep(FMath::RandRange(0.05f, 0.5f)); // 50-500ms
        });

        FPlatformMemoryStats MemoryAfter = FPlatformMemory::GetStats();
        float MemoryUsage = static_cast<float>(MemoryAfter.UsedPhysical - MemoryBefore.UsedPhysical) / (1024.0f * 1024.0f);

        ExecutionTimes.Add(ExecutionTime * 1000.0f); // Convert to ms
        MemoryUsages.Add(MemoryUsage);
    }

    return CalculateStatistics(ExecutionTimes, MemoryUsages);
}

float UAuracronPCGPerformanceBenchmark::MeasureExecutionTime(TFunction<void()> TestFunction)
{
    FDateTime StartTime = FDateTime::Now();
    TestFunction();
    FDateTime EndTime = FDateTime::Now();

    return (EndTime - StartTime).GetTotalSeconds();
}

FAuracronPCGPerformanceMetrics UAuracronPCGPerformanceBenchmark::CalculateStatistics(const TArray<float>& ExecutionTimes, const TArray<float>& MemoryUsages)
{
    FAuracronPCGPerformanceMetrics Metrics;

    if (ExecutionTimes.Num() == 0)
    {
        return Metrics;
    }

    // Calculate execution time statistics
    float TotalTime = 0.0f;
    float MinTime = ExecutionTimes[0];
    float MaxTime = ExecutionTimes[0];

    for (float Time : ExecutionTimes)
    {
        TotalTime += Time;
        MinTime = FMath::Min(MinTime, Time);
        MaxTime = FMath::Max(MaxTime, Time);
    }

    Metrics.AverageExecutionTimeMs = TotalTime / ExecutionTimes.Num();
    Metrics.MinExecutionTimeMs = MinTime;
    Metrics.MaxExecutionTimeMs = MaxTime;

    // Calculate standard deviation
    float VarianceSum = 0.0f;
    for (float Time : ExecutionTimes)
    {
        float Diff = Time - Metrics.AverageExecutionTimeMs;
        VarianceSum += Diff * Diff;
    }
    Metrics.StandardDeviationMs = FMath::Sqrt(VarianceSum / ExecutionTimes.Num());

    // Calculate memory statistics
    if (MemoryUsages.Num() > 0)
    {
        float TotalMemory = 0.0f;
        float PeakMemory = MemoryUsages[0];

        for (float Memory : MemoryUsages)
        {
            TotalMemory += Memory;
            PeakMemory = FMath::Max(PeakMemory, Memory);
        }

        Metrics.AverageMemoryUsageMB = TotalMemory / MemoryUsages.Num();
        Metrics.PeakMemoryUsageMB = PeakMemory;
    }

    // Calculate throughput (simplified)
    if (Metrics.AverageExecutionTimeMs > 0.0f)
    {
        Metrics.PointsPerSecond = 1000.0f / Metrics.AverageExecutionTimeMs; // Simplified calculation
        Metrics.NodesPerSecond = 1000.0f / Metrics.AverageExecutionTimeMs;
    }

    Metrics.TotalIterations = ExecutionTimes.Num();
    Metrics.SuccessfulIterations = ExecutionTimes.Num(); // Assume all successful for now
    Metrics.SuccessRate = 1.0f;

    return Metrics;
}

void UAuracronPCGPerformanceBenchmark::StartPerformanceProfiling(const FString& ProfileName)
{
    ProfilingStartTimes.Add(ProfileName, FDateTime::Now());
}

void UAuracronPCGPerformanceBenchmark::StopPerformanceProfiling(const FString& ProfileName)
{
    FDateTime* StartTime = ProfilingStartTimes.Find(ProfileName);
    if (StartTime)
    {
        FDateTime EndTime = FDateTime::Now();
        float ElapsedTime = (EndTime - *StartTime).GetTotalSeconds();
        ProfiledTimes.Add(ProfileName, ElapsedTime);
        ProfilingStartTimes.Remove(ProfileName);
    }
}

float UAuracronPCGPerformanceBenchmark::GetProfiledTime(const FString& ProfileName) const
{
    const float* Time = ProfiledTimes.Find(ProfileName);
    return Time ? *Time : 0.0f;
}

TMap<FString, float> UAuracronPCGPerformanceBenchmark::GetAllProfiledTimes() const
{
    return ProfiledTimes;
}
