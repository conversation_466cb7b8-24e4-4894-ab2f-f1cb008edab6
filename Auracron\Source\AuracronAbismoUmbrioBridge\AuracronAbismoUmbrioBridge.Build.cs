using UnrealBuildTool;

public class AuracronAbismoUmbrioBridge : ModuleRules
{
    public AuracronAbismoUmbrioBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "PCG",
            "PCGGeometryScriptInterop",
            "GeometryScriptingCore",
            "GeometryFramework",
            "DynamicMesh",
            "MeshDescription",
            "StaticMeshDescription",
            "MeshConversion",
            "ModelingComponents",
            "ModelingOperators",
            "Landscape",
            "LandscapeEditor",
            "RenderCore",
            "RHI",
            "Renderer",
            "NavigationSystem",
            "AIModule",
            "GameplayTasks",
            "UMG",
            "Slate",
            "SlateCore",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "PythonScriptPlugin"
        });

        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "Chaos",
            "ChaosCore",
            "PhysicsCore",
            "GeometryCollectionEngine",
            "FieldSystemEngine",
            "Niagara",
            "NiagaraCore",
            "NiagaraShader",
            "CinematicCamera",
            "LevelSequence",
            "MovieScene",
            "MovieSceneTracks"
        });

        // Enable RTTI for this module
        bUseRTTI = true;
        
        // Enable exceptions for this module
        bEnableExceptions = true;
        
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronAbismoUmbrioBridge/Public"
        });
        
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronAbismoUmbrioBridge/Private"
        });
    }
}
