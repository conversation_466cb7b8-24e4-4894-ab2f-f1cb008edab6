// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Async Processing Implementation
// Bridge 2.16: PCG Framework - Async Processing

#include "AuracronPCGAsyncProcessing.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "HAL/PlatformProcess.h"
#include "HAL/PlatformMemory.h"
#include "Misc/DateTime.h"
#include "Misc/Guid.h"
#include "Stats/Stats.h"

// =============================================================================
// PROGRESS TRACKER IMPLEMENTATION
// =============================================================================

void UAuracronPCGProgressTracker::StartTracking(const FString& TaskId, const FString& TaskName, int32 TotalItems)
{
    FScopeLock Lock(&ProgressMapLock);
    
    FAuracronPCGProgressInfo& ProgressInfo = ProgressMap.FindOrAdd(TaskId);
    ProgressInfo.TaskId = TaskId;
    ProgressInfo.TaskName = TaskName;
    ProgressInfo.TaskState = EAuracronPCGTaskState::Running;
    ProgressInfo.TotalItems = TotalItems;
    ProgressInfo.ItemsProcessed = 0;
    ProgressInfo.ProgressPercentage = 0.0f;
    ProgressInfo.ElapsedTime = 0.0f;
    ProgressInfo.EstimatedTimeRemaining = 0.0f;
    ProgressInfo.CurrentOperation = TEXT("Starting...");
    ProgressInfo.StatusMessage = TEXT("Task started");
    ProgressInfo.bHasErrors = false;
    ProgressInfo.ErrorMessages.Empty();
    ProgressInfo.WarningMessages.Empty();
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Started tracking task: %s (%s) with %d items"), *TaskId, *TaskName, TotalItems);
}

void UAuracronPCGProgressTracker::UpdateProgress(const FString& TaskId, int32 ItemsProcessed, const FString& CurrentOperation)
{
    FScopeLock Lock(&ProgressMapLock);
    
    FAuracronPCGProgressInfo* ProgressInfo = ProgressMap.Find(TaskId);
    if (!ProgressInfo)
    {
        return;
    }
    
    ProgressInfo->ItemsProcessed = ItemsProcessed;
    ProgressInfo->ProgressPercentage = ProgressInfo->TotalItems > 0 ? 
        (static_cast<float>(ItemsProcessed) / static_cast<float>(ProgressInfo->TotalItems)) * 100.0f : 0.0f;
    
    if (!CurrentOperation.IsEmpty())
    {
        ProgressInfo->CurrentOperation = CurrentOperation;
    }
    
    UpdatePerformanceMetrics(TaskId);
    ProgressInfo->EstimatedTimeRemaining = CalculateEstimatedTimeRemaining(*ProgressInfo);
}

void UAuracronPCGProgressTracker::SetTaskState(const FString& TaskId, EAuracronPCGTaskState NewState)
{
    FScopeLock Lock(&ProgressMapLock);
    
    FAuracronPCGProgressInfo* ProgressInfo = ProgressMap.Find(TaskId);
    if (!ProgressInfo)
    {
        return;
    }
    
    ProgressInfo->TaskState = NewState;
    
    switch (NewState)
    {
        case EAuracronPCGTaskState::Completed:
            ProgressInfo->ProgressPercentage = 100.0f;
            ProgressInfo->StatusMessage = TEXT("Task completed successfully");
            ProgressInfo->EstimatedTimeRemaining = 0.0f;
            break;
        case EAuracronPCGTaskState::Failed:
            ProgressInfo->StatusMessage = TEXT("Task failed");
            ProgressInfo->bHasErrors = true;
            ProgressInfo->EstimatedTimeRemaining = 0.0f;
            break;
        case EAuracronPCGTaskState::Cancelled:
            ProgressInfo->StatusMessage = TEXT("Task cancelled");
            ProgressInfo->EstimatedTimeRemaining = 0.0f;
            break;
        case EAuracronPCGTaskState::Paused:
            ProgressInfo->StatusMessage = TEXT("Task paused");
            break;
        case EAuracronPCGTaskState::Running:
            ProgressInfo->StatusMessage = TEXT("Task running");
            break;
        default:
            break;
    }
}

void UAuracronPCGProgressTracker::AddError(const FString& TaskId, const FString& ErrorMessage)
{
    FScopeLock Lock(&ProgressMapLock);
    
    FAuracronPCGProgressInfo* ProgressInfo = ProgressMap.Find(TaskId);
    if (!ProgressInfo)
    {
        return;
    }
    
    ProgressInfo->bHasErrors = true;
    ProgressInfo->ErrorMessages.Add(ErrorMessage);
    
    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Task %s error: %s"), *TaskId, *ErrorMessage);
}

void UAuracronPCGProgressTracker::AddWarning(const FString& TaskId, const FString& WarningMessage)
{
    FScopeLock Lock(&ProgressMapLock);
    
    FAuracronPCGProgressInfo* ProgressInfo = ProgressMap.Find(TaskId);
    if (!ProgressInfo)
    {
        return;
    }
    
    ProgressInfo->WarningMessages.Add(WarningMessage);
    
    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Task %s warning: %s"), *TaskId, *WarningMessage);
}

void UAuracronPCGProgressTracker::FinishTracking(const FString& TaskId, bool bSuccess)
{
    FScopeLock Lock(&ProgressMapLock);
    
    FAuracronPCGProgressInfo* ProgressInfo = ProgressMap.Find(TaskId);
    if (!ProgressInfo)
    {
        return;
    }
    
    ProgressInfo->TaskState = bSuccess ? EAuracronPCGTaskState::Completed : EAuracronPCGTaskState::Failed;
    ProgressInfo->ProgressPercentage = bSuccess ? 100.0f : ProgressInfo->ProgressPercentage;
    ProgressInfo->EstimatedTimeRemaining = 0.0f;
    
    UpdatePerformanceMetrics(TaskId);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Finished tracking task: %s (Success: %s)"), *TaskId, bSuccess ? TEXT("Yes") : TEXT("No"));
}

FAuracronPCGProgressInfo UAuracronPCGProgressTracker::GetProgressInfo(const FString& TaskId) const
{
    FScopeLock Lock(&ProgressMapLock);
    
    const FAuracronPCGProgressInfo* ProgressInfo = ProgressMap.Find(TaskId);
    if (ProgressInfo)
    {
        return *ProgressInfo;
    }
    
    return FAuracronPCGProgressInfo();
}

TArray<FAuracronPCGProgressInfo> UAuracronPCGProgressTracker::GetAllProgressInfo() const
{
    FScopeLock Lock(&ProgressMapLock);
    
    TArray<FAuracronPCGProgressInfo> AllProgress;
    for (const auto& ProgressPair : ProgressMap)
    {
        AllProgress.Add(ProgressPair.Value);
    }
    
    return AllProgress;
}

bool UAuracronPCGProgressTracker::IsTaskActive(const FString& TaskId) const
{
    FScopeLock Lock(&ProgressMapLock);
    
    const FAuracronPCGProgressInfo* ProgressInfo = ProgressMap.Find(TaskId);
    if (ProgressInfo)
    {
        return ProgressInfo->TaskState == EAuracronPCGTaskState::Running || 
               ProgressInfo->TaskState == EAuracronPCGTaskState::Paused;
    }
    
    return false;
}

float UAuracronPCGProgressTracker::GetOverallProgress() const
{
    FScopeLock Lock(&ProgressMapLock);
    
    if (ProgressMap.Num() == 0)
    {
        return 0.0f;
    }
    
    float TotalProgress = 0.0f;
    for (const auto& ProgressPair : ProgressMap)
    {
        TotalProgress += ProgressPair.Value.ProgressPercentage;
    }
    
    return TotalProgress / static_cast<float>(ProgressMap.Num());
}

int32 UAuracronPCGProgressTracker::GetActiveTaskCount() const
{
    FScopeLock Lock(&ProgressMapLock);
    
    int32 ActiveCount = 0;
    for (const auto& ProgressPair : ProgressMap)
    {
        if (ProgressPair.Value.TaskState == EAuracronPCGTaskState::Running || 
            ProgressPair.Value.TaskState == EAuracronPCGTaskState::Paused)
        {
            ActiveCount++;
        }
    }
    
    return ActiveCount;
}

void UAuracronPCGProgressTracker::ClearCompletedTasks()
{
    FScopeLock Lock(&ProgressMapLock);
    
    TArray<FString> TasksToRemove;
    for (const auto& ProgressPair : ProgressMap)
    {
        if (ProgressPair.Value.TaskState == EAuracronPCGTaskState::Completed || 
            ProgressPair.Value.TaskState == EAuracronPCGTaskState::Failed ||
            ProgressPair.Value.TaskState == EAuracronPCGTaskState::Cancelled)
        {
            TasksToRemove.Add(ProgressPair.Key);
        }
    }
    
    for (const FString& TaskId : TasksToRemove)
    {
        ProgressMap.Remove(TaskId);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleared %d completed tasks"), TasksToRemove.Num());
}

void UAuracronPCGProgressTracker::ClearAllTasks()
{
    FScopeLock Lock(&ProgressMapLock);
    
    int32 TaskCount = ProgressMap.Num();
    ProgressMap.Empty();
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleared all %d tasks"), TaskCount);
}

void UAuracronPCGProgressTracker::RemoveTask(const FString& TaskId)
{
    FScopeLock Lock(&ProgressMapLock);
    
    if (ProgressMap.Remove(TaskId) > 0)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Removed task: %s"), *TaskId);
    }
}

void UAuracronPCGProgressTracker::UpdatePerformanceMetrics(const FString& TaskId)
{
    FAuracronPCGProgressInfo* ProgressInfo = ProgressMap.Find(TaskId);
    if (!ProgressInfo)
    {
        return;
    }
    
    // Calculate elapsed time (simplified - in production you'd track actual start time)
    ProgressInfo->ElapsedTime += 0.1f; // Assume 100ms update interval
    
    // Calculate items per second
    if (ProgressInfo->ElapsedTime > 0.0f)
    {
        ProgressInfo->ItemsPerSecond = static_cast<float>(ProgressInfo->ItemsProcessed) / ProgressInfo->ElapsedTime;
    }
    
    // Estimate memory usage (simplified)
    ProgressInfo->MemoryUsageMB = static_cast<float>(ProgressInfo->ItemsProcessed) * 0.001f; // 1KB per item estimate
    
    // Active threads (simplified)
    ProgressInfo->ActiveThreads = FMath::Min(4, FMath::Max(1, ProgressInfo->ItemsProcessed / 1000));
}

float UAuracronPCGProgressTracker::CalculateEstimatedTimeRemaining(const FAuracronPCGProgressInfo& ProgressInfo) const
{
    if (ProgressInfo.ItemsPerSecond <= 0.0f || ProgressInfo.ItemsProcessed >= ProgressInfo.TotalItems)
    {
        return 0.0f;
    }
    
    int32 RemainingItems = ProgressInfo.TotalItems - ProgressInfo.ItemsProcessed;
    return static_cast<float>(RemainingItems) / ProgressInfo.ItemsPerSecond;
}

// =============================================================================
// ASYNC TASK BASE IMPLEMENTATION
// =============================================================================

FAuracronPCGAsyncTaskBase::FAuracronPCGAsyncTaskBase(const FAuracronPCGAsyncTaskDescriptor& InDescriptor)
    : TaskId(FGuid::NewGuid().ToString())
    , Descriptor(InDescriptor)
    , TaskState(EAuracronPCGTaskState::Pending)
    , bIsCancelled(false)
    , bIsPaused(false)
    , ProgressTracker(nullptr)
{
    StartTime = FDateTime::Now();
    TaskResult.TaskId = TaskId;

    // Get progress tracker instance
    ProgressTracker = NewObject<UAuracronPCGProgressTracker>();

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Created async task: %s (%s)"), *TaskId, *Descriptor.TaskName);
}

FAuracronPCGAsyncTaskBase::~FAuracronPCGAsyncTaskBase()
{
    if (ProgressTracker)
    {
        ProgressTracker->RemoveTask(TaskId);
    }
}

void FAuracronPCGAsyncTaskBase::Cancel()
{
    if (Descriptor.bAllowCancellation)
    {
        bIsCancelled = true;
        SetTaskState(EAuracronPCGTaskState::Cancelled);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Task cancelled: %s"), *TaskId);
    }
}

void FAuracronPCGAsyncTaskBase::Pause()
{
    if (!bIsCancelled.Load())
    {
        bIsPaused = true;
        SetTaskState(EAuracronPCGTaskState::Paused);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Task paused: %s"), *TaskId);
    }
}

void FAuracronPCGAsyncTaskBase::Resume()
{
    if (bIsPaused.Load() && !bIsCancelled.Load())
    {
        bIsPaused = false;
        SetTaskState(EAuracronPCGTaskState::Running);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Task resumed: %s"), *TaskId);
    }
}

void FAuracronPCGAsyncTaskBase::UpdateProgress(int32 ItemsProcessed, const FString& CurrentOperation)
{
    if (ProgressTracker && Descriptor.bTrackProgress)
    {
        ProgressTracker->UpdateProgress(TaskId, ItemsProcessed, CurrentOperation);

        if (Descriptor.bReportProgress)
        {
            FAuracronPCGProgressInfo ProgressInfo = ProgressTracker->GetProgressInfo(TaskId);
            TaskResult.ItemsProcessed = ProgressInfo.ItemsProcessed;
        }
    }
}

FAuracronPCGProgressInfo FAuracronPCGAsyncTaskBase::GetProgressInfo() const
{
    if (ProgressTracker)
    {
        return ProgressTracker->GetProgressInfo(TaskId);
    }

    return FAuracronPCGProgressInfo();
}

void FAuracronPCGAsyncTaskBase::SetTaskState(EAuracronPCGTaskState NewState)
{
    FScopeLock Lock(&TaskLock);

    TaskState = NewState;
    TaskResult.FinalState = NewState;

    if (ProgressTracker)
    {
        ProgressTracker->SetTaskState(TaskId, NewState);
    }

    if (NewState == EAuracronPCGTaskState::Completed || NewState == EAuracronPCGTaskState::Failed)
    {
        EndTime = FDateTime::Now();
        TaskResult.TotalExecutionTime = (EndTime - StartTime).GetTotalSeconds();
        TaskResult.bSuccess = (NewState == EAuracronPCGTaskState::Completed);

        if (ProgressTracker)
        {
            ProgressTracker->FinishTracking(TaskId, TaskResult.bSuccess);
        }
    }
}

void FAuracronPCGAsyncTaskBase::AddError(const FString& ErrorMessage)
{
    FScopeLock Lock(&TaskLock);

    TaskResult.ErrorMessage = ErrorMessage;

    if (ProgressTracker)
    {
        ProgressTracker->AddError(TaskId, ErrorMessage);
    }
}

void FAuracronPCGAsyncTaskBase::AddWarning(const FString& WarningMessage)
{
    FScopeLock Lock(&TaskLock);

    TaskResult.WarningMessages.Add(WarningMessage);

    if (ProgressTracker)
    {
        ProgressTracker->AddWarning(TaskId, WarningMessage);
    }
}

bool FAuracronPCGAsyncTaskBase::ShouldContinueExecution() const
{
    return !bIsCancelled.Load() && TaskState.Load() != EAuracronPCGTaskState::Failed;
}

void FAuracronPCGAsyncTaskBase::WaitIfPaused()
{
    while (bIsPaused.Load() && !bIsCancelled.Load())
    {
        FPlatformProcess::Sleep(0.01f); // 10ms sleep
    }
}

// =============================================================================
// MEMORY POOL MANAGER IMPLEMENTATION
// =============================================================================

UAuracronPCGMemoryPoolManager* UAuracronPCGMemoryPoolManager::Instance = nullptr;

UAuracronPCGMemoryPoolManager* UAuracronPCGMemoryPoolManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGMemoryPoolManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronPCGMemoryPoolManager::InitializePool(int32 PoolSizeMB)
{
    FScopeLock Lock(&PoolLock);

    if (bIsInitialized)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Memory pool already initialized"));
        return;
    }

    PoolSizeMB = PoolSizeMB;
    InitializeMemoryPool();
    bIsInitialized = true;

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Memory pool initialized with %d MB"), PoolSizeMB);
}

void UAuracronPCGMemoryPoolManager::ShutdownPool()
{
    FScopeLock Lock(&PoolLock);

    if (!bIsInitialized)
    {
        return;
    }

    CleanupMemoryPool();
    bIsInitialized = false;

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Memory pool shutdown"));
}

bool UAuracronPCGMemoryPoolManager::IsPoolInitialized() const
{
    FScopeLock Lock(&PoolLock);
    return bIsInitialized;
}

void* UAuracronPCGMemoryPoolManager::AllocateMemory(int32 SizeBytes)
{
    FScopeLock Lock(&PoolLock);

    if (!bIsInitialized || SizeBytes <= 0)
    {
        return nullptr;
    }

    // Simplified allocation - in production you'd implement proper memory pool allocation
    void* Memory = FMemory::Malloc(SizeBytes);
    if (Memory)
    {
        AllocatedBlocks.Add(Memory, SizeBytes);
        UsedMemoryBytes += SizeBytes;
        AllocationCount++;

        if (bMemoryTrackingEnabled)
        {
            FString AllocKey = FString::Printf(TEXT("Alloc_%d"), AllocationCount);
            MemoryTrackingMap.Add(AllocKey, SizeBytes);
        }
    }

    return Memory;
}

void UAuracronPCGMemoryPoolManager::DeallocateMemory(void* Memory)
{
    FScopeLock Lock(&PoolLock);

    if (!Memory || !bIsInitialized)
    {
        return;
    }

    int32* SizePtr = AllocatedBlocks.Find(Memory);
    if (SizePtr)
    {
        UsedMemoryBytes -= *SizePtr;
        AllocatedBlocks.Remove(Memory);
        FMemory::Free(Memory);
    }
}

void* UAuracronPCGMemoryPoolManager::ReallocateMemory(void* Memory, int32 NewSizeBytes)
{
    FScopeLock Lock(&PoolLock);

    if (!bIsInitialized)
    {
        return nullptr;
    }

    if (!Memory)
    {
        return AllocateMemory(NewSizeBytes);
    }

    int32* OldSizePtr = AllocatedBlocks.Find(Memory);
    if (!OldSizePtr)
    {
        return nullptr;
    }

    void* NewMemory = FMemory::Realloc(Memory, NewSizeBytes);
    if (NewMemory)
    {
        AllocatedBlocks.Remove(Memory);
        AllocatedBlocks.Add(NewMemory, NewSizeBytes);
        UsedMemoryBytes = UsedMemoryBytes - *OldSizePtr + NewSizeBytes;
    }

    return NewMemory;
}

int32 UAuracronPCGMemoryPoolManager::GetTotalPoolSize() const
{
    FScopeLock Lock(&PoolLock);
    return PoolSizeMB;
}

int32 UAuracronPCGMemoryPoolManager::GetUsedMemory() const
{
    FScopeLock Lock(&PoolLock);
    return UsedMemoryBytes / (1024 * 1024); // Convert to MB
}

int32 UAuracronPCGMemoryPoolManager::GetFreeMemory() const
{
    FScopeLock Lock(&PoolLock);
    return PoolSizeMB - GetUsedMemory();
}

float UAuracronPCGMemoryPoolManager::GetMemoryUsagePercentage() const
{
    FScopeLock Lock(&PoolLock);
    if (PoolSizeMB == 0)
    {
        return 0.0f;
    }
    return (static_cast<float>(GetUsedMemory()) / static_cast<float>(PoolSizeMB)) * 100.0f;
}

int32 UAuracronPCGMemoryPoolManager::GetAllocationCount() const
{
    FScopeLock Lock(&PoolLock);
    return AllocationCount;
}

void UAuracronPCGMemoryPoolManager::EnableMemoryTracking(bool bEnabled)
{
    FScopeLock Lock(&PoolLock);
    bMemoryTrackingEnabled = bEnabled;

    if (!bEnabled)
    {
        MemoryTrackingMap.Empty();
    }
}

TMap<FString, int32> UAuracronPCGMemoryPoolManager::GetMemoryTrackingInfo() const
{
    FScopeLock Lock(&PoolLock);
    return MemoryTrackingMap;
}

void UAuracronPCGMemoryPoolManager::ClearMemoryTrackingInfo()
{
    FScopeLock Lock(&PoolLock);
    MemoryTrackingMap.Empty();
}

void UAuracronPCGMemoryPoolManager::InitializeMemoryPool()
{
    // Simplified initialization - in production you'd allocate actual memory pool
    MemoryPool = nullptr; // Would allocate large block here
    FreeBlocks.Empty();
    AllocatedBlocks.Empty();
    UsedMemoryBytes = 0;
    AllocationCount = 0;
}

void UAuracronPCGMemoryPoolManager::CleanupMemoryPool()
{
    // Free all allocated blocks
    for (const auto& AllocPair : AllocatedBlocks)
    {
        FMemory::Free(AllocPair.Key);
    }

    AllocatedBlocks.Empty();
    FreeBlocks.Empty();
    MemoryTrackingMap.Empty();

    if (MemoryPool)
    {
        FMemory::Free(MemoryPool);
        MemoryPool = nullptr;
    }

    UsedMemoryBytes = 0;
    AllocationCount = 0;
}
