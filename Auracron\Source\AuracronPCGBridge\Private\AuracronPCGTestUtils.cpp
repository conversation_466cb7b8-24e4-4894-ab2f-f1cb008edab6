// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Test Utilities Implementation
// Bridge 2.18: PCG Framework - Testing e Validation

#include "AuracronPCGTestingFramework.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Misc/Paths.h"
#include "HAL/PlatformFilemanager.h"

// =============================================================================
// TEST UTILITIES IMPLEMENTATION
// =============================================================================

TMap<FString, FDateTime> UAuracronPCGTestUtils::TestTimers;

UPCGPointData* UAuracronPCGTestUtils::GenerateTestPointData(int32 PointCount, const FBox& Bounds)
{
    UPCGPointData* PointData = NewObject<UPCGPointData>();
    
    TArray<FPCGPoint>& Points = PointData->GetMutablePoints();
    Points.Reserve(PointCount);
    
    for (int32 i = 0; i < PointCount; i++)
    {
        FPCGPoint Point;
        
        // Generate random position within bounds
        Point.Transform.SetLocation(FVector(
            FMath::RandRange(Bounds.Min.X, Bounds.Max.X),
            FMath::RandRange(Bounds.Min.Y, Bounds.Max.Y),
            FMath::RandRange(Bounds.Min.Z, Bounds.Max.Z)
        ));
        
        // Generate random rotation
        Point.Transform.SetRotation(FQuat(FRotator(
            FMath::RandRange(-180.0f, 180.0f),
            FMath::RandRange(-180.0f, 180.0f),
            FMath::RandRange(-180.0f, 180.0f)
        )));
        
        // Generate random scale
        float Scale = FMath::RandRange(0.5f, 2.0f);
        Point.Transform.SetScale3D(FVector(Scale));
        
        // Set random density
        Point.Density = FMath::RandRange(0.1f, 1.0f);
        
        // Set random color
        Point.Color = FVector4(
            FMath::RandRange(0.0f, 1.0f),
            FMath::RandRange(0.0f, 1.0f),
            FMath::RandRange(0.0f, 1.0f),
            1.0f
        );
        
        Points.Add(Point);
    }
    
    return PointData;
}

UPCGSpatialData* UAuracronPCGTestUtils::GenerateTestSpatialData(const FBox& Bounds)
{
    // Create a simple spatial data object
    UPCGSpatialData* SpatialData = NewObject<UPCGSpatialData>();
    
    // Note: In a real implementation, you would set up the spatial data properly
    // This is a simplified version for testing purposes
    
    return SpatialData;
}

UPCGGraph* UAuracronPCGTestUtils::CreateTestGraph(int32 NodeCount)
{
    UPCGGraph* Graph = NewObject<UPCGGraph>();
    
    // Note: In a real implementation, you would create and connect actual PCG nodes
    // This is a simplified version for testing purposes
    
    return Graph;
}

UPCGSettings* UAuracronPCGTestUtils::CreateTestNodeSettings(const FString& NodeType)
{
    // Create a basic PCG settings object
    UPCGSettings* Settings = NewObject<UPCGSettings>();
    
    // Note: In a real implementation, you would configure the settings based on NodeType
    // This is a simplified version for testing purposes
    
    return Settings;
}

bool UAuracronPCGTestUtils::AssertEqual(float Expected, float Actual, float Tolerance)
{
    float Difference = FMath::Abs(Expected - Actual);
    bool bResult = Difference <= Tolerance;
    
    if (!bResult)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("AssertEqual failed: Expected %.6f, Actual %.6f, Difference %.6f > Tolerance %.6f"), 
                                  Expected, Actual, Difference, Tolerance);
    }
    
    return bResult;
}

bool UAuracronPCGTestUtils::AssertVectorEqual(const FVector& Expected, const FVector& Actual, float Tolerance)
{
    bool bResult = FVector::Dist(Expected, Actual) <= Tolerance;
    
    if (!bResult)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("AssertVectorEqual failed: Expected %s, Actual %s, Distance %.6f > Tolerance %.6f"), 
                                  *Expected.ToString(), *Actual.ToString(), FVector::Dist(Expected, Actual), Tolerance);
    }
    
    return bResult;
}

bool UAuracronPCGTestUtils::AssertNotNull(UObject* Object)
{
    bool bResult = Object != nullptr;
    
    if (!bResult)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("AssertNotNull failed: Object is null"));
    }
    
    return bResult;
}

bool UAuracronPCGTestUtils::AssertArraySizeEqual(int32 Expected, int32 Actual)
{
    bool bResult = Expected == Actual;
    
    if (!bResult)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("AssertArraySizeEqual failed: Expected %d, Actual %d"), Expected, Actual);
    }
    
    return bResult;
}

bool UAuracronPCGTestUtils::AssertTrue(bool Condition, const FString& Message)
{
    if (!Condition)
    {
        FString ErrorMessage = Message.IsEmpty() ? TEXT("AssertTrue failed") : FString::Printf(TEXT("AssertTrue failed: %s"), *Message);
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("%s"), *ErrorMessage);
    }
    
    return Condition;
}

bool UAuracronPCGTestUtils::AssertFalse(bool Condition, const FString& Message)
{
    if (Condition)
    {
        FString ErrorMessage = Message.IsEmpty() ? TEXT("AssertFalse failed") : FString::Printf(TEXT("AssertFalse failed: %s"), *Message);
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("%s"), *ErrorMessage);
    }
    
    return !Condition;
}

FString UAuracronPCGTestUtils::GetTestDataDirectory()
{
    return FPaths::ProjectDir() / TEXT("Content/Tests/PCG/Data");
}

FString UAuracronPCGTestUtils::GetTestOutputDirectory()
{
    return FPaths::ProjectSavedDir() / TEXT("Tests/PCG/Output");
}

void UAuracronPCGTestUtils::CleanupTestData()
{
    FString TestOutputDir = GetTestOutputDirectory();
    
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (PlatformFile.DirectoryExists(*TestOutputDir))
    {
        PlatformFile.DeleteDirectoryRecursively(*TestOutputDir);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleaned up test output directory: %s"), *TestOutputDir);
    }
}

void UAuracronPCGTestUtils::SetupTestEnvironment()
{
    // Create test directories
    FString TestDataDir = GetTestDataDirectory();
    FString TestOutputDir = GetTestOutputDirectory();
    
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    if (!PlatformFile.DirectoryExists(*TestDataDir))
    {
        PlatformFile.CreateDirectoryTree(*TestDataDir);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Created test data directory: %s"), *TestDataDir);
    }
    
    if (!PlatformFile.DirectoryExists(*TestOutputDir))
    {
        PlatformFile.CreateDirectoryTree(*TestOutputDir);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Created test output directory: %s"), *TestOutputDir);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Test environment setup completed"));
}

void UAuracronPCGTestUtils::TeardownTestEnvironment()
{
    CleanupTestData();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Test environment teardown completed"));
}

UPCGSettings* UAuracronPCGTestUtils::CreateMockNodeSettings()
{
    UPCGSettings* MockSettings = NewObject<UPCGSettings>();
    
    // Configure mock settings with default values
    // Note: In a real implementation, you would set up realistic mock data
    
    return MockSettings;
}

UPCGData* UAuracronPCGTestUtils::CreateMockPCGData()
{
    // Create mock point data as the most common PCG data type
    return GenerateTestPointData(100, FBox(FVector(-100, -100, 0), FVector(100, 100, 100)));
}

UPCGGraph* UAuracronPCGTestUtils::CreateMockPCGGraph()
{
    return CreateTestGraph(5); // Create a graph with 5 nodes
}

void UAuracronPCGTestUtils::StartTimer(const FString& TimerName)
{
    TestTimers.Add(TimerName, FDateTime::Now());
}

float UAuracronPCGTestUtils::StopTimer(const FString& TimerName)
{
    FDateTime* StartTime = TestTimers.Find(TimerName);
    if (StartTime)
    {
        FDateTime EndTime = FDateTime::Now();
        float ElapsedTime = (EndTime - *StartTime).GetTotalSeconds();
        TestTimers.Remove(TimerName);
        return ElapsedTime;
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Timer '%s' was not found or already stopped"), *TimerName);
    return 0.0f;
}

float UAuracronPCGTestUtils::GetElapsedTime(const FString& TimerName)
{
    FDateTime* StartTime = TestTimers.Find(TimerName);
    if (StartTime)
    {
        FDateTime CurrentTime = FDateTime::Now();
        return (CurrentTime - *StartTime).GetTotalSeconds();
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Timer '%s' was not found"), *TimerName);
    return 0.0f;
}

// =============================================================================
// VALIDATION SUITE IMPLEMENTATION
// =============================================================================

bool UAuracronPCGValidationSuite::ValidatePointData(UPCGPointData* PointData, TArray<FString>& OutErrors)
{
    OutErrors.Empty();
    
    if (!PointData)
    {
        OutErrors.Add(TEXT("PointData is null"));
        return false;
    }
    
    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    
    // Validate point count
    if (Points.Num() == 0)
    {
        OutErrors.Add(TEXT("PointData contains no points"));
    }
    
    // Validate individual points
    for (int32 i = 0; i < Points.Num(); i++)
    {
        const FPCGPoint& Point = Points[i];
        
        if (!ValidatePointAttributes(Point, OutErrors))
        {
            OutErrors.Add(FString::Printf(TEXT("Point %d has invalid attributes"), i));
        }
    }
    
    // Validate metadata if present
    if (PointData->Metadata)
    {
        TArray<FString> MetadataErrors;
        if (!ValidateMetadata(PointData->Metadata, MetadataErrors))
        {
            OutErrors.Append(MetadataErrors);
        }
    }
    
    return OutErrors.Num() == 0;
}

bool UAuracronPCGValidationSuite::ValidatePointAttributes(const FPCGPoint& Point, TArray<FString>& OutErrors)
{
    bool bValid = true;
    
    // Validate transform
    if (!ValidateTransform(Point.Transform, OutErrors))
    {
        bValid = false;
    }
    
    // Validate density
    if (!IsValidFloat(Point.Density) || Point.Density < 0.0f || Point.Density > 1.0f)
    {
        OutErrors.Add(FString::Printf(TEXT("Invalid density: %f"), Point.Density));
        bValid = false;
    }
    
    // Validate color
    if (!IsValidFloat(Point.Color.X) || !IsValidFloat(Point.Color.Y) || 
        !IsValidFloat(Point.Color.Z) || !IsValidFloat(Point.Color.W))
    {
        OutErrors.Add(TEXT("Invalid color values"));
        bValid = false;
    }
    
    return bValid;
}

bool UAuracronPCGValidationSuite::ValidateTransform(const FTransform& Transform, TArray<FString>& OutErrors)
{
    bool bValid = true;
    
    // Validate location
    if (!IsValidVector(Transform.GetLocation()))
    {
        OutErrors.Add(TEXT("Invalid transform location"));
        bValid = false;
    }
    
    // Validate rotation
    FQuat Rotation = Transform.GetRotation();
    if (!IsValidFloat(Rotation.X) || !IsValidFloat(Rotation.Y) || 
        !IsValidFloat(Rotation.Z) || !IsValidFloat(Rotation.W))
    {
        OutErrors.Add(TEXT("Invalid transform rotation"));
        bValid = false;
    }
    
    // Validate scale
    if (!IsValidVector(Transform.GetScale3D()))
    {
        OutErrors.Add(TEXT("Invalid transform scale"));
        bValid = false;
    }
    
    return bValid;
}

bool UAuracronPCGValidationSuite::IsValidFloat(float Value)
{
    return FMath::IsFinite(Value) && !FMath::IsNaN(Value);
}

bool UAuracronPCGValidationSuite::IsValidVector(const FVector& Vector)
{
    return IsValidFloat(Vector.X) && IsValidFloat(Vector.Y) && IsValidFloat(Vector.Z);
}

bool UAuracronPCGValidationSuite::ValidateMetadata(const UPCGMetadata* Metadata, TArray<FString>& OutErrors)
{
    OutErrors.Empty();
    
    if (!Metadata)
    {
        OutErrors.Add(TEXT("Metadata is null"));
        return false;
    }
    
    // Basic metadata validation
    // Note: In a real implementation, you would validate metadata attributes and values
    
    return OutErrors.Num() == 0;
}
