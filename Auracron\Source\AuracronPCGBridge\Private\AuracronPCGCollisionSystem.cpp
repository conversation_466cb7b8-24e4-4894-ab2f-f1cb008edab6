// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Collision System Implementation
// Bridge 2.10: PCG Framework - Collision e Physics

#include "AuracronPCGCollisionSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "PhysicsEngine/BodyInstance.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Async/ParallelFor.h"

// =============================================================================
// COLLISION TESTER IMPLEMENTATION
// =============================================================================

UAuracronPCGCollisionTesterSettings::UAuracronPCGCollisionTesterSettings()
{
    NodeMetadata.NodeName = TEXT("Collision Tester");
    NodeMetadata.NodeDescription = TEXT("Tests collision and physics properties for point placement validation");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Sampler;
    NodeMetadata.Tags.Add(TEXT("Collision"));
    NodeMetadata.Tags.Add(TEXT("Physics"));
    NodeMetadata.Tags.Add(TEXT("Validation"));
    NodeMetadata.Tags.Add(TEXT("Placement"));
    NodeMetadata.Tags.Add(TEXT("Testing"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.2f);
}

void UAuracronPCGCollisionTesterSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGCollisionTesterSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputHitResults)
    {
        FPCGPinProperties& HitPin = OutputPins.Emplace_GetRef();
        HitPin.Label = TEXT("Hit Results");
        HitPin.AllowedTypes = EPCGDataType::Attribute;
        HitPin.bAdvancedPin = true;
    }

    if (bOutputCollisionInfo)
    {
        FPCGPinProperties& CollisionPin = OutputPins.Emplace_GetRef();
        CollisionPin.Label = TEXT("Collision Info");
        CollisionPin.AllowedTypes = EPCGDataType::Attribute;
        CollisionPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGCollisionTesterElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                          FPCGDataCollection& OutputData, 
                                                                          const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGCollisionTesterSettings* Settings = GetTypedSettings<UAuracronPCGCollisionTesterSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Collision Tester");
            return Result;
        }

        // Get world context
        UWorld* World = GetWorld(Parameters);
        if (!World)
        {
            Result.ErrorMessage = TEXT("No valid world context for collision testing");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 ValidPlacements = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint> ValidPoints;
            ValidPoints.Reserve(InputPoints.Num());

            // Test collision for each point
            for (const FPCGPoint& InputPoint : InputPoints)
            {
                FPCGPoint OutputPoint = InputPoint;
                bool bIsValidPlacement = TestPointPlacement(World, OutputPoint, Settings);
                
                if (bIsValidPlacement)
                {
                    ValidPlacements++;
                    ValidPoints.Add(OutputPoint);
                }
                else if (!Settings->bFilterInvalidPoints)
                {
                    // Keep invalid points but mark them
                    SetPointValidationAttribute(OutputPoint, false, Settings);
                    ValidPoints.Add(OutputPoint);
                }

                TotalProcessed++;
            }

            OutputPointData->GetMutablePoints() = ValidPoints;
            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Collision Tester processed %d points, %d valid placements"), 
                                  TotalProcessed, ValidPlacements);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Collision Tester error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGCollisionTesterElement::TestPointPlacement(UWorld* World, FPCGPoint& Point, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    FVector Location = Point.Transform.GetLocation();
    bool bValidPlacement = true;

    // Project to surface if requested
    if (Settings->bProjectToSurface)
    {
        FVector ProjectedLocation = UAuracronPCGCollisionSystemUtils::ProjectToSurface(
            World, Location, Settings->ProjectionDistance, Settings->CollisionDescriptor);
        
        if (ProjectedLocation != Location)
        {
            Point.Transform.SetLocation(ProjectedLocation);
            Location = ProjectedLocation;
        }
    }

    // Perform validation based on mode
    switch (Settings->ValidationMode)
    {
        case EAuracronPCGPlacementValidation::GroundCheck:
            bValidPlacement = ValidateGroundPlacement(World, Location, Settings);
            break;
        case EAuracronPCGPlacementValidation::ClearanceCheck:
            bValidPlacement = ValidateClearance(World, Location, Settings);
            break;
        case EAuracronPCGPlacementValidation::StabilityCheck:
            bValidPlacement = ValidateStability(World, Location, Settings);
            break;
        case EAuracronPCGPlacementValidation::AllChecks:
            bValidPlacement = ValidateGroundPlacement(World, Location, Settings) &&
                             ValidateClearance(World, Location, Settings) &&
                             ValidateStability(World, Location, Settings);
            break;
        default:
            bValidPlacement = UAuracronPCGCollisionSystemUtils::ValidatePlacement(
                World, Location, Settings->CollisionDescriptor, Settings->ValidationMode);
            break;
    }

    // Set validation attributes
    SetPointValidationAttribute(Point, bValidPlacement, Settings);

    return bValidPlacement;
}

bool FAuracronPCGCollisionTesterElement::ValidateGroundPlacement(UWorld* World, const FVector& Location, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    FHitResult HitResult;
    FVector TraceStart = Location + FVector(0.0f, 0.0f, 100.0f);
    FVector TraceEnd = Location + Settings->CollisionDescriptor.TraceDirection * Settings->ProjectionDistance;

    bool bHit = UAuracronPCGCollisionSystemUtils::PerformCollisionTest(
        World, TraceStart, TraceEnd, Settings->CollisionDescriptor, HitResult);

    if (!bHit)
    {
        return false;
    }

    // Check ground angle
    float GroundAngle = UAuracronPCGCollisionSystemUtils::CalculateGroundAngle(HitResult.Normal);
    if (GroundAngle < Settings->MinGroundAngle || GroundAngle > Settings->MaxGroundAngle)
    {
        return false;
    }

    return true;
}

bool FAuracronPCGCollisionTesterElement::ValidateClearance(UWorld* World, const FVector& Location, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    return UAuracronPCGCollisionSystemUtils::CheckClearance(
        World, Location, Settings->ClearanceRadius, Settings->ClearanceHeight, Settings->CollisionDescriptor);
}

bool FAuracronPCGCollisionTesterElement::ValidateStability(UWorld* World, const FVector& Location, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    float StabilityScore = UAuracronPCGCollisionSystemUtils::CalculateStabilityScore(
        World, Location, Settings->ClearanceRadius, 8, Settings->CollisionDescriptor);
    
    return StabilityScore >= 0.7f; // Minimum stability threshold
}

void FAuracronPCGCollisionTesterElement::SetPointValidationAttribute(FPCGPoint& Point, bool bIsValid, const UAuracronPCGCollisionTesterSettings* Settings) const
{
    // Simplified attribute setting - in production you'd use proper metadata
    Point.Density = bIsValid ? 1.0f : 0.0f;
    
    if (bIsValid)
    {
        Point.Color = FVector4(0.0f, 1.0f, 0.0f, 1.0f); // Green for valid
    }
    else
    {
        Point.Color = FVector4(1.0f, 0.0f, 0.0f, 1.0f); // Red for invalid
    }
}

UWorld* FAuracronPCGCollisionTesterElement::GetWorld(const FAuracronPCGElementParams& Parameters) const
{
    // Simplified world access - in production you'd get from proper context
    return GWorld;
}

// =============================================================================
// PHYSICS SIMULATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGPhysicsSimulatorSettings::UAuracronPCGPhysicsSimulatorSettings()
{
    NodeMetadata.NodeName = TEXT("Physics Simulator");
    NodeMetadata.NodeDescription = TEXT("Simulates physics properties and applies physics-based transformations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Physics"));
    NodeMetadata.Tags.Add(TEXT("Simulation"));
    NodeMetadata.Tags.Add(TEXT("Dynamics"));
    NodeMetadata.Tags.Add(TEXT("Forces"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.2f);
}

void UAuracronPCGPhysicsSimulatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bApplyInitialForce || bApplyInitialImpulse)
    {
        FPCGPinProperties& ForcePin = InputPins.Emplace_GetRef();
        ForcePin.Label = TEXT("Forces");
        ForcePin.AllowedTypes = EPCGDataType::Point;
        ForcePin.bAdvancedPin = true;
    }
}

void UAuracronPCGPhysicsSimulatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputVelocity)
    {
        FPCGPinProperties& VelocityPin = OutputPins.Emplace_GetRef();
        VelocityPin.Label = TEXT("Velocity");
        VelocityPin.AllowedTypes = EPCGDataType::Attribute;
        VelocityPin.bAdvancedPin = true;
    }

    if (bOutputPhysicsState)
    {
        FPCGPinProperties& StatePin = OutputPins.Emplace_GetRef();
        StatePin.Label = TEXT("Physics State");
        StatePin.AllowedTypes = EPCGDataType::Attribute;
        StatePin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGPhysicsSimulatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                           FPCGDataCollection& OutputData, 
                                                                           const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGPhysicsSimulatorSettings* Settings = GetTypedSettings<UAuracronPCGPhysicsSimulatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Physics Simulator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 PointsSimulated = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Simulate physics for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                if (Settings->bRunSimulation)
                {
                    SimulatePointPhysics(OutputPoint, Settings);
                    PointsSimulated++;
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Physics Simulator processed %d points, simulated %d points"), 
                                  TotalProcessed, PointsSimulated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Physics Simulator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGPhysicsSimulatorElement::SimulatePointPhysics(FPCGPoint& Point, const UAuracronPCGPhysicsSimulatorSettings* Settings) const
{
    FVector Position = Point.Transform.GetLocation();
    FVector Velocity = FVector::ZeroVector;
    FVector AngularVelocity = FVector::ZeroVector;

    // Apply initial forces/impulses
    if (Settings->bApplyInitialForce)
    {
        FVector Force = Settings->InitialForce / Settings->PhysicsDescriptor.Mass;
        Velocity += Force * Settings->TimeStep;
    }

    if (Settings->bApplyInitialImpulse)
    {
        Velocity += Settings->InitialImpulse / Settings->PhysicsDescriptor.Mass;
    }

    // Simulate physics steps
    float CurrentTime = 0.0f;
    int32 Iterations = 0;
    
    while (CurrentTime < Settings->SimulationTime && Iterations < Settings->MaxIterations)
    {
        // Apply gravity
        if (Settings->bApplyGravity)
        {
            FVector GravityForce = Settings->GravityOverride;
            if (Settings->PhysicsDescriptor.bEnableGravity)
            {
                Velocity += GravityForce * Settings->TimeStep;
            }
        }

        // Apply damping
        Velocity *= (1.0f - Settings->PhysicsDescriptor.LinearDamping * Settings->TimeStep);
        AngularVelocity *= (1.0f - Settings->PhysicsDescriptor.AngularDamping * Settings->TimeStep);

        // Update position
        Position += Velocity * Settings->TimeStep;

        // Apply constraints
        if (Settings->bUseConstraints)
        {
            ApplyConstraints(Position, Velocity, Settings);
        }

        // Check for stability
        if (Settings->bStabilizeResults && 
            UAuracronPCGCollisionSystemUtils::IsPhysicsStable(Velocity, AngularVelocity))
        {
            break;
        }

        CurrentTime += Settings->TimeStep;
        Iterations++;
    }

    // Update point transform
    Point.Transform.SetLocation(Position);
    
    // Set velocity attributes if requested
    if (Settings->bOutputVelocity)
    {
        // Simplified attribute setting - in production you'd use proper metadata
        Point.SetLocalBounds(FBox(FVector(-Velocity.Size()), FVector(Velocity.Size())));
    }
}

void FAuracronPCGPhysicsSimulatorElement::ApplyConstraints(FVector& Position, FVector& Velocity, const UAuracronPCGPhysicsSimulatorSettings* Settings) const
{
    FVector ToCenter = Settings->ConstraintCenter - Position;
    float Distance = ToCenter.Size();
    
    if (Distance > Settings->ConstraintRadius)
    {
        // Push back inside constraint
        FVector Direction = ToCenter.GetSafeNormal();
        Position = Settings->ConstraintCenter - Direction * Settings->ConstraintRadius;
        
        if (Settings->bBounceOffConstraints)
        {
            // Reflect velocity
            FVector Normal = -Direction;
            Velocity = Velocity - 2.0f * FVector::DotProduct(Velocity, Normal) * Normal;
            Velocity *= Settings->PhysicsDescriptor.Restitution;
        }
        else
        {
            // Stop at constraint
            Velocity = FVector::ZeroVector;
        }
    }
}

// =============================================================================
// SPATIAL QUERY PROCESSOR IMPLEMENTATION
// =============================================================================

UAuracronPCGSpatialQuerySettings::UAuracronPCGSpatialQuerySettings()
{
    NodeMetadata.NodeName = TEXT("Spatial Query Processor");
    NodeMetadata.NodeDescription = TEXT("Performs spatial queries and proximity testing for point relationships");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Sampler;
    NodeMetadata.Tags.Add(TEXT("Spatial"));
    NodeMetadata.Tags.Add(TEXT("Query"));
    NodeMetadata.Tags.Add(TEXT("Proximity"));
    NodeMetadata.Tags.Add(TEXT("Relationships"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.2f, 0.8f);
}

void UAuracronPCGSpatialQuerySettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseInputAsTargets)
    {
        FPCGPinProperties& TargetPin = InputPins.Emplace_GetRef();
        TargetPin.Label = TEXT("Targets");
        TargetPin.AllowedTypes = EPCGDataType::Point;
        TargetPin.bAdvancedPin = true;
    }
}

void UAuracronPCGSpatialQuerySettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputQueryResults)
    {
        FPCGPinProperties& QueryPin = OutputPins.Emplace_GetRef();
        QueryPin.Label = TEXT("Query Results");
        QueryPin.AllowedTypes = EPCGDataType::Point;
        QueryPin.bAdvancedPin = true;
    }

    if (bOutputRelationshipData)
    {
        FPCGPinProperties& RelationshipPin = OutputPins.Emplace_GetRef();
        RelationshipPin.Label = TEXT("Relationships");
        RelationshipPin.AllowedTypes = EPCGDataType::Attribute;
        RelationshipPin.bAdvancedPin = true;
    }

    if (bOutputClusterData)
    {
        FPCGPinProperties& ClusterPin = OutputPins.Emplace_GetRef();
        ClusterPin.Label = TEXT("Clusters");
        ClusterPin.AllowedTypes = EPCGDataType::Attribute;
        ClusterPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGSpatialQueryElement::ProcessData(const FPCGDataCollection& InputData,
                                                                       FPCGDataCollection& OutputData,
                                                                       const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGSpatialQuerySettings* Settings = GetTypedSettings<UAuracronPCGSpatialQuerySettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Spatial Query Processor");
            return Result;
        }

        // Get world context
        UWorld* World = GetWorld(Parameters);
        if (!World)
        {
            Result.ErrorMessage = TEXT("No valid world context for spatial queries");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 QueriesPerformed = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Perform spatial queries for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Perform spatial query
                PerformSpatialQueryForPoint(World, OutputPoint, Settings);
                QueriesPerformed++;

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Perform clustering if requested
        if (Settings->bPerformClustering)
        {
            PerformClustering(ProcessedPointData, Settings);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Spatial Query Processor processed %d points, performed %d queries"),
                                  TotalProcessed, QueriesPerformed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Spatial Query Processor error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGSpatialQueryElement::PerformSpatialQueryForPoint(UWorld* World, FPCGPoint& Point, const UAuracronPCGSpatialQuerySettings* Settings) const
{
    FVector Location = Point.Transform.GetLocation();

    // Perform spatial query based on type
    TArray<AActor*> FoundActors;

    if (Settings->bQueryWorldActors)
    {
        FoundActors = UAuracronPCGCollisionSystemUtils::PerformSpatialQuery(World, Location, Settings->QueryDescriptor);
    }

    // Calculate relationships if requested
    if (Settings->bAnalyzeRelationships && FoundActors.Num() > 0)
    {
        AnalyzePointRelationships(Point, FoundActors, Settings);
    }

    // Set query result attributes
    SetQueryResultAttributes(Point, FoundActors, Settings);
}

void FAuracronPCGSpatialQueryElement::AnalyzePointRelationships(FPCGPoint& Point, const TArray<AActor*>& FoundActors, const UAuracronPCGSpatialQuerySettings* Settings) const
{
    FVector Location = Point.Transform.GetLocation();

    if (Settings->bCalculateDistances && FoundActors.Num() > 0)
    {
        // Find nearest actor
        float NearestDistance = FLT_MAX;
        for (AActor* Actor : FoundActors)
        {
            if (Actor)
            {
                float Distance = FVector::Dist(Location, Actor->GetActorLocation());
                if (Distance < NearestDistance)
                {
                    NearestDistance = Distance;
                }
            }
        }

        // Set nearest distance attribute (simplified)
        Point.SetLocalBounds(FBox(FVector(-NearestDistance), FVector(NearestDistance)));
    }

    if (Settings->bCalculateAngles)
    {
        // Calculate angles to nearby objects
        // Simplified implementation
    }

    if (Settings->bCalculateVisibility)
    {
        // Check line of sight to nearby objects
        // Simplified implementation
    }
}

void FAuracronPCGSpatialQueryElement::SetQueryResultAttributes(FPCGPoint& Point, const TArray<AActor*>& FoundActors, const UAuracronPCGSpatialQuerySettings* Settings) const
{
    // Set neighbor count
    int32 NeighborCount = FoundActors.Num();

    // Simplified attribute setting - in production you'd use proper metadata
    Point.Density = FMath::Clamp(static_cast<float>(NeighborCount) / 10.0f, 0.0f, 1.0f);

    // Color based on neighbor count
    float ColorIntensity = FMath::Clamp(static_cast<float>(NeighborCount) / 5.0f, 0.0f, 1.0f);
    Point.Color = FVector4(ColorIntensity, 0.0f, 1.0f - ColorIntensity, 1.0f);
}

void FAuracronPCGSpatialQueryElement::PerformClustering(TArray<UPCGPointData*>& PointDataArray, const UAuracronPCGSpatialQuerySettings* Settings) const
{
    // Simple clustering implementation
    for (UPCGPointData* PointData : PointDataArray)
    {
        if (!PointData)
        {
            continue;
        }

        TArray<FPCGPoint>& Points = PointData->GetMutablePoints();
        TArray<int32> ClusterIDs;
        ClusterIDs.SetNumZeroed(Points.Num());

        int32 CurrentClusterID = 0;

        for (int32 i = 0; i < Points.Num(); i++)
        {
            if (ClusterIDs[i] != 0) // Already assigned to cluster
            {
                continue;
            }

            // Start new cluster
            CurrentClusterID++;
            TArray<int32> ClusterPoints;
            ClusterPoints.Add(i);
            ClusterIDs[i] = CurrentClusterID;

            // Find nearby points for this cluster
            for (int32 j = i + 1; j < Points.Num(); j++)
            {
                if (ClusterIDs[j] != 0) // Already assigned
                {
                    continue;
                }

                float Distance = FVector::Dist(Points[i].Transform.GetLocation(), Points[j].Transform.GetLocation());
                if (Distance <= Settings->ClusterRadius)
                {
                    ClusterPoints.Add(j);
                    ClusterIDs[j] = CurrentClusterID;
                }
            }

            // Validate cluster size
            if (ClusterPoints.Num() < Settings->MinClusterSize || ClusterPoints.Num() > Settings->MaxClusterSize)
            {
                // Remove cluster assignment
                for (int32 PointIndex : ClusterPoints)
                {
                    ClusterIDs[PointIndex] = 0;
                }
                CurrentClusterID--;
            }
        }

        // Apply cluster IDs to points
        for (int32 i = 0; i < Points.Num(); i++)
        {
            // Simplified cluster ID setting - in production you'd use proper metadata
            if (ClusterIDs[i] > 0)
            {
                float ClusterColorValue = static_cast<float>(ClusterIDs[i]) / static_cast<float>(CurrentClusterID);
                Points[i].Color = FVector4(ClusterColorValue, ClusterColorValue, 1.0f, 1.0f);
            }
        }
    }
}

UWorld* FAuracronPCGSpatialQueryElement::GetWorld(const FAuracronPCGElementParams& Parameters) const
{
    // Simplified world access - in production you'd get from proper context
    return GWorld;
}
