using UnrealBuildTool;

public class AuracronQABridge : ModuleRules
{
    public AuracronQABridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "AutomationController",
            "AutomationMessages",
            "AutomationTest",
            "AutomationWorker",
            "FunctionalTesting",
            "ScreenShotComparisonTools",
            "ImageWrapper",
            "RenderCore",
            "RHI",
            "Renderer",
            "Slate",
            "SlateCore",
            "UMG",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "PythonScriptPlugin"
        });

        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "Json",
            "JsonObjectConverter",
            "HTTP",
            "ApplicationCore",
            "InputCore",
            "DesktopPlatform",
            "HAL",
            "RenderCore",
            "RHI",
            "Renderer",
            "ImageWrapper",
            "AutomationController",
            "AutomationMessages",
            "AutomationTest",
            "AutomationWorker",
            "FunctionalTesting",
            "ScreenShotComparisonTools",
            "SessionServices",
            "TargetPlatform",
            "LauncherServices",
            "GameplayDebugger",
            "EngineSettings",
            "DeveloperSettings"
        });

        // Enable RTTI for this module
        bUseRTTI = true;
        
        // Enable exceptions for this module
        bEnableExceptions = true;
        
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronQABridge/Public"
        });
        
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronQABridge/Private"
        });
    }
}
