// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Async Processing Utilities Implementation
// Bridge 2.16: PCG Framework - Async Processing

#include "AuracronPCGAsyncProcessing.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "HAL/PlatformProcess.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformMisc.h"
#include "Misc/DateTime.h"
#include "Stats/Stats.h"

// =============================================================================
// ASYNC PROCESSING UTILITIES IMPLEMENTATION
// =============================================================================

int32 UAuracronPCGAsyncProcessingUtils::GetOptimalThreadCount()
{
    int32 CoreCount = FPlatformMisc::NumberOfCores();
    int32 LogicalCoreCount = FPlatformMisc::NumberOfCoresIncludingHyperthreads();
    
    // Use physical cores - 1 for game thread, but at least 1 thread
    int32 OptimalThreads = FMath::Max(1, CoreCount - 1);
    
    // Cap at logical cores if hyperthreading is available
    if (LogicalCoreCount > CoreCount)
    {
        OptimalThreads = FMath::Min(OptimalThreads, LogicalCoreCount - 1);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Optimal thread count: %d (Physical cores: %d, Logical cores: %d)"), 
                              OptimalThreads, CoreCount, LogicalCoreCount);
    
    return OptimalThreads;
}

int32 UAuracronPCGAsyncProcessingUtils::GetAvailableMemoryMB()
{
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    
    // Available physical memory in MB
    int32 AvailableMemoryMB = static_cast<int32>(MemoryStats.AvailablePhysical / (1024 * 1024));
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Available memory: %d MB"), AvailableMemoryMB);
    
    return AvailableMemoryMB;
}

float UAuracronPCGAsyncProcessingUtils::GetCPUUsagePercentage()
{
    // Simplified CPU usage calculation
    // In a real implementation, you'd use platform-specific APIs to get actual CPU usage
    static float SimulatedCPUUsage = 25.0f;
    
    // Simulate some variation
    SimulatedCPUUsage += FMath::RandRange(-5.0f, 5.0f);
    SimulatedCPUUsage = FMath::Clamp(SimulatedCPUUsage, 0.0f, 100.0f);
    
    return SimulatedCPUUsage;
}

bool UAuracronPCGAsyncProcessingUtils::IsMultithreadingSupported()
{
    return FPlatformProcess::SupportsMultithreading();
}

int32 UAuracronPCGAsyncProcessingUtils::CalculateOptimalBatchSize(int32 DataSize, int32 ThreadCount)
{
    if (DataSize <= 0 || ThreadCount <= 0)
    {
        return 1;
    }
    
    // Base batch size calculation
    int32 BaseBatchSize = DataSize / ThreadCount;
    
    // Ensure minimum batch size for efficiency
    int32 MinBatchSize = 100;
    int32 MaxBatchSize = 10000;
    
    // Adjust based on data size
    if (DataSize < 1000)
    {
        MinBatchSize = 10;
        MaxBatchSize = 100;
    }
    else if (DataSize > 100000)
    {
        MinBatchSize = 1000;
        MaxBatchSize = 50000;
    }
    
    int32 OptimalBatchSize = FMath::Clamp(BaseBatchSize, MinBatchSize, MaxBatchSize);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Optimal batch size: %d (Data size: %d, Threads: %d)"), 
                              OptimalBatchSize, DataSize, ThreadCount);
    
    return OptimalBatchSize;
}

EAuracronPCGAsyncProcessingMode UAuracronPCGAsyncProcessingUtils::RecommendProcessingMode(int32 DataSize, int32 Complexity)
{
    // Simple heuristic for processing mode recommendation
    if (DataSize < 100)
    {
        return EAuracronPCGAsyncProcessingMode::Sequential;
    }
    else if (DataSize < 1000)
    {
        return EAuracronPCGAsyncProcessingMode::AsyncTask;
    }
    else if (DataSize < 10000)
    {
        return EAuracronPCGAsyncProcessingMode::Parallel;
    }
    else if (Complexity > 5)
    {
        return EAuracronPCGAsyncProcessingMode::TaskGraph;
    }
    else
    {
        return EAuracronPCGAsyncProcessingMode::ParallelFor;
    }
}

FAuracronPCGAsyncTaskDescriptor UAuracronPCGAsyncProcessingUtils::CreateOptimizedDescriptor(int32 DataSize, EAuracronPCGTaskPriority Priority)
{
    FAuracronPCGAsyncTaskDescriptor Descriptor;
    
    // Set basic properties
    Descriptor.TaskName = TEXT("OptimizedTask");
    Descriptor.Priority = Priority;
    Descriptor.ProcessingMode = RecommendProcessingMode(DataSize, 3); // Assume medium complexity
    
    // Optimize thread count
    int32 OptimalThreads = GetOptimalThreadCount();
    Descriptor.MaxConcurrentTasks = FMath::Min(OptimalThreads, FMath::Max(1, DataSize / 1000));
    
    // Optimize batch size
    Descriptor.BatchSize = CalculateOptimalBatchSize(DataSize, Descriptor.MaxConcurrentTasks);
    Descriptor.MinBatchSize = FMath::Max(10, Descriptor.BatchSize / 10);
    Descriptor.MaxBatchSize = FMath::Min(50000, Descriptor.BatchSize * 10);
    
    // Memory settings based on data size
    if (DataSize < 1000)
    {
        Descriptor.MemoryPoolSize = 128; // 128 MB
        Descriptor.MaxMemoryUsageMB = 256;
    }
    else if (DataSize < 10000)
    {
        Descriptor.MemoryPoolSize = 512; // 512 MB
        Descriptor.MaxMemoryUsageMB = 1024;
    }
    else
    {
        Descriptor.MemoryPoolSize = 1024; // 1 GB
        Descriptor.MaxMemoryUsageMB = 2048;
    }
    
    // Timeout based on data size and complexity
    Descriptor.TimeoutSeconds = FMath::Max(30.0f, static_cast<float>(DataSize) * 0.01f);
    
    // Enable features based on priority
    if (Priority == EAuracronPCGTaskPriority::High || Priority == EAuracronPCGTaskPriority::Critical)
    {
        Descriptor.bEnableDetailedLogging = true;
        Descriptor.bEnablePerformanceProfiling = true;
        Descriptor.ProgressUpdateInterval = 0.05f; // More frequent updates
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Created optimized descriptor for %d items with %d threads"), 
                              DataSize, Descriptor.MaxConcurrentTasks);
    
    return Descriptor;
}

float UAuracronPCGAsyncProcessingUtils::EstimateExecutionTime(int32 DataSize, EAuracronPCGAsyncProcessingMode Mode)
{
    if (DataSize <= 0)
    {
        return 0.0f;
    }
    
    // Base time per item in seconds (simplified estimation)
    float BaseTimePerItem = 0.001f; // 1ms per item
    
    // Mode-specific multipliers
    float ModeMultiplier = 1.0f;
    switch (Mode)
    {
        case EAuracronPCGAsyncProcessingMode::Sequential:
            ModeMultiplier = 1.0f;
            break;
        case EAuracronPCGAsyncProcessingMode::Parallel:
            ModeMultiplier = 0.3f; // ~70% speedup
            break;
        case EAuracronPCGAsyncProcessingMode::TaskGraph:
            ModeMultiplier = 0.25f; // ~75% speedup
            break;
        case EAuracronPCGAsyncProcessingMode::AsyncTask:
            ModeMultiplier = 0.8f; // ~20% speedup
            break;
        case EAuracronPCGAsyncProcessingMode::ParallelFor:
            ModeMultiplier = 0.2f; // ~80% speedup
            break;
        case EAuracronPCGAsyncProcessingMode::Hybrid:
            ModeMultiplier = 0.15f; // ~85% speedup
            break;
    }
    
    float EstimatedTime = static_cast<float>(DataSize) * BaseTimePerItem * ModeMultiplier;
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Estimated execution time: %.3f seconds for %d items"), 
                              EstimatedTime, DataSize);
    
    return EstimatedTime;
}

int32 UAuracronPCGAsyncProcessingUtils::EstimateMemoryUsage(int32 DataSize, EAuracronPCGAsyncProcessingMode Mode)
{
    if (DataSize <= 0)
    {
        return 0;
    }
    
    // Base memory per item in bytes (simplified estimation)
    int32 BaseMemoryPerItem = 1024; // 1KB per item
    
    // Mode-specific multipliers
    float ModeMultiplier = 1.0f;
    switch (Mode)
    {
        case EAuracronPCGAsyncProcessingMode::Sequential:
            ModeMultiplier = 1.0f;
            break;
        case EAuracronPCGAsyncProcessingMode::Parallel:
            ModeMultiplier = 1.5f; // More memory for parallel processing
            break;
        case EAuracronPCGAsyncProcessingMode::TaskGraph:
            ModeMultiplier = 2.0f; // Task graph overhead
            break;
        case EAuracronPCGAsyncProcessingMode::AsyncTask:
            ModeMultiplier = 1.2f; // Slight overhead
            break;
        case EAuracronPCGAsyncProcessingMode::ParallelFor:
            ModeMultiplier = 1.3f; // ParallelFor overhead
            break;
        case EAuracronPCGAsyncProcessingMode::Hybrid:
            ModeMultiplier = 2.5f; // Highest overhead
            break;
    }
    
    int32 EstimatedMemoryBytes = static_cast<int32>(static_cast<float>(DataSize * BaseMemoryPerItem) * ModeMultiplier);
    int32 EstimatedMemoryMB = EstimatedMemoryBytes / (1024 * 1024);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Estimated memory usage: %d MB for %d items"), 
                              EstimatedMemoryMB, DataSize);
    
    return EstimatedMemoryMB;
}

bool UAuracronPCGAsyncProcessingUtils::WillTaskFitInMemory(const FAuracronPCGAsyncTaskDescriptor& Descriptor, int32 DataSize)
{
    int32 EstimatedMemoryMB = EstimateMemoryUsage(DataSize, Descriptor.ProcessingMode);
    int32 AvailableMemoryMB = GetAvailableMemoryMB();
    
    // Leave 25% buffer for system
    int32 UsableMemoryMB = static_cast<int32>(static_cast<float>(AvailableMemoryMB) * 0.75f);
    
    bool bWillFit = EstimatedMemoryMB <= UsableMemoryMB;
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Memory check: Estimated %d MB, Available %d MB, Will fit: %s"), 
                              EstimatedMemoryMB, UsableMemoryMB, bWillFit ? TEXT("Yes") : TEXT("No"));
    
    return bWillFit;
}

void UAuracronPCGAsyncProcessingUtils::EnableAsyncDebugging(bool bEnabled)
{
    static bool bAsyncDebuggingEnabled = false;
    bAsyncDebuggingEnabled = bEnabled;
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Async debugging %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronPCGAsyncProcessingUtils::IsAsyncDebuggingEnabled()
{
    static bool bAsyncDebuggingEnabled = false;
    return bAsyncDebuggingEnabled;
}

FString UAuracronPCGAsyncProcessingUtils::GeneratePerformanceReport()
{
    FString Report = TEXT("=== Async Processing Performance Report ===\n\n");
    
    // System information
    Report += FString::Printf(TEXT("System Information:\n"));
    Report += FString::Printf(TEXT("  CPU Cores: %d\n"), FPlatformMisc::NumberOfCores());
    Report += FString::Printf(TEXT("  Logical Cores: %d\n"), FPlatformMisc::NumberOfCoresIncludingHyperthreads());
    Report += FString::Printf(TEXT("  Available Memory: %d MB\n"), GetAvailableMemoryMB());
    Report += FString::Printf(TEXT("  CPU Usage: %.1f%%\n"), GetCPUUsagePercentage());
    Report += FString::Printf(TEXT("  Multithreading Supported: %s\n"), IsMultithreadingSupported() ? TEXT("Yes") : TEXT("No"));
    Report += TEXT("\n");
    
    // Task manager statistics
    UAuracronPCGAsyncTaskManager* TaskManager = UAuracronPCGAsyncTaskManager::GetInstance();
    if (TaskManager && TaskManager->IsInitialized())
    {
        Report += TEXT("Task Manager Statistics:\n");
        Report += FString::Printf(TEXT("  Active Tasks: %d\n"), TaskManager->GetActiveTaskCount());
        Report += FString::Printf(TEXT("  Max Concurrent Tasks: %d\n"), TaskManager->GetMaxConcurrentTasks());
        Report += FString::Printf(TEXT("  Total Tasks Executed: %d\n"), TaskManager->GetTotalTasksExecuted());
        Report += FString::Printf(TEXT("  Average Execution Time: %.3f seconds\n"), TaskManager->GetAverageTaskExecutionTime());
        Report += FString::Printf(TEXT("  System Load: %.1f%%\n"), TaskManager->GetSystemLoadPercentage());
        Report += TEXT("\n");
        
        // Performance metrics
        TMap<FString, float> Metrics = TaskManager->GetPerformanceMetrics();
        Report += TEXT("Performance Metrics:\n");
        for (const auto& MetricPair : Metrics)
        {
            Report += FString::Printf(TEXT("  %s: %.3f\n"), *MetricPair.Key, MetricPair.Value);
        }
        Report += TEXT("\n");
    }
    
    // Memory pool statistics
    UAuracronPCGMemoryPoolManager* MemoryManager = UAuracronPCGMemoryPoolManager::GetInstance();
    if (MemoryManager && MemoryManager->IsPoolInitialized())
    {
        Report += TEXT("Memory Pool Statistics:\n");
        Report += FString::Printf(TEXT("  Total Pool Size: %d MB\n"), MemoryManager->GetTotalPoolSize());
        Report += FString::Printf(TEXT("  Used Memory: %d MB\n"), MemoryManager->GetUsedMemory());
        Report += FString::Printf(TEXT("  Free Memory: %d MB\n"), MemoryManager->GetFreeMemory());
        Report += FString::Printf(TEXT("  Usage Percentage: %.1f%%\n"), MemoryManager->GetMemoryUsagePercentage());
        Report += FString::Printf(TEXT("  Allocation Count: %d\n"), MemoryManager->GetAllocationCount());
        Report += TEXT("\n");
    }
    
    Report += FString::Printf(TEXT("Report generated at: %s\n"), *FDateTime::Now().ToString());
    
    return Report;
}

void UAuracronPCGAsyncProcessingUtils::LogSystemInfo()
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("=== System Information ==="));
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("CPU Cores: %d"), FPlatformMisc::NumberOfCores());
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Logical Cores: %d"), FPlatformMisc::NumberOfCoresIncludingHyperthreads());
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Available Memory: %d MB"), GetAvailableMemoryMB());
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("CPU Usage: %.1f%%"), GetCPUUsagePercentage());
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Multithreading Supported: %s"), IsMultithreadingSupported() ? TEXT("Yes") : TEXT("No"));
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Optimal Thread Count: %d"), GetOptimalThreadCount());
}

bool UAuracronPCGAsyncProcessingUtils::ValidateTaskDescriptor(const FAuracronPCGAsyncTaskDescriptor& Descriptor, TArray<FString>& OutErrors)
{
    OutErrors.Empty();
    
    // Validate basic properties
    if (Descriptor.TaskName.IsEmpty())
    {
        OutErrors.Add(TEXT("Task name cannot be empty"));
    }
    
    if (Descriptor.MaxConcurrentTasks <= 0)
    {
        OutErrors.Add(TEXT("Max concurrent tasks must be greater than 0"));
    }
    
    if (Descriptor.TimeoutSeconds < 0.0f)
    {
        OutErrors.Add(TEXT("Timeout seconds cannot be negative"));
    }
    
    if (Descriptor.BatchSize <= 0)
    {
        OutErrors.Add(TEXT("Batch size must be greater than 0"));
    }
    
    if (Descriptor.MinBatchSize > Descriptor.MaxBatchSize)
    {
        OutErrors.Add(TEXT("Min batch size cannot be greater than max batch size"));
    }
    
    if (Descriptor.MemoryPoolSize <= 0)
    {
        OutErrors.Add(TEXT("Memory pool size must be greater than 0"));
    }
    
    if (Descriptor.MaxMemoryUsageMB <= 0)
    {
        OutErrors.Add(TEXT("Max memory usage must be greater than 0"));
    }
    
    if (Descriptor.MaxRetryAttempts < 0)
    {
        OutErrors.Add(TEXT("Max retry attempts cannot be negative"));
    }
    
    if (Descriptor.RetryDelaySeconds < 0.0f)
    {
        OutErrors.Add(TEXT("Retry delay cannot be negative"));
    }
    
    if (Descriptor.ProgressUpdateInterval <= 0.0f)
    {
        OutErrors.Add(TEXT("Progress update interval must be greater than 0"));
    }
    
    return OutErrors.Num() == 0;
}

bool UAuracronPCGAsyncProcessingUtils::IsTaskDescriptorOptimal(const FAuracronPCGAsyncTaskDescriptor& Descriptor, int32 DataSize)
{
    // Check if the descriptor is reasonably optimized for the given data size
    int32 OptimalThreads = GetOptimalThreadCount();
    int32 OptimalBatchSize = CalculateOptimalBatchSize(DataSize, OptimalThreads);
    EAuracronPCGAsyncProcessingMode OptimalMode = RecommendProcessingMode(DataSize, 3);
    
    bool bIsOptimal = true;
    
    // Check thread count
    if (Descriptor.MaxConcurrentTasks > OptimalThreads * 2 || Descriptor.MaxConcurrentTasks < OptimalThreads / 2)
    {
        bIsOptimal = false;
    }
    
    // Check batch size
    if (Descriptor.BatchSize > OptimalBatchSize * 2 || Descriptor.BatchSize < OptimalBatchSize / 2)
    {
        bIsOptimal = false;
    }
    
    // Check processing mode
    if (Descriptor.ProcessingMode != OptimalMode)
    {
        bIsOptimal = false;
    }
    
    return bIsOptimal;
}

FAuracronPCGAsyncTaskDescriptor UAuracronPCGAsyncProcessingUtils::OptimizeTaskDescriptor(const FAuracronPCGAsyncTaskDescriptor& Descriptor, int32 DataSize)
{
    FAuracronPCGAsyncTaskDescriptor OptimizedDescriptor = Descriptor;
    
    // Optimize based on data size
    int32 OptimalThreads = GetOptimalThreadCount();
    int32 OptimalBatchSize = CalculateOptimalBatchSize(DataSize, OptimalThreads);
    EAuracronPCGAsyncProcessingMode OptimalMode = RecommendProcessingMode(DataSize, 3);
    
    OptimizedDescriptor.MaxConcurrentTasks = OptimalThreads;
    OptimizedDescriptor.BatchSize = OptimalBatchSize;
    OptimizedDescriptor.ProcessingMode = OptimalMode;
    
    // Optimize memory settings
    int32 EstimatedMemoryMB = EstimateMemoryUsage(DataSize, OptimalMode);
    OptimizedDescriptor.MemoryPoolSize = FMath::Max(128, EstimatedMemoryMB);
    OptimizedDescriptor.MaxMemoryUsageMB = OptimizedDescriptor.MemoryPoolSize * 2;
    
    // Optimize timeout
    float EstimatedTime = EstimateExecutionTime(DataSize, OptimalMode);
    OptimizedDescriptor.TimeoutSeconds = FMath::Max(30.0f, EstimatedTime * 3.0f); // 3x safety margin
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Optimized task descriptor for %d items"), DataSize);
    
    return OptimizedDescriptor;
}
