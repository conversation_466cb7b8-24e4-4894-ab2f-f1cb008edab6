// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Spline System Advanced Utilities Implementation
// Bridge 2.8: PCG Framework - Spline System

#include "AuracronPCGSplineSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Spline includes
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "PCGSplineData.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

namespace AuracronPCGSplineSystemUtils
{
    // =============================================================================
    // PCG DATA CONVERSION
    // =============================================================================

    bool CreateSplineFromPCGData(const UPCGSplineData* SplineData, USplineComponent* OutSplineComponent)
    {
        if (!SplineData || !OutSplineComponent)
        {
            return false;
        }

        // Extract spline information from PCG spline data
        // Simplified implementation - in production you'd properly extract spline data
        
        // Clear existing points
        OutSplineComponent->ClearSplinePoints();

        // Get spline points from PCG data (simplified)
        // In production, you'd extract actual spline points from the PCG spline data
        TArray<FVector> SplinePoints;
        
        // For now, create a simple spline
        SplinePoints.Add(FVector(0.0f, 0.0f, 0.0f));
        SplinePoints.Add(FVector(100.0f, 0.0f, 0.0f));
        SplinePoints.Add(FVector(200.0f, 100.0f, 0.0f));

        // Add points to spline component
        for (const FVector& Point : SplinePoints)
        {
            OutSplineComponent->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }

        OutSplineComponent->UpdateSpline();
        return true;
    }

    UPCGSplineData* CreatePCGDataFromSpline(USplineComponent* SplineComponent)
    {
        if (!SplineComponent)
        {
            return nullptr;
        }

        UPCGSplineData* SplineData = NewObject<UPCGSplineData>();
        
        // Initialize PCG spline data from spline component
        // Simplified implementation - in production you'd properly convert spline data
        
        return SplineData;
    }

    // =============================================================================
    // SPLINE INTERPOLATION
    // =============================================================================

    bool InterpolateSplinePoints(const TArray<FVector>& ControlPoints, TArray<FVector>& OutInterpolatedPoints, int32 PointsPerSegment)
    {
        OutInterpolatedPoints.Empty();
        
        if (ControlPoints.Num() < 2)
        {
            return false;
        }

        OutInterpolatedPoints.Reserve((ControlPoints.Num() - 1) * PointsPerSegment + 1);

        // Interpolate between each pair of control points
        for (int32 i = 0; i < ControlPoints.Num() - 1; i++)
        {
            FVector StartPoint = ControlPoints[i];
            FVector EndPoint = ControlPoints[i + 1];

            // Add interpolated points for this segment
            for (int32 j = 0; j < PointsPerSegment; j++)
            {
                float T = static_cast<float>(j) / PointsPerSegment;
                FVector InterpolatedPoint = FMath::Lerp(StartPoint, EndPoint, T);
                OutInterpolatedPoints.Add(InterpolatedPoint);
            }
        }

        // Add the final point
        OutInterpolatedPoints.Add(ControlPoints.Last());

        return true;
    }

    // =============================================================================
    // PATH FINDING ALGORITHMS
    // =============================================================================

    float CalculatePathCost(const TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor)
    {
        if (Path.Num() < 2)
        {
            return FLT_MAX;
        }

        float TotalCost = 0.0f;

        for (int32 i = 1; i < Path.Num(); i++)
        {
            FVector Segment = Path[i] - Path[i-1];
            float SegmentLength = Segment.Size();
            
            // Distance cost
            if (PathFindingDescriptor.bUseDistanceCost)
            {
                TotalCost += SegmentLength * PathFindingDescriptor.DistanceCostMultiplier;
            }

            // Terrain cost (slope)
            if (PathFindingDescriptor.bUseTerrainCost)
            {
                float Slope = FMath::Abs(FMath::Atan2(Segment.Z, FVector2D(Segment.X, Segment.Y).Size()));
                float SlopeDegrees = FMath::RadiansToDegrees(Slope);
                float SlopeCost = (SlopeDegrees / 90.0f) * PathFindingDescriptor.SlopeCostMultiplier;
                TotalCost += SlopeCost * SegmentLength;
            }
        }

        return TotalCost;
    }

    bool ValidateSplineForMeshGeneration(USplineComponent* SplineComponent, const FAuracronPCGSplineMeshDescriptor& MeshDescriptor)
    {
        if (!SplineComponent)
        {
            return false;
        }

        // Check if spline has enough points
        if (SplineComponent->GetNumberOfSplinePoints() < 2)
        {
            return false;
        }

        // Check if mesh is valid
        UStaticMesh* StaticMesh = MeshDescriptor.StaticMesh.LoadSynchronous();
        if (!StaticMesh)
        {
            return false;
        }

        // Check spline length
        float SplineLength = SplineComponent->GetSplineLength();
        if (SplineLength <= 0.0f)
        {
            return false;
        }

        return true;
    }

    // =============================================================================
    // SPLINE DEFORMATION
    // =============================================================================

    void ApplySplineDeformation(USplineComponent* SplineComponent, EAuracronPCGSplineDeformationMode DeformationMode, float Strength, float Frequency)
    {
        if (!SplineComponent)
        {
            return;
        }

        int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
        
        for (int32 i = 0; i < NumPoints; i++)
        {
            FVector CurrentLocation = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
            FVector DeformedLocation = CurrentLocation;

            float T = static_cast<float>(i) / FMath::Max(1, NumPoints - 1);

            switch (DeformationMode)
            {
                case EAuracronPCGSplineDeformationMode::Wave:
                {
                    float WaveOffset = FMath::Sin(T * Frequency * 2.0f * PI) * Strength;
                    DeformedLocation.Z += WaveOffset;
                    break;
                }
                case EAuracronPCGSplineDeformationMode::Noise:
                {
                    float NoiseValue = FMath::PerlinNoise1D(T * Frequency) * Strength;
                    DeformedLocation += FVector(0.0f, NoiseValue, NoiseValue * 0.5f);
                    break;
                }
                case EAuracronPCGSplineDeformationMode::Spiral:
                {
                    float Angle = T * Frequency * 2.0f * PI;
                    float Radius = Strength;
                    DeformedLocation.Y += FMath::Cos(Angle) * Radius;
                    DeformedLocation.Z += FMath::Sin(Angle) * Radius;
                    break;
                }
                case EAuracronPCGSplineDeformationMode::Bend:
                {
                    float BendAngle = T * Frequency * PI;
                    float BendRadius = 1000.0f / FMath::Max(0.001f, Strength);
                    
                    FVector BendCenter = FVector(BendRadius, 0.0f, 0.0f);
                    FVector RelativePos = CurrentLocation - BendCenter;
                    
                    float CosAngle = FMath::Cos(BendAngle);
                    float SinAngle = FMath::Sin(BendAngle);
                    
                    DeformedLocation.X = BendCenter.X + RelativePos.X * CosAngle - RelativePos.Z * SinAngle;
                    DeformedLocation.Z = BendCenter.Z + RelativePos.X * SinAngle + RelativePos.Z * CosAngle;
                    break;
                }
                default:
                    break;
            }

            SplineComponent->SetLocationAtSplinePoint(i, DeformedLocation, ESplineCoordinateSpace::World);
        }

        SplineComponent->UpdateSpline();
    }

    // =============================================================================
    // ADAPTIVE POINT GENERATION
    // =============================================================================

    TArray<FVector> GenerateAdaptivePoints(USplineComponent* SplineComponent, float CurvatureThreshold, float MinSpacing, float MaxSpacing)
    {
        TArray<FVector> AdaptivePoints;
        
        if (!SplineComponent)
        {
            return AdaptivePoints;
        }

        float SplineLength = SplineComponent->GetSplineLength();
        float CurrentDistance = 0.0f;

        AdaptivePoints.Add(SplineComponent->GetLocationAtDistanceAlongSpline(0.0f, ESplineCoordinateSpace::World));

        while (CurrentDistance < SplineLength)
        {
            // Calculate curvature at current position
            float Curvature = UAuracronPCGSplineSystemUtils::CalculateSplineCurvature(SplineComponent, CurrentDistance);
            
            // Determine spacing based on curvature
            float Spacing = MaxSpacing;
            if (Curvature > CurvatureThreshold)
            {
                float CurvatureFactor = FMath::Clamp(Curvature / CurvatureThreshold, 1.0f, 10.0f);
                Spacing = FMath::Lerp(MaxSpacing, MinSpacing, (CurvatureFactor - 1.0f) / 9.0f);
            }

            CurrentDistance += Spacing;
            
            if (CurrentDistance < SplineLength)
            {
                FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(CurrentDistance, ESplineCoordinateSpace::World);
                AdaptivePoints.Add(Location);
            }
        }

        // Ensure we include the end point
        if (AdaptivePoints.Num() > 0)
        {
            FVector EndLocation = SplineComponent->GetLocationAtDistanceAlongSpline(SplineLength, ESplineCoordinateSpace::World);
            if (FVector::Dist(AdaptivePoints.Last(), EndLocation) > MinSpacing)
            {
                AdaptivePoints.Add(EndLocation);
            }
        }

        return AdaptivePoints;
    }

    // =============================================================================
    // PATH FINDING IMPLEMENTATIONS
    // =============================================================================

    bool PerformAStarPathFinding(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& Descriptor, TArray<FVector>& OutPath)
    {
        OutPath.Empty();

        // Simplified A* implementation
        // In production, you'd implement a proper A* algorithm with grid-based or graph-based pathfinding

        // For now, create a simple path with waypoints
        OutPath.Add(Start);

        // Add waypoints if provided
        for (const FVector& Waypoint : Descriptor.Waypoints)
        {
            OutPath.Add(Waypoint);
        }

        OutPath.Add(End);

        // Apply obstacle avoidance (simplified)
        if (Descriptor.bAvoidObstacles)
        {
            ApplyObstacleAvoidance(OutPath, Descriptor);
        }

        return OutPath.Num() >= 2;
    }

    bool PerformDijkstraPathFinding(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& Descriptor, TArray<FVector>& OutPath)
    {
        OutPath.Empty();

        // Simplified Dijkstra implementation
        // In production, you'd implement proper Dijkstra's algorithm

        // For now, create a simple direct path
        OutPath.Add(Start);
        
        // Add intermediate points based on distance
        FVector Direction = (End - Start).GetSafeNormal();
        float Distance = FVector::Dist(Start, End);
        int32 NumIntermediatePoints = FMath::CeilToInt(Distance / 100.0f); // Point every 100 units

        for (int32 i = 1; i < NumIntermediatePoints; i++)
        {
            float T = static_cast<float>(i) / NumIntermediatePoints;
            FVector IntermediatePoint = FMath::Lerp(Start, End, T);
            OutPath.Add(IntermediatePoint);
        }

        OutPath.Add(End);

        return OutPath.Num() >= 2;
    }

    // =============================================================================
    // SPLINE OPTIMIZATION
    // =============================================================================

    void OptimizeSplineTangents(USplineComponent* SplineComponent, float SmoothingFactor)
    {
        if (!SplineComponent)
        {
            return;
        }

        int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
        
        if (NumPoints < 3)
        {
            return;
        }

        // Optimize tangents for smoother curves
        for (int32 i = 1; i < NumPoints - 1; i++)
        {
            FVector PrevPoint = SplineComponent->GetLocationAtSplinePoint(i - 1, ESplineCoordinateSpace::Local);
            FVector CurrentPoint = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::Local);
            FVector NextPoint = SplineComponent->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::Local);

            // Calculate smooth tangent
            FVector SmoothTangent = (NextPoint - PrevPoint) * 0.5f * SmoothingFactor;

            SplineComponent->SetTangentsAtSplinePoint(i, SmoothTangent, SmoothTangent, ESplineCoordinateSpace::Local);
        }

        SplineComponent->UpdateSpline();
    }

    // =============================================================================
    // COLLISION GENERATION
    // =============================================================================

    bool GenerateSplineCollision(USplineComponent* SplineComponent, float CollisionWidth, bool bComplexCollision)
    {
        if (!SplineComponent)
        {
            return false;
        }

        // Generate collision along spline
        // Simplified implementation - in production you'd create proper collision geometry

        float SplineLength = SplineComponent->GetSplineLength();
        int32 NumSegments = FMath::CeilToInt(SplineLength / 100.0f); // Collision segment every 100 units

        for (int32 i = 0; i < NumSegments; i++)
        {
            float Distance = (static_cast<float>(i) / NumSegments) * SplineLength;
            FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FVector Direction = SplineComponent->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);

            // Create collision box at this location
            // In production, you'd create actual collision components
        }

        return true;
    }

    // =============================================================================
    // LOD MANAGEMENT
    // =============================================================================

    void UpdateSplineMeshLODs(USplineMeshComponent* SplineMeshComponent, const TArray<float>& LODDistances)
    {
        if (!SplineMeshComponent)
        {
            return;
        }

        // Update LOD distances for spline mesh component
        // Simplified implementation - in production you'd properly configure LODs

        for (int32 i = 0; i < LODDistances.Num(); i++)
        {
            // Set LOD distance
            // In production, you'd use proper LOD configuration
        }
    }

    // =============================================================================
    // HELPER FUNCTIONS
    // =============================================================================

    void ApplyObstacleAvoidance(TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& Descriptor)
    {
        // Simplified obstacle avoidance
        // In production, you'd implement proper obstacle detection and avoidance

        for (int32 i = 1; i < Path.Num() - 1; i++)
        {
            // Check for obstacles around this point
            FVector CurrentPoint = Path[i];
            
            // Apply simple avoidance by adding random offset
            FRandomStream RandomStream(i);
            FVector AvoidanceOffset = FVector(
                RandomStream.FRandRange(-Descriptor.ObstacleAvoidanceRadius, Descriptor.ObstacleAvoidanceRadius),
                RandomStream.FRandRange(-Descriptor.ObstacleAvoidanceRadius, Descriptor.ObstacleAvoidanceRadius),
                0.0f
            ) * 0.1f; // Small avoidance

            Path[i] = CurrentPoint + AvoidanceOffset;
        }
    }
}
