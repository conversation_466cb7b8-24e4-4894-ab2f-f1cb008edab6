// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Material System Core Implementation
// Bridge 2.11: PCG Framework - Material Assignment

#include "AuracronPCGMaterialSystem.h"
#include "AuracronPCGBridge.h"

// Engine includes
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/Texture.h"
#include "Curves/CurveFloat.h"
#include "Curves/CurveLinearColor.h"

namespace AuracronPCGMaterialSystemUtils
{
    // =============================================================================
    // SAFE LOADING FUNCTIONS
    // =============================================================================

    UMaterialInterface* LoadMaterialSafe(const TSoftObjectPtr<UMaterialInterface>& MaterialPtr)
    {
        if (!MaterialPtr.IsValid())
        {
            return nullptr;
        }

        UMaterialInterface* Material = MaterialPtr.LoadSynchronous();
        if (!Material)
        {
            // Try to load asynchronously if synchronous failed
            Material = MaterialPtr.Get();
        }

        return Material;
    }

    UTexture* LoadTextureSafe(const TSoftObjectPtr<UTexture>& TexturePtr)
    {
        if (!TexturePtr.IsValid())
        {
            return nullptr;
        }

        UTexture* Texture = TexturePtr.LoadSynchronous();
        if (!Texture)
        {
            // Try to load asynchronously if synchronous failed
            Texture = TexturePtr.Get();
        }

        return Texture;
    }

    UCurveFloat* LoadCurveSafe(const TSoftObjectPtr<UCurveFloat>& CurvePtr)
    {
        if (!CurvePtr.IsValid())
        {
            return nullptr;
        }

        UCurveFloat* Curve = CurvePtr.LoadSynchronous();
        if (!Curve)
        {
            // Try to load asynchronously if synchronous failed
            Curve = CurvePtr.Get();
        }

        return Curve;
    }

    // =============================================================================
    // SELECTION CRITERIA EVALUATION
    // =============================================================================

    float EvaluateSelectionCriteria(const FPCGPoint& Point, const FAuracronPCGMaterialSelectionDescriptor& Descriptor)
    {
        switch (Descriptor.SelectionMode)
        {
            case EAuracronPCGMaterialSelectionMode::ByHeight:
                return CalculateHeightFactor(Point.Transform.GetLocation(), Descriptor.HeightThresholds);
            case EAuracronPCGMaterialSelectionMode::BySlope:
                return CalculateSlopeFactor(Point.Transform.GetRotation().GetUpVector(), Descriptor.SlopeThresholds);
            case EAuracronPCGMaterialSelectionMode::ByDistance:
                return CalculateDistanceFactor(Point.Transform.GetLocation(), Descriptor.ReferenceLocation, Descriptor.DistanceThresholds);
            case EAuracronPCGMaterialSelectionMode::ByDensity:
                return Point.Density;
            default:
                return 1.0f;
        }
    }

    float CalculateHeightFactor(const FVector& Position, const TArray<float>& HeightThresholds)
    {
        if (HeightThresholds.Num() == 0)
        {
            return 1.0f;
        }

        float Height = Position.Z;
        float MinHeight = HeightThresholds[0];
        float MaxHeight = HeightThresholds.Last();

        if (Height <= MinHeight)
        {
            return 0.0f;
        }
        else if (Height >= MaxHeight)
        {
            return 1.0f;
        }
        else
        {
            return (Height - MinHeight) / (MaxHeight - MinHeight);
        }
    }

    float CalculateSlopeFactor(const FVector& Normal, const TArray<float>& SlopeThresholds)
    {
        if (SlopeThresholds.Num() == 0)
        {
            return 1.0f;
        }

        float SlopeAngle = FMath::Acos(FVector::DotProduct(Normal, FVector::UpVector)) * 180.0f / PI;
        float MinSlope = SlopeThresholds[0];
        float MaxSlope = SlopeThresholds.Last();

        if (SlopeAngle <= MinSlope)
        {
            return 0.0f;
        }
        else if (SlopeAngle >= MaxSlope)
        {
            return 1.0f;
        }
        else
        {
            return (SlopeAngle - MinSlope) / (MaxSlope - MinSlope);
        }
    }

    float CalculateDistanceFactor(const FVector& Position, const FVector& Reference, const TArray<float>& DistanceThresholds)
    {
        if (DistanceThresholds.Num() == 0)
        {
            return 1.0f;
        }

        float Distance = FVector::Dist(Position, Reference);
        float MinDistance = DistanceThresholds[0];
        float MaxDistance = DistanceThresholds.Last();

        if (Distance <= MinDistance)
        {
            return 1.0f;
        }
        else if (Distance >= MaxDistance)
        {
            return 0.0f;
        }
        else
        {
            return 1.0f - (Distance - MinDistance) / (MaxDistance - MinDistance);
        }
    }

    // =============================================================================
    // UV TRANSFORMATION FUNCTIONS
    // =============================================================================

    FVector2D ApplyUVTransform(const FVector2D& UV, const FVector2D& Scale, const FVector2D& Offset, float Rotation)
    {
        FVector2D TransformedUV = UV;

        // Apply scale
        TransformedUV.X *= Scale.X;
        TransformedUV.Y *= Scale.Y;

        // Apply rotation
        if (FMath::Abs(Rotation) > SMALL_NUMBER)
        {
            float CosRot = FMath::Cos(FMath::DegreesToRadians(Rotation));
            float SinRot = FMath::Sin(FMath::DegreesToRadians(Rotation));
            
            float NewU = TransformedUV.X * CosRot - TransformedUV.Y * SinRot;
            float NewV = TransformedUV.X * SinRot + TransformedUV.Y * CosRot;
            
            TransformedUV.X = NewU;
            TransformedUV.Y = NewV;
        }

        // Apply offset
        TransformedUV.X += Offset.X;
        TransformedUV.Y += Offset.Y;

        return TransformedUV;
    }

    FVector2D WrapUVCoordinates(const FVector2D& UV, bool bWrapU, bool bWrapV)
    {
        FVector2D WrappedUV = UV;

        if (bWrapU)
        {
            WrappedUV.X = FMath::Fmod(WrappedUV.X, 1.0f);
            if (WrappedUV.X < 0.0f)
            {
                WrappedUV.X += 1.0f;
            }
        }

        if (bWrapV)
        {
            WrappedUV.Y = FMath::Fmod(WrappedUV.Y, 1.0f);
            if (WrappedUV.Y < 0.0f)
            {
                WrappedUV.Y += 1.0f;
            }
        }

        return WrappedUV;
    }

    FVector2D FlipUVCoordinates(const FVector2D& UV, bool bFlipU, bool bFlipV)
    {
        FVector2D FlippedUV = UV;

        if (bFlipU)
        {
            FlippedUV.X = 1.0f - FlippedUV.X;
        }

        if (bFlipV)
        {
            FlippedUV.Y = 1.0f - FlippedUV.Y;
        }

        return FlippedUV;
    }

    // =============================================================================
    // MATERIAL BLENDING FUNCTIONS
    // =============================================================================

    float BlendMaterialValues(float ValueA, float ValueB, EAuracronPCGMaterialBlendMode BlendMode, float BlendFactor)
    {
        switch (BlendMode)
        {
            case EAuracronPCGMaterialBlendMode::Replace:
                return FMath::Lerp(ValueA, ValueB, BlendFactor);
            case EAuracronPCGMaterialBlendMode::Additive:
                return ValueA + ValueB * BlendFactor;
            case EAuracronPCGMaterialBlendMode::Multiply:
                return ValueA * FMath::Lerp(1.0f, ValueB, BlendFactor);
            case EAuracronPCGMaterialBlendMode::Overlay:
                if (ValueA < 0.5f)
                {
                    return FMath::Lerp(ValueA, 2.0f * ValueA * ValueB, BlendFactor);
                }
                else
                {
                    return FMath::Lerp(ValueA, 1.0f - 2.0f * (1.0f - ValueA) * (1.0f - ValueB), BlendFactor);
                }
            case EAuracronPCGMaterialBlendMode::SoftLight:
                return FMath::Lerp(ValueA, ValueA * (1.0f - ValueB) + FMath::Sqrt(ValueA) * ValueB, BlendFactor);
            case EAuracronPCGMaterialBlendMode::HardLight:
                if (ValueB < 0.5f)
                {
                    return FMath::Lerp(ValueA, 2.0f * ValueA * ValueB, BlendFactor);
                }
                else
                {
                    return FMath::Lerp(ValueA, 1.0f - 2.0f * (1.0f - ValueA) * (1.0f - ValueB), BlendFactor);
                }
            case EAuracronPCGMaterialBlendMode::Screen:
                return FMath::Lerp(ValueA, 1.0f - (1.0f - ValueA) * (1.0f - ValueB), BlendFactor);
            case EAuracronPCGMaterialBlendMode::ColorBurn:
                return FMath::Lerp(ValueA, ValueB > 0.0f ? 1.0f - (1.0f - ValueA) / ValueB : 0.0f, BlendFactor);
            case EAuracronPCGMaterialBlendMode::ColorDodge:
                return FMath::Lerp(ValueA, ValueB < 1.0f ? ValueA / (1.0f - ValueB) : 1.0f, BlendFactor);
            case EAuracronPCGMaterialBlendMode::Difference:
                return FMath::Lerp(ValueA, FMath::Abs(ValueA - ValueB), BlendFactor);
            case EAuracronPCGMaterialBlendMode::Exclusion:
                return FMath::Lerp(ValueA, ValueA + ValueB - 2.0f * ValueA * ValueB, BlendFactor);
            default:
                return FMath::Lerp(ValueA, ValueB, BlendFactor);
        }
    }

    FLinearColor BlendMaterialColors(const FLinearColor& ColorA, const FLinearColor& ColorB, EAuracronPCGMaterialBlendMode BlendMode, float BlendFactor)
    {
        FLinearColor Result;
        Result.R = BlendMaterialValues(ColorA.R, ColorB.R, BlendMode, BlendFactor);
        Result.G = BlendMaterialValues(ColorA.G, ColorB.G, BlendMode, BlendFactor);
        Result.B = BlendMaterialValues(ColorA.B, ColorB.B, BlendMode, BlendFactor);
        Result.A = FMath::Lerp(ColorA.A, ColorB.A, BlendFactor); // Alpha always uses linear blend
        return Result;
    }

    // =============================================================================
    // MATERIAL PARAMETER FUNCTIONS
    // =============================================================================

    void SetScalarParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, float Value)
    {
        if (MaterialInstance && !ParameterName.IsEmpty())
        {
            MaterialInstance->SetScalarParameterValue(*ParameterName, Value);
        }
    }

    void SetVectorParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, const FVector& Value)
    {
        if (MaterialInstance && !ParameterName.IsEmpty())
        {
            MaterialInstance->SetVectorParameterValue(*ParameterName, FLinearColor(Value.X, Value.Y, Value.Z, 1.0f));
        }
    }

    void SetColorParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, const FLinearColor& Value)
    {
        if (MaterialInstance && !ParameterName.IsEmpty())
        {
            MaterialInstance->SetVectorParameterValue(*ParameterName, Value);
        }
    }

    void SetTextureParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, UTexture* Value)
    {
        if (MaterialInstance && !ParameterName.IsEmpty() && Value)
        {
            MaterialInstance->SetTextureParameterValue(*ParameterName, Value);
        }
    }

    // =============================================================================
    // UTILITY FUNCTIONS
    // =============================================================================

    FString GenerateMaterialInstanceName(const FString& BaseName, int32 Index)
    {
        return FString::Printf(TEXT("%s_Instance_%d"), *BaseName, Index);
    }

    bool IsMaterialCompatible(UMaterialInterface* Material, const TArray<FString>& RequiredParameters)
    {
        if (!Material)
        {
            return false;
        }

        // In production, you'd check if the material has the required parameters
        // This is a simplified implementation
        TArray<FString> MaterialParameters = GetMaterialParameterNames(Material);
        
        for (const FString& RequiredParam : RequiredParameters)
        {
            if (!MaterialParameters.Contains(RequiredParam))
            {
                return false;
            }
        }

        return true;
    }

    TArray<FString> GetMaterialParameterNames(UMaterialInterface* Material)
    {
        TArray<FString> ParameterNames;
        
        if (!Material)
        {
            return ParameterNames;
        }

        // In production, you'd extract actual parameter names from the material
        // This is a simplified implementation with common parameter names
        ParameterNames.Add(TEXT("BaseColor"));
        ParameterNames.Add(TEXT("Metallic"));
        ParameterNames.Add(TEXT("Roughness"));
        ParameterNames.Add(TEXT("Normal"));
        ParameterNames.Add(TEXT("Emissive"));
        ParameterNames.Add(TEXT("Opacity"));
        ParameterNames.Add(TEXT("OpacityMask"));
        ParameterNames.Add(TEXT("Subsurface"));
        ParameterNames.Add(TEXT("AmbientOcclusion"));
        ParameterNames.Add(TEXT("Refraction"));
        ParameterNames.Add(TEXT("CustomData0"));
        ParameterNames.Add(TEXT("CustomData1"));

        return ParameterNames;
    }

    // =============================================================================
    // ADVANCED UV GENERATION FUNCTIONS
    // =============================================================================

    FVector2D GenerateBoxProjectionUV(const FVector& Position, const FVector& BoxCenter, const FVector& BoxExtent, const FVector& Normal)
    {
        FVector RelativePos = Position - BoxCenter;
        FVector AbsNormal = Normal.GetAbs();
        
        FVector2D UV;
        
        // Determine dominant axis
        if (AbsNormal.X > AbsNormal.Y && AbsNormal.X > AbsNormal.Z)
        {
            // X-axis dominant
            UV.X = (RelativePos.Y + BoxExtent.Y) / (2.0f * BoxExtent.Y);
            UV.Y = (RelativePos.Z + BoxExtent.Z) / (2.0f * BoxExtent.Z);
        }
        else if (AbsNormal.Y > AbsNormal.Z)
        {
            // Y-axis dominant
            UV.X = (RelativePos.X + BoxExtent.X) / (2.0f * BoxExtent.X);
            UV.Y = (RelativePos.Z + BoxExtent.Z) / (2.0f * BoxExtent.Z);
        }
        else
        {
            // Z-axis dominant
            UV.X = (RelativePos.X + BoxExtent.X) / (2.0f * BoxExtent.X);
            UV.Y = (RelativePos.Y + BoxExtent.Y) / (2.0f * BoxExtent.Y);
        }
        
        return UV;
    }

    TArray<float> CalculateTriplanarBlendWeights(const FVector& Normal, float BlendSharpness)
    {
        TArray<float> BlendWeights;
        
        FVector AbsNormal = Normal.GetAbs();
        
        // Calculate raw weights
        float WeightX = FMath::Pow(AbsNormal.X, BlendSharpness);
        float WeightY = FMath::Pow(AbsNormal.Y, BlendSharpness);
        float WeightZ = FMath::Pow(AbsNormal.Z, BlendSharpness);
        
        // Normalize weights
        float TotalWeight = WeightX + WeightY + WeightZ;
        if (TotalWeight > 0.0f)
        {
            WeightX /= TotalWeight;
            WeightY /= TotalWeight;
            WeightZ /= TotalWeight;
        }
        
        BlendWeights.Add(WeightX);
        BlendWeights.Add(WeightY);
        BlendWeights.Add(WeightZ);
        
        return BlendWeights;
    }

    FVector2D GenerateProceduralUV(const FVector& Position, float NoiseScale, int32 Seed)
    {
        FRandomStream RandomStream(Seed);
        
        // Simple procedural UV generation using position-based noise
        float U = FMath::Sin(Position.X * NoiseScale + RandomStream.FRand() * 2.0f * PI) * 0.5f + 0.5f;
        float V = FMath::Sin(Position.Y * NoiseScale + RandomStream.FRand() * 2.0f * PI) * 0.5f + 0.5f;
        
        return FVector2D(U, V);
    }

    FVector2D ApplyUVDistortion(const FVector2D& UV, const FVector& Position, float DistortionStrength, float DistortionScale, int32 Seed)
    {
        FRandomStream RandomStream(Seed);
        
        // Generate distortion offsets
        float DistortionU = FMath::Sin(Position.X * DistortionScale + RandomStream.FRand() * 2.0f * PI) * DistortionStrength;
        float DistortionV = FMath::Sin(Position.Y * DistortionScale + RandomStream.FRand() * 2.0f * PI) * DistortionStrength;
        
        return FVector2D(UV.X + DistortionU, UV.Y + DistortionV);
    }
}
