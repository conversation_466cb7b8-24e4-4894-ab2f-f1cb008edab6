// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Streaming System Implementation
// Bridge 3.3: World Partition - Streaming System

#include "AuracronWorldPartitionStreaming.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionBridge.h"

// World Partition includes
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/WorldPartitionLevelStreamingDynamic.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/LevelStreaming.h"
#include "HAL/PlatformMemory.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"

// =============================================================================
// STREAMING REQUEST IMPLEMENTATION
// =============================================================================

float FAuracronStreamingRequest::CalculatePriorityScore() const
{
    float Score = 0.0f;
    
    // Base priority score
    switch (Priority)
    {
        case EAuracronStreamingPriority::Lowest: Score = 1.0f; break;
        case EAuracronStreamingPriority::Low: Score = 2.0f; break;
        case EAuracronStreamingPriority::Normal: Score = 3.0f; break;
        case EAuracronStreamingPriority::High: Score = 4.0f; break;
        case EAuracronStreamingPriority::Highest: Score = 5.0f; break;
        case EAuracronStreamingPriority::Critical: Score = 10.0f; break;
    }
    
    // Distance factor (closer = higher priority)
    if (DistanceFromSource > 0.0f)
    {
        Score *= (1.0f / (1.0f + DistanceFromSource / 10000.0f)); // Normalize by 10km
    }
    
    // Request type factor
    switch (RequestType)
    {
        case EAuracronStreamingRequestType::ForceLoad:
        case EAuracronStreamingRequestType::ForceUnload:
            Score *= 2.0f;
            break;
        case EAuracronStreamingRequestType::Load:
        case EAuracronStreamingRequestType::Unload:
            Score *= 1.0f;
            break;
        case EAuracronStreamingRequestType::Preload:
            Score *= 0.5f;
            break;
    }
    
    // Age factor (older requests get higher priority)
    FDateTime Now = FDateTime::Now();
    float AgeSeconds = (Now - CreationTime).GetTotalSeconds();
    Score *= (1.0f + AgeSeconds / 60.0f); // Increase priority over time
    
    return Score;
}

bool FAuracronStreamingRequest::IsExpired(float TimeoutSeconds) const
{
    FDateTime Now = FDateTime::Now();
    float AgeSeconds = (Now - CreationTime).GetTotalSeconds();
    return AgeSeconds > TimeoutSeconds;
}

// =============================================================================
// STREAMING SOURCE IMPLEMENTATION
// =============================================================================

FVector FAuracronStreamingSource::GetPredictedLocation() const
{
    if (bEnablePrediction && !Velocity.IsZero())
    {
        return Location + (Velocity * PredictionTime);
    }
    return Location;
}

bool FAuracronStreamingSource::IsLocationInRange(const FVector& TestLocation) const
{
    FVector EffectiveLocation = bEnablePrediction ? GetPredictedLocation() : Location;
    return FVector::Dist(EffectiveLocation, TestLocation) <= StreamingRadius;
}

// =============================================================================
// STREAMING STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronStreamingStatistics::UpdateCalculatedFields()
{
    if (TotalStreamingRequests > 0)
    {
        StreamingEfficiency = static_cast<float>(CompletedRequests) / static_cast<float>(TotalStreamingRequests);
    }
    else
    {
        StreamingEfficiency = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// WORLD PARTITION STREAMING MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionStreamingManager* UAuracronWorldPartitionStreamingManager::Instance = nullptr;

UAuracronWorldPartitionStreamingManager* UAuracronWorldPartitionStreamingManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionStreamingManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionStreamingManager::Initialize(const FAuracronStreamingConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Streaming Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize statistics
    Statistics = FAuracronStreamingStatistics();
    
    // Clear collections
    StreamingRequests.Empty();
    StreamingSources.Empty();
    ActiveRequests.Empty();
    
    // Initialize timing
    LastPriorityUpdateTime = 0.0f;
    LastMemoryUpdateTime = 0.0f;
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Streaming Manager initialized with max concurrent requests: %d"), Configuration.MaxConcurrentStreamingRequests);
}

void UAuracronWorldPartitionStreamingManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Cancel all pending requests
    for (auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Pending ||
            RequestPair.Value.State == EAuracronStreamingRequestState::Processing)
        {
            RequestPair.Value.State = EAuracronStreamingRequestState::Cancelled;
        }
    }
    
    // Clear all data
    StreamingRequests.Empty();
    StreamingSources.Empty();
    ActiveRequests.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Streaming Manager shutdown completed"));
}

bool UAuracronWorldPartitionStreamingManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionStreamingManager::Tick(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update priority system
    if (Configuration.bEnablePrioritySystem)
    {
        LastPriorityUpdateTime += DeltaTime;
        if (LastPriorityUpdateTime >= Configuration.PriorityUpdateInterval)
        {
            UpdateRequestPriorities();
            LastPriorityUpdateTime = 0.0f;
        }
    }
    
    // Update memory management
    LastMemoryUpdateTime += DeltaTime;
    if (LastMemoryUpdateTime >= 1.0f) // Update every second
    {
        UpdateMemoryManagement();
        LastMemoryUpdateTime = 0.0f;
    }
    
    // Update distance-based streaming
    if (Configuration.bEnableDistanceBasedStreaming)
    {
        UpdateDistanceBasedStreaming();
    }
    
    // Process streaming requests
    ProcessStreamingRequests();
    
    // Update statistics
    UpdateStatistics();
}

FString UAuracronWorldPartitionStreamingManager::RequestCellLoading(const FString& CellId, EAuracronStreamingPriority Priority)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot request cell loading: Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&StreamingLock);
    
    // Check if cell is already being loaded
    for (const auto& RequestPair : StreamingRequests)
    {
        const FAuracronStreamingRequest& Request = RequestPair.Value;
        if (Request.CellId == CellId && 
            Request.RequestType == EAuracronStreamingRequestType::Load &&
            (Request.State == EAuracronStreamingRequestState::Pending || Request.State == EAuracronStreamingRequestState::Processing))
        {
            AURACRON_WP_LOG_VERBOSE(TEXT("Cell %s already has pending load request"), *CellId);
            return Request.RequestId;
        }
    }
    
    // Create new request
    FAuracronStreamingRequest Request;
    Request.RequestId = GenerateRequestId();
    Request.CellId = CellId;
    Request.RequestType = EAuracronStreamingRequestType::Load;
    Request.Priority = Priority;
    Request.State = EAuracronStreamingRequestState::Pending;
    Request.CreationTime = FDateTime::Now();
    
    // Add to collections
    StreamingRequests.Add(Request.RequestId, Request);
    PendingRequestQueue.Enqueue(Request.RequestId);
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Cell loading requested: %s (Request: %s)"), *CellId, *Request.RequestId);
    
    return Request.RequestId;
}

FString UAuracronWorldPartitionStreamingManager::RequestCellUnloading(const FString& CellId, EAuracronStreamingPriority Priority)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot request cell unloading: Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&StreamingLock);
    
    // Check if cell is already being unloaded
    for (const auto& RequestPair : StreamingRequests)
    {
        const FAuracronStreamingRequest& Request = RequestPair.Value;
        if (Request.CellId == CellId && 
            Request.RequestType == EAuracronStreamingRequestType::Unload &&
            (Request.State == EAuracronStreamingRequestState::Pending || Request.State == EAuracronStreamingRequestState::Processing))
        {
            AURACRON_WP_LOG_VERBOSE(TEXT("Cell %s already has pending unload request"), *CellId);
            return Request.RequestId;
        }
    }
    
    // Create new request
    FAuracronStreamingRequest Request;
    Request.RequestId = GenerateRequestId();
    Request.CellId = CellId;
    Request.RequestType = EAuracronStreamingRequestType::Unload;
    Request.Priority = Priority;
    Request.State = EAuracronStreamingRequestState::Pending;
    Request.CreationTime = FDateTime::Now();
    
    // Add to collections
    StreamingRequests.Add(Request.RequestId, Request);
    PendingRequestQueue.Enqueue(Request.RequestId);
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Cell unloading requested: %s (Request: %s)"), *CellId, *Request.RequestId);
    
    return Request.RequestId;
}

FString UAuracronWorldPartitionStreamingManager::RequestCellPreloading(const FString& CellId, EAuracronStreamingPriority Priority)
{
    if (!bIsInitialized)
    {
        return FString();
    }

    FScopeLock Lock(&StreamingLock);
    
    // Create preload request
    FAuracronStreamingRequest Request;
    Request.RequestId = GenerateRequestId();
    Request.CellId = CellId;
    Request.RequestType = EAuracronStreamingRequestType::Preload;
    Request.Priority = Priority;
    Request.State = EAuracronStreamingRequestState::Pending;
    Request.CreationTime = FDateTime::Now();
    
    // Add to collections
    StreamingRequests.Add(Request.RequestId, Request);
    PendingRequestQueue.Enqueue(Request.RequestId);
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Cell preloading requested: %s (Request: %s)"), *CellId, *Request.RequestId);
    
    return Request.RequestId;
}

bool UAuracronWorldPartitionStreamingManager::CancelStreamingRequest(const FString& RequestId)
{
    FScopeLock Lock(&StreamingLock);
    
    FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (!Request)
    {
        return false;
    }
    
    if (Request->State == EAuracronStreamingRequestState::Pending)
    {
        Request->State = EAuracronStreamingRequestState::Cancelled;
        Request->CompletionTime = FDateTime::Now();
        
        AURACRON_WP_LOG_VERBOSE(TEXT("Streaming request cancelled: %s"), *RequestId);
        return true;
    }
    
    return false;
}

void UAuracronWorldPartitionStreamingManager::ProcessStreamingRequests()
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&StreamingLock);
    
    // Process requests up to the concurrent limit
    int32 ProcessedThisFrame = 0;
    
    while (!PendingRequestQueue.IsEmpty() && 
           ActiveRequests.Num() < Configuration.MaxConcurrentStreamingRequests &&
           ProcessedThisFrame < Configuration.MaxCellsPerFrame)
    {
        FString RequestId;
        if (PendingRequestQueue.Dequeue(RequestId))
        {
            FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
            if (Request && ShouldProcessRequest(*Request))
            {
                ProcessSingleRequest(RequestId);
                ProcessedThisFrame++;
            }
        }
    }
}

FString UAuracronWorldPartitionStreamingManager::AddStreamingSource(const FAuracronStreamingSource& StreamingSource)
{
    FScopeLock Lock(&StreamingLock);

    FString SourceId = StreamingSource.SourceId;
    if (SourceId.IsEmpty())
    {
        SourceId = FString::Printf(TEXT("Source_%d"), StreamingSources.Num());
    }

    FAuracronStreamingSource NewSource = StreamingSource;
    NewSource.SourceId = SourceId;
    NewSource.LastUpdateTime = FDateTime::Now();

    StreamingSources.Add(SourceId, NewSource);

    AURACRON_WP_LOG_VERBOSE(TEXT("Streaming source added: %s"), *SourceId);

    return SourceId;
}

bool UAuracronWorldPartitionStreamingManager::RemoveStreamingSource(const FString& SourceId)
{
    FScopeLock Lock(&StreamingLock);

    bool bRemoved = StreamingSources.Remove(SourceId) > 0;

    if (bRemoved)
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Streaming source removed: %s"), *SourceId);
    }

    return bRemoved;
}

void UAuracronWorldPartitionStreamingManager::UpdateStreamingSource(const FString& SourceId, const FVector& Location, const FVector& Velocity)
{
    FScopeLock Lock(&StreamingLock);

    FAuracronStreamingSource* Source = StreamingSources.Find(SourceId);
    if (Source)
    {
        Source->Location = Location;
        Source->Velocity = Velocity;
        Source->LastUpdateTime = FDateTime::Now();
    }
}

TArray<FAuracronStreamingSource> UAuracronWorldPartitionStreamingManager::GetActiveStreamingSources() const
{
    FScopeLock Lock(&StreamingLock);

    TArray<FAuracronStreamingSource> ActiveSources;

    for (const auto& SourcePair : StreamingSources)
    {
        if (SourcePair.Value.bIsActive)
        {
            ActiveSources.Add(SourcePair.Value);
        }
    }

    return ActiveSources;
}

void UAuracronWorldPartitionStreamingManager::UpdateDistanceBasedStreaming()
{
    if (!Configuration.bEnableDistanceBasedStreaming)
    {
        return;
    }

    TArray<FAuracronStreamingSource> ActiveSources = GetActiveStreamingSources();
    if (ActiveSources.Num() == 0)
    {
        return;
    }

    // Get grid manager for cell queries
    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager || !GridManager->IsInitialized())
    {
        return;
    }

    // Process each streaming source
    for (const FAuracronStreamingSource& Source : ActiveSources)
    {
        FVector EffectiveLocation = Source.GetPredictedLocation();

        // Get cells in streaming range
        TArray<FString> CellsInRange = GetCellsInStreamingRange(EffectiveLocation, Configuration.StreamingDistance);

        // Request loading for cells in range
        for (const FString& CellId : CellsInRange)
        {
            float Distance = FVector::Dist(EffectiveLocation, GridManager->GetCell(CellId).GetCenter());
            EAuracronStreamingPriority Priority = EAuracronStreamingPriority::Normal;

            // Adjust priority based on distance
            if (Distance < Configuration.StreamingDistance * 0.3f)
            {
                Priority = EAuracronStreamingPriority::High;
            }
            else if (Distance < Configuration.StreamingDistance * 0.6f)
            {
                Priority = EAuracronStreamingPriority::Normal;
            }
            else
            {
                Priority = EAuracronStreamingPriority::Low;
            }

            RequestCellLoading(CellId, Priority);
        }

        // Get cells beyond unloading distance
        TArray<FAuracronGridCell> AllCells = GridManager->GetAllCells();
        for (const FAuracronGridCell& Cell : AllCells)
        {
            float Distance = FVector::Dist(EffectiveLocation, Cell.GetCenter());

            if (Distance > Configuration.UnloadingDistance)
            {
                RequestCellUnloading(Cell.CellId, EAuracronStreamingPriority::Low);
            }
        }
    }
}

TArray<FString> UAuracronWorldPartitionStreamingManager::GetCellsInStreamingRange(const FVector& Location, float Radius) const
{
    TArray<FString> CellsInRange;

    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (GridManager && GridManager->IsInitialized())
    {
        TArray<FAuracronGridCell> Cells = GridManager->GetCellsInRadius(Location, Radius);

        for (const FAuracronGridCell& Cell : Cells)
        {
            CellsInRange.Add(Cell.CellId);
        }
    }

    return CellsInRange;
}

float UAuracronWorldPartitionStreamingManager::CalculateCellPriority(const FString& CellId, const FVector& SourceLocation) const
{
    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager || !GridManager->IsInitialized())
    {
        return 0.0f;
    }

    FAuracronGridCell Cell = GridManager->GetCell(CellId);
    if (Cell.CellId.IsEmpty())
    {
        return 0.0f;
    }

    float Distance = FVector::Dist(SourceLocation, Cell.GetCenter());
    float Priority = 1.0f / (1.0f + Distance / 1000.0f); // Normalize by 1km

    // Adjust for cell density
    Priority *= (1.0f + Cell.Density);

    return Priority;
}

void UAuracronWorldPartitionStreamingManager::UpdateMemoryManagement()
{
    float CurrentMemory = CalculateMemoryUsage();
    float MemoryPressure = CurrentMemory / Configuration.MaxMemoryUsageMB;

    {
        FScopeLock Lock(&StatisticsLock);
        Statistics.CurrentMemoryUsageMB = CurrentMemory;
        Statistics.MemoryPressure = MemoryPressure;
        Statistics.PeakMemoryUsageMB = FMath::Max(Statistics.PeakMemoryUsageMB, CurrentMemory);
    }

    // Handle memory pressure
    if (MemoryPressure > Configuration.MemoryPressureThreshold)
    {
        HandleMemoryPressure();
        OnMemoryPressureChanged.Broadcast(MemoryPressure);
    }
}

bool UAuracronWorldPartitionStreamingManager::IsMemoryPressureHigh() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.MemoryPressure > Configuration.MemoryPressureThreshold;
}

void UAuracronWorldPartitionStreamingManager::ForceMemoryCleanup()
{
    AURACRON_WP_LOG_INFO(TEXT("Forcing memory cleanup"));

    TArray<FString> CellsToUnload = GetCellsToUnloadForMemory();

    for (const FString& CellId : CellsToUnload)
    {
        RequestCellUnloading(CellId, EAuracronStreamingPriority::High);
    }

    // Clear completed requests
    ClearCompletedRequests();
}

float UAuracronWorldPartitionStreamingManager::GetCurrentMemoryUsage() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.CurrentMemoryUsageMB;
}

float UAuracronWorldPartitionStreamingManager::GetMemoryPressure() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.MemoryPressure;
}

FAuracronStreamingRequest UAuracronWorldPartitionStreamingManager::GetStreamingRequest(const FString& RequestId) const
{
    FScopeLock Lock(&StreamingLock);

    const FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (Request)
    {
        return *Request;
    }

    return FAuracronStreamingRequest();
}

TArray<FAuracronStreamingRequest> UAuracronWorldPartitionStreamingManager::GetPendingRequests() const
{
    FScopeLock Lock(&StreamingLock);

    TArray<FAuracronStreamingRequest> PendingRequests;

    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Pending)
        {
            PendingRequests.Add(RequestPair.Value);
        }
    }

    return PendingRequests;
}

TArray<FAuracronStreamingRequest> UAuracronWorldPartitionStreamingManager::GetActiveRequests() const
{
    FScopeLock Lock(&StreamingLock);

    TArray<FAuracronStreamingRequest> ActiveRequestsList;

    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Processing)
        {
            ActiveRequestsList.Add(RequestPair.Value);
        }
    }

    return ActiveRequestsList;
}

void UAuracronWorldPartitionStreamingManager::ClearCompletedRequests()
{
    FScopeLock Lock(&StreamingLock);

    TArray<FString> RequestsToRemove;

    for (const auto& RequestPair : StreamingRequests)
    {
        const FAuracronStreamingRequest& Request = RequestPair.Value;
        if (Request.State == EAuracronStreamingRequestState::Completed ||
            Request.State == EAuracronStreamingRequestState::Failed ||
            Request.State == EAuracronStreamingRequestState::Cancelled)
        {
            RequestsToRemove.Add(Request.RequestId);
        }
    }

    for (const FString& RequestId : RequestsToRemove)
    {
        StreamingRequests.Remove(RequestId);
        ActiveRequests.Remove(RequestId);
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Cleared %d completed requests"), RequestsToRemove.Num());
}

void UAuracronWorldPartitionStreamingManager::ProcessSingleRequest(const FString& RequestId)
{
    FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (!Request)
    {
        return;
    }

    // Mark as processing
    Request->State = EAuracronStreamingRequestState::Processing;
    ActiveRequests.Add(RequestId);

    OnRequestStarted(RequestId);

    // Simulate processing time based on request type
    float ProcessingTime = 0.0f;
    bool bSuccess = true;
    FString ErrorMessage;

    switch (Request->RequestType)
    {
        case EAuracronStreamingRequestType::Load:
        case EAuracronStreamingRequestType::ForceLoad:
            ProcessingTime = FMath::RandRange(0.5f, 2.0f); // 0.5-2 seconds
            bSuccess = (FMath::RandRange(0, 100) > 5); // 95% success rate
            break;

        case EAuracronStreamingRequestType::Unload:
        case EAuracronStreamingRequestType::ForceUnload:
            ProcessingTime = FMath::RandRange(0.1f, 0.5f); // 0.1-0.5 seconds
            bSuccess = (FMath::RandRange(0, 100) > 2); // 98% success rate
            break;

        case EAuracronStreamingRequestType::Preload:
            ProcessingTime = FMath::RandRange(1.0f, 3.0f); // 1-3 seconds
            bSuccess = (FMath::RandRange(0, 100) > 10); // 90% success rate
            break;
    }

    if (!bSuccess)
    {
        ErrorMessage = TEXT("Simulated streaming failure");
    }

    Request->ProcessingTime = ProcessingTime;

    // Complete the request
    OnRequestCompleted(RequestId, bSuccess, ErrorMessage);
}

void UAuracronWorldPartitionStreamingManager::UpdateRequestPriorities()
{
    FScopeLock Lock(&StreamingLock);

    // Update priorities based on current streaming sources
    TArray<FAuracronStreamingSource> ActiveSources = GetActiveStreamingSources();

    for (auto& RequestPair : StreamingRequests)
    {
        FAuracronStreamingRequest& Request = RequestPair.Value;

        if (Request.State != EAuracronStreamingRequestState::Pending)
        {
            continue;
        }

        // Find closest streaming source
        float MinDistance = FLT_MAX;
        for (const FAuracronStreamingSource& Source : ActiveSources)
        {
            UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
            if (GridManager && GridManager->IsInitialized())
            {
                FAuracronGridCell Cell = GridManager->GetCell(Request.CellId);
                if (!Cell.CellId.IsEmpty())
                {
                    float Distance = FVector::Dist(Source.GetPredictedLocation(), Cell.GetCenter());
                    MinDistance = FMath::Min(MinDistance, Distance);
                }
            }
        }

        Request.DistanceFromSource = MinDistance;
    }
}

void UAuracronWorldPartitionStreamingManager::SetRequestPriority(const FString& RequestId, EAuracronStreamingPriority Priority)
{
    FScopeLock Lock(&StreamingLock);

    FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (Request)
    {
        Request->Priority = Priority;
    }
}

TArray<FAuracronStreamingRequest> UAuracronWorldPartitionStreamingManager::GetRequestsByPriority(EAuracronStreamingPriority Priority) const
{
    FScopeLock Lock(&StreamingLock);

    TArray<FAuracronStreamingRequest> FilteredRequests;

    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.Priority == Priority)
        {
            FilteredRequests.Add(RequestPair.Value);
        }
    }

    return FilteredRequests;
}

void UAuracronWorldPartitionStreamingManager::SetConfiguration(const FAuracronStreamingConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Streaming configuration updated"));
}

FAuracronStreamingConfiguration UAuracronWorldPartitionStreamingManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronWorldPartitionStreamingManager::SetStreamingDistance(float Distance)
{
    Configuration.StreamingDistance = FMath::Max(0.0f, Distance);

    // Adjust unloading distance if necessary
    if (Configuration.UnloadingDistance <= Configuration.StreamingDistance)
    {
        Configuration.UnloadingDistance = Configuration.StreamingDistance * 1.5f;
    }

    AURACRON_WP_LOG_INFO(TEXT("Streaming distance set to: %.1f"), Configuration.StreamingDistance);
}

float UAuracronWorldPartitionStreamingManager::GetStreamingDistance() const
{
    return Configuration.StreamingDistance;
}

FAuracronStreamingStatistics UAuracronWorldPartitionStreamingManager::GetStreamingStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronStreamingStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronWorldPartitionStreamingManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronStreamingStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Streaming statistics reset"));
}

int32 UAuracronWorldPartitionStreamingManager::GetPendingRequestCount() const
{
    FScopeLock Lock(&StreamingLock);

    int32 PendingCount = 0;
    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Pending)
        {
            PendingCount++;
        }
    }

    return PendingCount;
}

int32 UAuracronWorldPartitionStreamingManager::GetActiveRequestCount() const
{
    FScopeLock Lock(&StreamingLock);
    return ActiveRequests.Num();
}

float UAuracronWorldPartitionStreamingManager::GetStreamingEfficiency() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.StreamingEfficiency;
}

void UAuracronWorldPartitionStreamingManager::EnableStreamingDebug(bool bEnabled)
{
    Configuration.bEnableStreamingDebug = bEnabled;

    AURACRON_WP_LOG_INFO(TEXT("Streaming debug %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionStreamingManager::IsStreamingDebugEnabled() const
{
    return Configuration.bEnableStreamingDebug;
}

void UAuracronWorldPartitionStreamingManager::DrawDebugStreamingInfo(UWorld* World) const
{
    if (!Configuration.bEnableStreamingDebug || !World)
    {
        return;
    }

    // Draw streaming sources
    TArray<FAuracronStreamingSource> ActiveSources = GetActiveStreamingSources();
    for (const FAuracronStreamingSource& Source : ActiveSources)
    {
        // Draw source location
        DrawDebugSphere(World, Source.Location, 100.0f, 12, FColor::Green, false, -1.0f, 0, 5.0f);

        // Draw streaming radius
        DrawDebugSphere(World, Source.Location, Source.StreamingRadius, 32, FColor::Yellow, false, -1.0f, 0, 2.0f);

        // Draw predicted location if enabled
        if (Source.bEnablePrediction)
        {
            FVector PredictedLocation = Source.GetPredictedLocation();
            DrawDebugSphere(World, PredictedLocation, 50.0f, 12, FColor::Blue, false, -1.0f, 0, 3.0f);
            DrawDebugLine(World, Source.Location, PredictedLocation, FColor::Blue, false, -1.0f, 0, 2.0f);
        }
    }
}

void UAuracronWorldPartitionStreamingManager::LogStreamingState() const
{
    FScopeLock Lock(&StreamingLock);

    int32 PendingCount = GetPendingRequestCount();
    int32 ActiveCount = GetActiveRequestCount();
    int32 SourceCount = StreamingSources.Num();

    AURACRON_WP_LOG_INFO(TEXT("Streaming State: %d pending, %d active, %d sources"), PendingCount, ActiveCount, SourceCount);

    FAuracronStreamingStatistics CurrentStats = GetStreamingStatistics();
    AURACRON_WP_LOG_INFO(TEXT("Statistics: %.1fMB memory, %.2f efficiency, %.3fs avg load time"),
                         CurrentStats.CurrentMemoryUsageMB, CurrentStats.StreamingEfficiency, CurrentStats.AverageLoadingTime);
}

void UAuracronWorldPartitionStreamingManager::UpdateStatistics()
{
    FScopeLock Lock(&StatisticsLock);

    // Count requests by state
    Statistics.PendingRequests = 0;
    Statistics.TotalStreamingRequests = StreamingRequests.Num();

    for (const auto& RequestPair : StreamingRequests)
    {
        const FAuracronStreamingRequest& Request = RequestPair.Value;

        switch (Request.State)
        {
            case EAuracronStreamingRequestState::Pending:
                Statistics.PendingRequests++;
                break;
            case EAuracronStreamingRequestState::Completed:
                Statistics.CompletedRequests++;
                break;
            case EAuracronStreamingRequestState::Failed:
                Statistics.FailedRequests++;
                break;
        }
    }

    Statistics.UpdateCalculatedFields();
}

FString UAuracronWorldPartitionStreamingManager::GenerateRequestId() const
{
    static int32 RequestCounter = 0;
    RequestCounter++;

    return FString::Printf(TEXT("StreamReq_%d_%lld"), RequestCounter, FDateTime::Now().GetTicks());
}

bool UAuracronWorldPartitionStreamingManager::ShouldProcessRequest(const FAuracronStreamingRequest& Request) const
{
    // Check if request is expired
    if (Request.IsExpired(60.0f)) // 60 second timeout
    {
        return false;
    }

    // Check memory pressure for load requests
    if ((Request.RequestType == EAuracronStreamingRequestType::Load ||
         Request.RequestType == EAuracronStreamingRequestType::Preload) &&
        IsMemoryPressureHigh())
    {
        return Request.Priority >= EAuracronStreamingPriority::High;
    }

    return true;
}

void UAuracronWorldPartitionStreamingManager::OnRequestStarted(const FString& RequestId)
{
    FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (Request)
    {
        OnCellStreamingStarted.Broadcast(Request->CellId, Request->RequestType);

        AURACRON_WP_LOG_VERBOSE(TEXT("Streaming started: %s for cell %s"), *RequestId, *Request->CellId);
    }
}

void UAuracronWorldPartitionStreamingManager::OnRequestCompleted(const FString& RequestId, bool bSuccess, const FString& ErrorMessage)
{
    FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (!Request)
    {
        return;
    }

    Request->CompletionTime = FDateTime::Now();
    Request->State = bSuccess ? EAuracronStreamingRequestState::Completed : EAuracronStreamingRequestState::Failed;
    Request->ErrorMessage = ErrorMessage;

    ActiveRequests.Remove(RequestId);

    // Update statistics
    {
        FScopeLock Lock(&StatisticsLock);

        if (Request->RequestType == EAuracronStreamingRequestType::Load ||
            Request->RequestType == EAuracronStreamingRequestType::ForceLoad)
        {
            if (bSuccess)
            {
                Statistics.CellsLoaded++;
                Statistics.AverageLoadingTime = (Statistics.AverageLoadingTime + Request->ProcessingTime) / 2.0f;
            }
        }
        else if (Request->RequestType == EAuracronStreamingRequestType::Unload ||
                 Request->RequestType == EAuracronStreamingRequestType::ForceUnload)
        {
            if (bSuccess)
            {
                Statistics.CellsUnloaded++;
                Statistics.AverageUnloadingTime = (Statistics.AverageUnloadingTime + Request->ProcessingTime) / 2.0f;
            }
        }
    }

    // Broadcast events
    if (bSuccess)
    {
        OnCellStreamingCompleted.Broadcast(Request->CellId, Request->RequestType, Request->ProcessingTime);
    }
    else
    {
        OnCellStreamingFailed.Broadcast(Request->CellId, Request->RequestType, ErrorMessage);
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Streaming %s: %s for cell %s (%.3fs)"),
                           bSuccess ? TEXT("completed") : TEXT("failed"),
                           *RequestId, *Request->CellId, Request->ProcessingTime);
}

void UAuracronWorldPartitionStreamingManager::ValidateConfiguration()
{
    // Validate distances
    Configuration.StreamingDistance = FMath::Max(0.0f, Configuration.StreamingDistance);
    Configuration.UnloadingDistance = FMath::Max(Configuration.StreamingDistance, Configuration.UnloadingDistance);
    Configuration.PreloadDistance = FMath::Max(Configuration.UnloadingDistance, Configuration.PreloadDistance);

    // Validate concurrent requests
    Configuration.MaxConcurrentStreamingRequests = FMath::Max(1, Configuration.MaxConcurrentStreamingRequests);
    Configuration.MaxCellsPerFrame = FMath::Max(1, Configuration.MaxCellsPerFrame);

    // Validate memory settings
    Configuration.MaxMemoryUsageMB = FMath::Max(100.0f, Configuration.MaxMemoryUsageMB);
    Configuration.MemoryPressureThreshold = FMath::Clamp(Configuration.MemoryPressureThreshold, 0.1f, 1.0f);

    // Validate timing
    Configuration.StreamingTimeSliceMs = FMath::Max(1.0f, Configuration.StreamingTimeSliceMs);
    Configuration.PriorityUpdateInterval = FMath::Max(0.1f, Configuration.PriorityUpdateInterval);
}

float UAuracronWorldPartitionStreamingManager::CalculateMemoryUsage() const
{
    // Simplified memory calculation
    // In a real implementation, this would query actual memory usage

    float BaseMemory = 50.0f; // Base framework memory
    float RequestMemory = StreamingRequests.Num() * 0.1f; // ~0.1MB per request
    float SourceMemory = StreamingSources.Num() * 0.05f; // ~0.05MB per source

    return BaseMemory + RequestMemory + SourceMemory;
}

void UAuracronWorldPartitionStreamingManager::HandleMemoryPressure()
{
    AURACRON_WP_LOG_WARNING(TEXT("High memory pressure detected, initiating cleanup"));

    // Cancel low priority preload requests
    TArray<FString> RequestsToCancel;
    for (const auto& RequestPair : StreamingRequests)
    {
        const FAuracronStreamingRequest& Request = RequestPair.Value;
        if (Request.RequestType == EAuracronStreamingRequestType::Preload &&
            Request.Priority <= EAuracronStreamingPriority::Low &&
            Request.State == EAuracronStreamingRequestState::Pending)
        {
            RequestsToCancel.Add(Request.RequestId);
        }
    }

    for (const FString& RequestId : RequestsToCancel)
    {
        CancelStreamingRequest(RequestId);
    }

    // Force unload distant cells
    TArray<FString> CellsToUnload = GetCellsToUnloadForMemory();
    for (const FString& CellId : CellsToUnload)
    {
        RequestCellUnloading(CellId, EAuracronStreamingPriority::High);
    }
}

TArray<FString> UAuracronWorldPartitionStreamingManager::GetCellsToUnloadForMemory() const
{
    TArray<FString> CellsToUnload;

    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager || !GridManager->IsInitialized())
    {
        return CellsToUnload;
    }

    TArray<FAuracronStreamingSource> ActiveSources = GetActiveStreamingSources();
    TArray<FAuracronGridCell> AllCells = GridManager->GetAllCells();

    // Find cells that are far from all streaming sources
    for (const FAuracronGridCell& Cell : AllCells)
    {
        bool bIsFarFromAllSources = true;

        for (const FAuracronStreamingSource& Source : ActiveSources)
        {
            float Distance = FVector::Dist(Source.GetPredictedLocation(), Cell.GetCenter());
            if (Distance <= Configuration.UnloadingDistance)
            {
                bIsFarFromAllSources = false;
                break;
            }
        }

        if (bIsFarFromAllSources)
        {
            CellsToUnload.Add(Cell.CellId);
        }
    }

    return CellsToUnload;
}
