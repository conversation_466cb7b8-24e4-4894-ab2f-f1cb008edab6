// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - PCG Manager Implementation
// Bridge 2.1: PCG Framework - Core Infrastructure

#include "AuracronPCGManager.h"
#include "AuracronPCGBridge.h"

// Engine includes
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Stats/StatsHierarchical.h"

// PCG includes
#include "PCGSubsystem.h"
#include "PCGWorldActor.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGNode.h"
#include "PCGSettings.h"
#include "PCGData.h"
#include "PCGPointData.h"

// Initialize static member
int32 UAuracronPCGManager::GenerationIdCounter = 0;

UAuracronPCGManager::UAuracronPCGManager()
{
    // Initialize default configuration
    Configuration = FAuracronPCGConfiguration();
    
    // Initialize performance metrics
    PerformanceMetrics = FAuracronPCGPerformanceMetrics();
    
    // Clear collections
    ActiveGenerations.Empty();
    CompletedGenerations.Empty();
    GenerationProgress.Empty();
    RegisteredElements.Empty();
    ErrorHistory.Empty();
    
    // Initialize state
    bIsInitialized = false;
    PCGSubsystem = nullptr;
}

void UAuracronPCGManager::Initialize()
{
    if (bIsInitialized)
    {
        AURACRON_PCG_LOG_CORE(Warning, TEXT("PCG Manager already initialized"));
        return;
    }

    AURACRON_PCG_LOG_CORE(Log, TEXT("Initializing PCG Manager"));

    // Initialize PCG subsystem
    InitializePCGSubsystem();

    // Initialize default elements
    InitializeDefaultElements();

    // Initialize performance monitoring
    InitializePerformanceMonitoring();

    // Start timers
    if (UWorld* World = GEngine->GetCurrentPlayWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        
        // Performance update timer (every 1 second)
        TimerManager.SetTimer(PerformanceUpdateTimer, this, &UAuracronPCGManager::UpdatePerformanceMetrics, 1.0f, true);
        
        // Generation queue timer (every 0.1 seconds)
        TimerManager.SetTimer(GenerationQueueTimer, this, &UAuracronPCGManager::ProcessGenerationQueue, 0.1f, true);
    }

    bIsInitialized = true;
    AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Manager initialized successfully"));
}

void UAuracronPCGManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    AURACRON_PCG_LOG_CORE(Log, TEXT("Shutting down PCG Manager"));

    // Stop all active generations
    TArray<FString> ActiveGenerationIds;
    ActiveGenerations.GetKeys(ActiveGenerationIds);
    
    for (const FString& GenerationId : ActiveGenerationIds)
    {
        StopGeneration(GenerationId);
    }

    // Clear timers
    if (UWorld* World = GEngine->GetCurrentPlayWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.ClearTimer(PerformanceUpdateTimer);
        TimerManager.ClearTimer(GenerationQueueTimer);
    }

    // Clear collections
    ActiveGenerations.Empty();
    CompletedGenerations.Empty();
    GenerationProgress.Empty();
    RegisteredElements.Empty();
    ErrorHistory.Empty();

    // Reset subsystem reference
    PCGSubsystem = nullptr;

    bIsInitialized = false;
    AURACRON_PCG_LOG_CORE(Log, TEXT("PCG Manager shut down successfully"));
}

void UAuracronPCGManager::ApplyConfiguration(const FAuracronPCGConfiguration& NewConfiguration)
{
    Configuration = NewConfiguration;
    
    AURACRON_PCG_LOG_CORE(Log, TEXT("Applied new PCG configuration:"));
    AURACRON_PCG_LOG_CORE(Log, TEXT("  - Execution Mode: %d"), (int32)Configuration.ExecutionMode);
    AURACRON_PCG_LOG_CORE(Log, TEXT("  - Quality Level: %d"), (int32)Configuration.QualityLevel);
    AURACRON_PCG_LOG_CORE(Log, TEXT("  - Thread Count: %d"), Configuration.ThreadCount);
    AURACRON_PCG_LOG_CORE(Log, TEXT("  - GPU Acceleration: %s"), Configuration.bEnableGPUAcceleration ? TEXT("Enabled") : TEXT("Disabled"));
}

FString UAuracronPCGManager::StartGeneration(const FAuracronPCGGenerationRequest& Request)
{
    if (!bIsInitialized)
    {
        AURACRON_PCG_LOG(Error, TEXT("Cannot start generation - PCG Manager not initialized"));
        return FString();
    }

    // Validate request
    TArray<FString> ValidationErrors;
    if (!ValidateGenerationRequest(Request, ValidationErrors))
    {
        AURACRON_PCG_LOG(Error, TEXT("Generation request validation failed:"));
        for (const FString& Error : ValidationErrors)
        {
            AURACRON_PCG_LOG(Error, TEXT("  - %s"), *Error);
        }
        return FString();
    }

    // Generate unique ID
    FString GenerationId = FString::Printf(TEXT("PCG_Gen_%d_%s"), 
                                          ++GenerationIdCounter, 
                                          *FDateTime::Now().ToString(TEXT("%Y%m%d_%H%M%S")));

    // Add to active generations
    {
        FScopeLock Lock(&GenerationLock);
        ActiveGenerations.Add(GenerationId, Request);
        GenerationProgress.Add(GenerationId, 0.0f);
    }

    AURACRON_PCG_LOG(Log, TEXT("Started PCG generation: %s"), *GenerationId);

    // Execute generation (async or sync based on request)
    if (Request.bAsynchronous)
    {
        // Execute asynchronously
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, GenerationId, Request]()
        {
            ExecuteGenerationRequest(GenerationId, Request);
        });
    }
    else
    {
        // Execute synchronously
        ExecuteGenerationRequest(GenerationId, Request);
    }

    return GenerationId;
}

bool UAuracronPCGManager::StopGeneration(const FString& GenerationId)
{
    FScopeLock Lock(&GenerationLock);
    
    if (!ActiveGenerations.Contains(GenerationId))
    {
        AURACRON_PCG_LOG(Warning, TEXT("Cannot stop generation - ID not found: %s"), *GenerationId);
        return false;
    }

    // Remove from active generations
    ActiveGenerations.Remove(GenerationId);
    GenerationProgress.Remove(GenerationId);

    AURACRON_PCG_LOG(Log, TEXT("Stopped PCG generation: %s"), *GenerationId);
    return true;
}

FAuracronPCGGenerationResult UAuracronPCGManager::GetGenerationResult(const FString& GenerationId) const
{
    FScopeLock Lock(&GenerationLock);
    
    if (const FAuracronPCGGenerationResult* Result = CompletedGenerations.Find(GenerationId))
    {
        return *Result;
    }

    // Return empty result if not found
    return FAuracronPCGGenerationResult();
}

UPCGGraph* UAuracronPCGManager::CreatePCGGraph(const FString& GraphName)
{
    if (!bIsInitialized || !PCGSubsystem)
    {
        AURACRON_PCG_LOG(Error, TEXT("Cannot create PCG graph - system not initialized"));
        return nullptr;
    }

    // Create new PCG graph
    UPCGGraph* NewGraph = NewObject<UPCGGraph>(this, *GraphName);
    if (NewGraph)
    {
        AURACRON_PCG_LOG(Log, TEXT("Created PCG graph: %s"), *GraphName);
    }
    else
    {
        AURACRON_PCG_LOG(Error, TEXT("Failed to create PCG graph: %s"), *GraphName);
    }

    return NewGraph;
}

bool UAuracronPCGManager::ValidatePCGGraph(UPCGGraph* Graph, TArray<FString>& ValidationErrors)
{
    ValidationErrors.Empty();

    if (!Graph)
    {
        ValidationErrors.Add(TEXT("Graph is null"));
        return false;
    }

    // Basic graph validation
    if (!Graph->GetInputNode() || !Graph->GetOutputNode())
    {
        ValidationErrors.Add(TEXT("Graph missing input or output node"));
    }

    // Check for disconnected nodes
    TArray<UPCGNode*> AllNodes = Graph->GetNodes();
    for (UPCGNode* Node : AllNodes)
    {
        if (!Node)
        {
            ValidationErrors.Add(TEXT("Graph contains null node"));
            continue;
        }

        // Check if node has valid settings
        if (!Node->GetSettings())
        {
            ValidationErrors.Add(FString::Printf(TEXT("Node '%s' has no settings"), *Node->GetNodeTitle().ToString()));
        }
    }

    bool bIsValid = ValidationErrors.Num() == 0;
    AURACRON_PCG_LOG(Log, TEXT("PCG graph validation %s (%d errors)"), 
                     bIsValid ? TEXT("passed") : TEXT("failed"), ValidationErrors.Num());

    return bIsValid;
}

bool UAuracronPCGManager::ExecutePCGGraph(UPCGGraph* Graph, AActor* TargetActor, const FAuracronPCGConfiguration& Config)
{
    if (!bIsInitialized || !PCGSubsystem)
    {
        AURACRON_PCG_LOG(Error, TEXT("Cannot execute PCG graph - system not initialized"));
        return false;
    }

    if (!Graph)
    {
        AURACRON_PCG_LOG(Error, TEXT("Cannot execute null PCG graph"));
        return false;
    }

    if (!TargetActor)
    {
        AURACRON_PCG_LOG(Error, TEXT("Cannot execute PCG graph - no target actor"));
        return false;
    }

    // Get or create PCG component on target actor
    UPCGComponent* PCGComponent = TargetActor->FindComponentByClass<UPCGComponent>();
    if (!PCGComponent)
    {
        PCGComponent = NewObject<UPCGComponent>(TargetActor);
        TargetActor->AddInstanceComponent(PCGComponent);
        PCGComponent->RegisterComponent();
    }

    // Set the graph
    PCGComponent->SetGraph(Graph);

    // Execute the graph
    PCGComponent->GenerateLocal(true);

    AURACRON_PCG_LOG(Log, TEXT("Executed PCG graph on actor: %s"), *TargetActor->GetName());
    return true;
}

void UAuracronPCGManager::RegisterCustomElement(TSubclassOf<UAuracronPCGElementBase> ElementClass)
{
    if (!ElementClass)
    {
        AURACRON_PCG_LOG(Warning, TEXT("Cannot register null element class"));
        return;
    }

    if (!RegisteredElements.Contains(ElementClass))
    {
        RegisteredElements.Add(ElementClass);
        AURACRON_PCG_LOG(Log, TEXT("Registered custom PCG element: %s"), *ElementClass->GetName());
    }
    else
    {
        AURACRON_PCG_LOG(Warning, TEXT("Element class already registered: %s"), *ElementClass->GetName());
    }
}

void UAuracronPCGManager::UnregisterCustomElement(TSubclassOf<UAuracronPCGElementBase> ElementClass)
{
    if (RegisteredElements.Remove(ElementClass) > 0)
    {
        AURACRON_PCG_LOG(Log, TEXT("Unregistered custom PCG element: %s"), *ElementClass->GetName());
    }
    else
    {
        AURACRON_PCG_LOG(Warning, TEXT("Element class not found for unregistration: %s"), 
                         ElementClass ? *ElementClass->GetName() : TEXT("null"));
    }
}

TArray<TSubclassOf<UAuracronPCGElementBase>> UAuracronPCGManager::GetRegisteredElements() const
{
    return RegisteredElements;
}

FAuracronPCGPerformanceMetrics UAuracronPCGManager::GetCurrentPerformanceMetrics() const
{
    FScopeLock Lock(&PerformanceLock);
    return PerformanceMetrics;
}

void UAuracronPCGManager::ResetPerformanceMetrics()
{
    FScopeLock Lock(&PerformanceLock);
    PerformanceMetrics = FAuracronPCGPerformanceMetrics();
    AURACRON_PCG_LOG_PERFORMANCE(Log, TEXT("Performance metrics reset"));
}

TArray<FString> UAuracronPCGManager::GetPerformanceRecommendations() const
{
    TArray<FString> Recommendations;

    FScopeLock Lock(&PerformanceLock);

    // Check execution time
    if (PerformanceMetrics.ExecutionTimeSeconds > 10.0f)
    {
        Recommendations.Add(TEXT("Consider enabling GPU acceleration for better performance"));
        Recommendations.Add(TEXT("Increase thread count if CPU cores are available"));
    }

    // Check memory usage
    if (PerformanceMetrics.MemoryUsageMB > 1024.0f)
    {
        Recommendations.Add(TEXT("High memory usage detected - consider reducing batch size"));
        Recommendations.Add(TEXT("Enable memory optimization in configuration"));
    }

    // Check point generation efficiency
    if (PerformanceMetrics.PointsGenerated > 0 && PerformanceMetrics.ExecutionTimeSeconds > 0.0f)
    {
        float PointsPerSecond = PerformanceMetrics.PointsGenerated / PerformanceMetrics.ExecutionTimeSeconds;
        if (PointsPerSecond < 1000.0f)
        {
            Recommendations.Add(TEXT("Low point generation rate - optimize PCG graph complexity"));
        }
    }

    return Recommendations;
}

TArray<FAuracronPCGErrorInfo> UAuracronPCGManager::GetErrorHistory(int32 MaxEntries) const
{
    FScopeLock Lock(&ErrorLock);

    TArray<FAuracronPCGErrorInfo> Result;
    int32 StartIndex = FMath::Max(0, ErrorHistory.Num() - MaxEntries);

    for (int32 i = StartIndex; i < ErrorHistory.Num(); ++i)
    {
        Result.Add(ErrorHistory[i]);
    }

    return Result;
}

void UAuracronPCGManager::ClearErrorHistory()
{
    FScopeLock Lock(&ErrorLock);
    ErrorHistory.Empty();
    AURACRON_PCG_LOG(Log, TEXT("Error history cleared"));
}

void UAuracronPCGManager::ReportError(const FAuracronPCGErrorInfo& ErrorInfo)
{
    FScopeLock Lock(&ErrorLock);

    // Add to error history
    ErrorHistory.Add(ErrorInfo);

    // Keep only the last 100 errors
    if (ErrorHistory.Num() > 100)
    {
        ErrorHistory.RemoveAt(0, ErrorHistory.Num() - 100);
    }

    // Log the error
    FString ErrorTypeString = UEnum::GetValueAsString(ErrorInfo.ErrorCode);
    AURACRON_PCG_LOG(Error, TEXT("PCG Error [%s]: %s (Context: %s)"),
                     *ErrorTypeString, *ErrorInfo.ErrorMessage, *ErrorInfo.ErrorContext);

    // Broadcast error event
    OnError.Broadcast(ErrorInfo);
}

FString UAuracronPCGManager::GetVersionString() const
{
    return AURACRON_PCG_VERSION_STRING;
}

bool UAuracronPCGManager::IsGPUAccelerationAvailable() const
{
    // Check if GPU acceleration is supported
    return GRHISupportsComputeShaders && Configuration.bEnableGPUAcceleration;
}

int32 UAuracronPCGManager::GetAvailableThreadCount() const
{
    return FPlatformMisc::NumberOfCoresIncludingHyperthreads();
}

float UAuracronPCGManager::GetSystemHealthScore() const
{
    float HealthScore = 1.0f;

    FScopeLock Lock(&PerformanceLock);

    // Factor in execution time (lower is better)
    if (PerformanceMetrics.ExecutionTimeSeconds > 30.0f)
    {
        HealthScore *= 0.7f;
    }
    else if (PerformanceMetrics.ExecutionTimeSeconds > 10.0f)
    {
        HealthScore *= 0.9f;
    }

    // Factor in memory usage (lower is better)
    if (PerformanceMetrics.MemoryUsageMB > 2048.0f)
    {
        HealthScore *= 0.6f;
    }
    else if (PerformanceMetrics.MemoryUsageMB > 1024.0f)
    {
        HealthScore *= 0.8f;
    }

    // Factor in error count
    {
        FScopeLock ErrorLockLocal(&ErrorLock);
        if (ErrorHistory.Num() > 10)
        {
            HealthScore *= 0.5f;
        }
        else if (ErrorHistory.Num() > 5)
        {
            HealthScore *= 0.8f;
        }
    }

    return FMath::Clamp(HealthScore, 0.0f, 1.0f);
}

void UAuracronPCGManager::CleanupGeneratedContent()
{
    AURACRON_PCG_LOG(Log, TEXT("Cleaning up generated PCG content"));

    // Clean up completed generations older than 1 hour
    TArray<FString> GenerationsToRemove;
    FDateTime CurrentTime = FDateTime::Now();

    for (const auto& Pair : CompletedGenerations)
    {
        const FAuracronPCGGenerationResult& Result = Pair.Value;
        FTimespan TimeSinceCompletion = CurrentTime - Result.CompletionTime;

        if (TimeSinceCompletion.GetTotalHours() > 1.0)
        {
            GenerationsToRemove.Add(Pair.Key);
        }
    }

    for (const FString& GenerationId : GenerationsToRemove)
    {
        CompletedGenerations.Remove(GenerationId);
    }

    AURACRON_PCG_LOG(Log, TEXT("Cleaned up %d old generation results"), GenerationsToRemove.Num());
}

void UAuracronPCGManager::OptimizeMemoryUsage()
{
    AURACRON_PCG_LOG_PERFORMANCE(Log, TEXT("Optimizing PCG memory usage"));

    // Clean up old generations
    CleanupGeneratedContent();

    // Force garbage collection
    GEngine->ForceGarbageCollection(true);

    // Update performance metrics
    UpdatePerformanceMetrics();

    AURACRON_PCG_LOG_PERFORMANCE(Log, TEXT("Memory optimization completed"));
}

bool UAuracronPCGManager::ValidateSystemRequirements(TArray<FString>& ValidationMessages)
{
    ValidationMessages.Empty();
    bool bAllRequirementsMet = true;

    // Check PCG subsystem availability
    if (!PCGSubsystem)
    {
        ValidationMessages.Add(TEXT("PCG Subsystem not available"));
        bAllRequirementsMet = false;
    }

    // Check minimum thread count
    int32 AvailableThreads = GetAvailableThreadCount();
    if (AvailableThreads < 2)
    {
        ValidationMessages.Add(FString::Printf(TEXT("Insufficient CPU cores: %d (minimum 2 required)"), AvailableThreads));
        bAllRequirementsMet = false;
    }

    // Check GPU acceleration if enabled
    if (Configuration.bEnableGPUAcceleration && !IsGPUAccelerationAvailable())
    {
        ValidationMessages.Add(TEXT("GPU acceleration requested but not available"));
        // This is a warning, not a failure
    }

    // Check memory availability
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    uint64 AvailableMemoryMB = MemStats.AvailablePhysical / 1024 / 1024;

    if (AvailableMemoryMB < 1024) // Less than 1GB
    {
        ValidationMessages.Add(FString::Printf(TEXT("Low available memory: %llu MB (minimum 1024 MB recommended)"), AvailableMemoryMB));
        // This is a warning, not a failure
    }

    AURACRON_PCG_LOG(Log, TEXT("System requirements validation %s (%d messages)"),
                     bAllRequirementsMet ? TEXT("passed") : TEXT("failed"), ValidationMessages.Num());

    return bAllRequirementsMet;
}

// Internal methods implementation

void UAuracronPCGManager::InitializePCGSubsystem()
{
    AURACRON_PCG_LOG_CORE(Log, TEXT("Initializing PCG subsystem"));

    if (UWorld* World = GEngine->GetCurrentPlayWorld())
    {
        PCGSubsystem = World->GetSubsystem<UPCGSubsystem>();
        if (PCGSubsystem)
        {
            AURACRON_PCG_LOG_CORE(Log, TEXT("PCG subsystem initialized successfully"));
        }
        else
        {
            AURACRON_PCG_LOG_CORE(Error, TEXT("Failed to get PCG subsystem"));
        }
    }
    else
    {
        AURACRON_PCG_LOG_CORE(Warning, TEXT("No current play world - PCG subsystem will be initialized later"));
    }
}

void UAuracronPCGManager::InitializeDefaultElements()
{
    AURACRON_PCG_LOG_CORE(Log, TEXT("Initializing default PCG elements"));

    // Register default element types here
    // This would be expanded with actual custom element classes

    AURACRON_PCG_LOG_CORE(Log, TEXT("Default PCG elements initialized"));
}

void UAuracronPCGManager::InitializePerformanceMonitoring()
{
    AURACRON_PCG_LOG_CORE(Log, TEXT("Initializing performance monitoring"));

    // Reset performance metrics
    PerformanceMetrics = FAuracronPCGPerformanceMetrics();
    PerformanceMetrics.StartTime = FDateTime::Now();

    AURACRON_PCG_LOG_CORE(Log, TEXT("Performance monitoring initialized"));
}

void UAuracronPCGManager::UpdatePerformanceMetrics()
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FScopeLock Lock(&PerformanceLock);

    // Update timing
    PerformanceMetrics.EndTime = FDateTime::Now();
    FTimespan ExecutionTime = PerformanceMetrics.EndTime - PerformanceMetrics.StartTime;
    PerformanceMetrics.ExecutionTimeSeconds = ExecutionTime.GetTotalSeconds();

    // Update memory usage
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    PerformanceMetrics.MemoryUsageMB = (MemStats.UsedPhysical - MemStats.AvailablePhysical) / 1024.0f / 1024.0f;

    // Update thread usage
    PerformanceMetrics.ThreadsUsed = Configuration.ThreadCount;

    // Update stats
    AURACRON_PCG_SET_MEMORY_STAT(STAT_AuracronPCG_MemoryUsage, PerformanceMetrics.MemoryUsageMB * 1024 * 1024);
    AURACRON_PCG_SET_DWORD_STAT(STAT_AuracronPCG_PointsGenerated, PerformanceMetrics.PointsGenerated);
}

void UAuracronPCGManager::ProcessGenerationQueue()
{
    // This method would process queued generation requests
    // For now, it's a placeholder for future async processing

    FScopeLock Lock(&GenerationLock);

    // Update progress for active generations
    for (auto& Pair : GenerationProgress)
    {
        const FString& GenerationId = Pair.Key;
        float& Progress = Pair.Value;

        // Simulate progress update (this would be replaced with actual progress tracking)
        if (Progress < 1.0f)
        {
            Progress = FMath::Min(Progress + 0.1f, 1.0f);

            // Broadcast progress update
            OnGenerationProgress.Broadcast(Progress, FString::Printf(TEXT("Processing generation %s"), *GenerationId));

            // Check if generation is complete
            if (Progress >= 1.0f)
            {
                HandleGenerationComplete(GenerationId, true);
            }
        }
    }
}

void UAuracronPCGManager::HandleGenerationComplete(const FString& GenerationId, bool bSuccess)
{
    AURACRON_PCG_LOG(Log, TEXT("Generation completed: %s (Success: %s)"), *GenerationId, bSuccess ? TEXT("Yes") : TEXT("No"));

    FScopeLock Lock(&GenerationLock);

    // Move from active to completed
    if (FAuracronPCGGenerationRequest* Request = ActiveGenerations.Find(GenerationId))
    {
        FAuracronPCGGenerationResult Result;
        Result.bSuccess = bSuccess;
        Result.GenerationId = GenerationId;
        Result.CompletionTime = FDateTime::Now();
        Result.PerformanceMetrics = PerformanceMetrics;

        CompletedGenerations.Add(GenerationId, Result);
        ActiveGenerations.Remove(GenerationId);
        GenerationProgress.Remove(GenerationId);

        // Broadcast completion event
        OnGenerationComplete.Broadcast(bSuccess, Result.PerformanceMetrics);
    }
}

bool UAuracronPCGManager::ValidateGenerationRequest(const FAuracronPCGGenerationRequest& Request, TArray<FString>& ValidationErrors)
{
    ValidationErrors.Empty();

    // Validate PCG graph
    if (!Request.PCGGraph)
    {
        ValidationErrors.Add(TEXT("PCG Graph is required"));
    }
    else
    {
        TArray<FString> GraphErrors;
        if (!ValidatePCGGraph(Request.PCGGraph, GraphErrors))
        {
            ValidationErrors.Append(GraphErrors);
        }
    }

    // Validate target actor
    if (!Request.TargetActor)
    {
        ValidationErrors.Add(TEXT("Target Actor is required"));
    }
    else if (!IsValid(Request.TargetActor))
    {
        ValidationErrors.Add(TEXT("Target Actor is not valid"));
    }

    // Validate generation bounds
    if (Request.GenerationBounds.IsZero())
    {
        ValidationErrors.Add(TEXT("Generation bounds cannot be zero"));
    }

    // Validate configuration
    if (Request.Configuration.ThreadCount <= 0)
    {
        ValidationErrors.Add(TEXT("Thread count must be greater than zero"));
    }

    if (Request.Configuration.MaxPointsPerBatch <= 0)
    {
        ValidationErrors.Add(TEXT("Max points per batch must be greater than zero"));
    }

    return ValidationErrors.Num() == 0;
}

void UAuracronPCGManager::ExecuteGenerationRequest(const FString& GenerationId, const FAuracronPCGGenerationRequest& Request)
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_GraphGeneration);

    AURACRON_PCG_LOG(Log, TEXT("Executing generation request: %s"), *GenerationId);

    bool bSuccess = false;

    try
    {
        // Apply configuration for this generation
        FAuracronPCGConfiguration PreviousConfig = Configuration;
        ApplyConfiguration(Request.Configuration);

        // Execute the PCG graph
        bSuccess = ExecutePCGGraph(Request.PCGGraph, Request.TargetActor, Request.Configuration);

        // Restore previous configuration
        ApplyConfiguration(PreviousConfig);

        AURACRON_PCG_LOG(Log, TEXT("Generation execution completed: %s (Success: %s)"),
                         *GenerationId, bSuccess ? TEXT("Yes") : TEXT("No"));
    }
    catch (const std::exception& e)
    {
        AURACRON_PCG_LOG(Error, TEXT("Exception during generation execution: %s"), UTF8_TO_TCHAR(e.what()));

        FAuracronPCGErrorInfo ErrorInfo(EAuracronPCGErrorCode::GenerationFailed,
                                       FString(UTF8_TO_TCHAR(e.what())),
                                       FString::Printf(TEXT("Generation ID: %s"), *GenerationId));
        ReportError(ErrorInfo);

        bSuccess = false;
    }

    // Handle completion
    HandleGenerationComplete(GenerationId, bSuccess);
}
