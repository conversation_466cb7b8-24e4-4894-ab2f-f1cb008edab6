// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Python Integration Utilities Implementation
// Bridge 2.15: PCG Framework - Python Integration

#include "AuracronPCGPythonBindings.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// =============================================================================
// PYTHON INTEGRATION UTILITIES IMPLEMENTATION
// =============================================================================

FString UAuracronPCGPythonIntegrationUtils::ConvertUObjectToPython(UObject* Object)
{
    if (!Object)
    {
        return TEXT("None");
    }

    // Create a Python dictionary representation of the UObject
    FString PythonObject = TEXT("{");
    PythonObject += FString::Printf(TEXT("'class_name': '%s', "), *Object->GetClass()->GetName());
    PythonObject += FString::Printf(TEXT("'object_name': '%s', "), *Object->GetName());
    PythonObject += FString::Printf(TEXT("'is_valid': %s"), Object->IsValidLowLevel() ? TEXT("True") : TEXT("False"));
    PythonObject += TEXT("}");

    return PythonObject;
}

UObject* UAuracronPCGPythonIntegrationUtils::ConvertPythonToUObject(const FString& PythonObject, UClass* TargetClass)
{
    if (!TargetClass)
    {
        return nullptr;
    }

    // Simplified conversion - in production you'd parse the Python object and create/find the UObject
    // This is a placeholder implementation
    return NewObject<UObject>(GetTransientPackage(), TargetClass);
}

FString UAuracronPCGPythonIntegrationUtils::ConvertStructToPython(const UScriptStruct* Struct, const void* StructPtr)
{
    if (!Struct || !StructPtr)
    {
        return TEXT("None");
    }

    FString PythonStruct = TEXT("{");
    PythonStruct += FString::Printf(TEXT("'struct_name': '%s', "), *Struct->GetName());
    
    // Iterate through struct properties
    for (TFieldIterator<FProperty> PropIt(Struct); PropIt; ++PropIt)
    {
        FProperty* Property = *PropIt;
        FString PropertyName = Property->GetName();
        
        // Get property value as string (simplified)
        FString PropertyValue = TEXT("'unknown'");
        
        if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
        {
            float Value = FloatProp->GetPropertyValue_InContainer(StructPtr);
            PropertyValue = FString::SanitizeFloat(Value);
        }
        else if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
        {
            int32 Value = IntProp->GetPropertyValue_InContainer(StructPtr);
            PropertyValue = FString::FromInt(Value);
        }
        else if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
        {
            bool Value = BoolProp->GetPropertyValue_InContainer(StructPtr);
            PropertyValue = Value ? TEXT("True") : TEXT("False");
        }
        else if (FStrProperty* StrProp = CastField<FStrProperty>(Property))
        {
            FString Value = StrProp->GetPropertyValue_InContainer(StructPtr);
            PropertyValue = FString::Printf(TEXT("'%s'"), *Value);
        }
        
        PythonStruct += FString::Printf(TEXT("'%s': %s, "), *PropertyName, *PropertyValue);
    }
    
    PythonStruct += TEXT("}");
    return PythonStruct;
}

bool UAuracronPCGPythonIntegrationUtils::ConvertPythonToStruct(const FString& PythonStruct, const UScriptStruct* Struct, void* StructPtr)
{
    if (!Struct || !StructPtr)
    {
        return false;
    }

    // Simplified conversion - in production you'd parse the Python dictionary and set struct values
    // This is a placeholder implementation
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Converting Python struct to %s"), *Struct->GetName());
    return true;
}

FString UAuracronPCGPythonIntegrationUtils::ConvertArrayToPython(const TArray<FString>& Array)
{
    FString PythonArray = TEXT("[");
    
    for (int32 i = 0; i < Array.Num(); i++)
    {
        PythonArray += FString::Printf(TEXT("'%s'"), *Array[i]);
        if (i < Array.Num() - 1)
        {
            PythonArray += TEXT(", ");
        }
    }
    
    PythonArray += TEXT("]");
    return PythonArray;
}

TArray<FString> UAuracronPCGPythonIntegrationUtils::ConvertPythonToArray(const FString& PythonArray)
{
    TArray<FString> Array;
    
    // Simple parsing - remove brackets and split by comma
    FString CleanArray = PythonArray;
    CleanArray = CleanArray.Replace(TEXT("["), TEXT(""));
    CleanArray = CleanArray.Replace(TEXT("]"), TEXT(""));
    CleanArray = CleanArray.Replace(TEXT("'"), TEXT(""));
    CleanArray = CleanArray.Replace(TEXT("\""), TEXT(""));
    
    CleanArray.ParseIntoArray(Array, TEXT(","), true);
    
    // Trim whitespace
    for (FString& Item : Array)
    {
        Item = Item.TrimStartAndEnd();
    }
    
    return Array;
}

FString UAuracronPCGPythonIntegrationUtils::ConvertMapToPython(const TMap<FString, FString>& Map)
{
    FString PythonMap = TEXT("{");
    
    int32 Index = 0;
    for (const auto& Pair : Map)
    {
        PythonMap += FString::Printf(TEXT("'%s': '%s'"), *Pair.Key, *Pair.Value);
        if (Index < Map.Num() - 1)
        {
            PythonMap += TEXT(", ");
        }
        Index++;
    }
    
    PythonMap += TEXT("}");
    return PythonMap;
}

TMap<FString, FString> UAuracronPCGPythonIntegrationUtils::ConvertPythonToMap(const FString& PythonMap)
{
    TMap<FString, FString> Map;
    
    // Simple parsing - remove braces and split by comma
    FString CleanMap = PythonMap;
    CleanMap = CleanMap.Replace(TEXT("{"), TEXT(""));
    CleanMap = CleanMap.Replace(TEXT("}"), TEXT(""));
    
    TArray<FString> Pairs;
    CleanMap.ParseIntoArray(Pairs, TEXT(","), true);
    
    for (const FString& Pair : Pairs)
    {
        TArray<FString> KeyValue;
        Pair.ParseIntoArray(KeyValue, TEXT(":"), true);
        
        if (KeyValue.Num() == 2)
        {
            FString Key = KeyValue[0].TrimStartAndEnd().Replace(TEXT("'"), TEXT("")).Replace(TEXT("\""), TEXT(""));
            FString Value = KeyValue[1].TrimStartAndEnd().Replace(TEXT("'"), TEXT("")).Replace(TEXT("\""), TEXT(""));
            Map.Add(Key, Value);
        }
    }
    
    return Map;
}

FString UAuracronPCGPythonIntegrationUtils::GenerateClassDocumentation(UClass* Class)
{
    if (!Class)
    {
        return TEXT("Invalid class");
    }

    FString Documentation = FString::Printf(TEXT("Class: %s\n"), *Class->GetName());
    Documentation += FString::Printf(TEXT("Parent: %s\n"), Class->GetSuperClass() ? *Class->GetSuperClass()->GetName() : TEXT("None"));
    Documentation += TEXT("\nProperties:\n");
    
    // Iterate through class properties
    for (TFieldIterator<FProperty> PropIt(Class); PropIt; ++PropIt)
    {
        FProperty* Property = *PropIt;
        Documentation += FString::Printf(TEXT("  %s: %s\n"), *Property->GetName(), *Property->GetClass()->GetName());
    }
    
    Documentation += TEXT("\nFunctions:\n");
    
    // Iterate through class functions
    for (TFieldIterator<UFunction> FuncIt(Class); FuncIt; ++FuncIt)
    {
        UFunction* Function = *FuncIt;
        Documentation += FString::Printf(TEXT("  %s("), *Function->GetName());
        
        // Add function parameters
        bool bFirstParam = true;
        for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
        {
            FProperty* Param = *ParamIt;
            if (!bFirstParam)
            {
                Documentation += TEXT(", ");
            }
            Documentation += FString::Printf(TEXT("%s: %s"), *Param->GetName(), *Param->GetClass()->GetName());
            bFirstParam = false;
        }
        
        Documentation += TEXT(")\n");
    }
    
    return Documentation;
}

FString UAuracronPCGPythonIntegrationUtils::GenerateFunctionDocumentation(UFunction* Function)
{
    if (!Function)
    {
        return TEXT("Invalid function");
    }

    FString Documentation = FString::Printf(TEXT("Function: %s\n"), *Function->GetName());
    Documentation += TEXT("Parameters:\n");
    
    for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
    {
        FProperty* Param = *ParamIt;
        Documentation += FString::Printf(TEXT("  %s: %s\n"), *Param->GetName(), *Param->GetClass()->GetName());
    }
    
    return Documentation;
}

FString UAuracronPCGPythonIntegrationUtils::GeneratePropertyDocumentation(FProperty* Property)
{
    if (!Property)
    {
        return TEXT("Invalid property");
    }

    FString Documentation = FString::Printf(TEXT("Property: %s\n"), *Property->GetName());
    Documentation += FString::Printf(TEXT("Type: %s\n"), *Property->GetClass()->GetName());
    
    return Documentation;
}

FString UAuracronPCGPythonIntegrationUtils::GenerateModuleDocumentation(const FString& ModuleName)
{
    FString Documentation = FString::Printf(TEXT("Module: %s\n"), *ModuleName);
    Documentation += TEXT("Auracron PCG Framework Python Bindings\n");
    Documentation += TEXT("Version: 2.15.0\n\n");
    
    Documentation += TEXT("Available Classes:\n");
    Documentation += TEXT("  - PCGSettings: Base class for PCG node settings\n");
    Documentation += TEXT("  - PCGData: Base class for PCG data\n");
    Documentation += TEXT("  - PCGPointData: Point cloud data\n");
    Documentation += TEXT("  - PCGSpatialData: Spatial data with bounds\n");
    Documentation += TEXT("  - PCGGraph: PCG graph container\n");
    Documentation += TEXT("  - PCGComponent: PCG component for actors\n\n");
    
    Documentation += TEXT("Available Enums:\n");
    Documentation += TEXT("  - PCGNodeCategory: Node categorization\n");
    Documentation += TEXT("  - PCGExecutionMode: Execution modes\n");
    Documentation += TEXT("  - PCGDebugVisualizationMode: Debug visualization modes\n\n");
    
    Documentation += TEXT("Available Functions:\n");
    Documentation += TEXT("  - log_message(message): Log a message\n");
    Documentation += TEXT("  - log_warning(message): Log a warning\n");
    Documentation += TEXT("  - log_error(message): Log an error\n");
    Documentation += TEXT("  - get_framework_version(): Get framework version\n");
    
    return Documentation;
}

FString UAuracronPCGPythonIntegrationUtils::GeneratePythonStub(UClass* Class)
{
    if (!Class)
    {
        return TEXT("");
    }

    FString Stub = FString::Printf(TEXT("class %s:\n"), *Class->GetName());
    Stub += FString::Printf(TEXT("    \"\"\"Stub for %s class\"\"\"\n"), *Class->GetName());
    
    // Add properties
    for (TFieldIterator<FProperty> PropIt(Class); PropIt; ++PropIt)
    {
        FProperty* Property = *PropIt;
        Stub += FString::Printf(TEXT("    %s = None  # %s\n"), *Property->GetName(), *Property->GetClass()->GetName());
    }
    
    // Add functions
    for (TFieldIterator<UFunction> FuncIt(Class); FuncIt; ++FuncIt)
    {
        UFunction* Function = *FuncIt;
        Stub += FString::Printf(TEXT("    def %s(self"), *Function->GetName());
        
        // Add function parameters
        for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
        {
            FProperty* Param = *ParamIt;
            Stub += FString::Printf(TEXT(", %s"), *Param->GetName());
        }
        
        Stub += TEXT("):\n");
        Stub += FString::Printf(TEXT("        \"\"\"Stub for %s function\"\"\"\n"), *Function->GetName());
        Stub += TEXT("        pass\n\n");
    }
    
    return Stub;
}

FString UAuracronPCGPythonIntegrationUtils::GeneratePythonWrapper(UClass* Class)
{
    if (!Class)
    {
        return TEXT("");
    }

    FString Wrapper = FString::Printf(TEXT("# Python wrapper for %s\n"), *Class->GetName());
    Wrapper += TEXT("import auracron_pcg\n\n");
    Wrapper += FString::Printf(TEXT("class %sWrapper:\n"), *Class->GetName());
    Wrapper += FString::Printf(TEXT("    def __init__(self, native_object):\n"));
    Wrapper += TEXT("        self._native = native_object\n\n");
    
    // Add wrapper methods
    for (TFieldIterator<UFunction> FuncIt(Class); FuncIt; ++FuncIt)
    {
        UFunction* Function = *FuncIt;
        Wrapper += FString::Printf(TEXT("    def %s(self"), *Function->GetName());
        
        // Add function parameters
        for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
        {
            FProperty* Param = *ParamIt;
            Wrapper += FString::Printf(TEXT(", %s"), *Param->GetName());
        }
        
        Wrapper += TEXT("):\n");
        Wrapper += FString::Printf(TEXT("        return self._native.%s("), *Function->GetName());
        
        // Add parameter passing
        bool bFirstParam = true;
        for (TFieldIterator<FProperty> ParamIt(Function); ParamIt; ++ParamIt)
        {
            FProperty* Param = *ParamIt;
            if (!bFirstParam)
            {
                Wrapper += TEXT(", ");
            }
            Wrapper += Param->GetName();
            bFirstParam = false;
        }
        
        Wrapper += TEXT(")\n\n");
    }
    
    return Wrapper;
}

bool UAuracronPCGPythonIntegrationUtils::SavePythonStubs(const FString& OutputDirectory)
{
    // Create output directory if it doesn't exist
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*OutputDirectory))
    {
        if (!PlatformFile.CreateDirectoryTree(*OutputDirectory))
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to create output directory: %s"), *OutputDirectory);
            return false;
        }
    }

    // Generate stubs for key classes
    TArray<UClass*> ClassesToStub = {
        UPCGSettings::StaticClass(),
        UPCGData::StaticClass(),
        UPCGPointData::StaticClass(),
        UPCGSpatialData::StaticClass(),
        UPCGGraph::StaticClass(),
        UPCGComponent::StaticClass()
    };

    for (UClass* Class : ClassesToStub)
    {
        FString StubContent = GeneratePythonStub(Class);
        FString StubFilePath = OutputDirectory / (Class->GetName() + TEXT(".py"));
        
        if (!FFileHelper::SaveStringToFile(StubContent, *StubFilePath))
        {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to save stub file: %s"), *StubFilePath);
            return false;
        }
    }

    // Generate main module stub
    FString ModuleStub = GenerateModuleDocumentation(TEXT("auracron_pcg"));
    FString ModuleStubPath = OutputDirectory / TEXT("__init__.py");
    
    if (!FFileHelper::SaveStringToFile(ModuleStub, *ModuleStubPath))
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to save module stub: %s"), *ModuleStubPath);
        return false;
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python stubs saved to: %s"), *OutputDirectory);
    return true;
}

void UAuracronPCGPythonIntegrationUtils::EnablePythonDebugging(bool bEnabled)
{
    // Set global debugging flag
    static bool bPythonDebuggingEnabled = false;
    bPythonDebuggingEnabled = bEnabled;
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python debugging %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronPCGPythonIntegrationUtils::IsPythonDebuggingEnabled()
{
    static bool bPythonDebuggingEnabled = false;
    return bPythonDebuggingEnabled;
}

TArray<FString> UAuracronPCGPythonIntegrationUtils::GetPythonCallStack()
{
    TArray<FString> CallStack;
    
#ifdef WITH_PYTHON
    try
    {
        // Get Python traceback
        pybind11::module_ traceback = pybind11::module_::import("traceback");
        pybind11::list stack = traceback.attr("format_stack")();
        
        for (const auto& frame : stack)
        {
            CallStack.Add(UTF8_TO_TCHAR(frame.cast<std::string>().c_str()));
        }
    }
    catch (const std::exception& e)
    {
        CallStack.Add(FString::Printf(TEXT("Error getting call stack: %s"), UTF8_TO_TCHAR(e.what())));
    }
#else
    CallStack.Add(TEXT("Python support not available"));
#endif

    return CallStack;
}

FString UAuracronPCGPythonIntegrationUtils::GetPythonMemoryUsage()
{
#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ psutil = pybind11::module_::import("psutil");
        pybind11::object process = psutil.attr("Process")();
        pybind11::object memory_info = process.attr("memory_info")();
        
        int64 MemoryUsage = memory_info.attr("rss").cast<int64>();
        return FString::Printf(TEXT("%.2f MB"), MemoryUsage / (1024.0 * 1024.0));
    }
    catch (const std::exception& e)
    {
        return FString::Printf(TEXT("Error getting memory usage: %s"), UTF8_TO_TCHAR(e.what()));
    }
#else
    return TEXT("Python support not available");
#endif
}
