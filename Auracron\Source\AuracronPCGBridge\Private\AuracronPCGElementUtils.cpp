// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Element Utilities Implementation
// Bridge 2.3: PCG Framework - Element Library

#include "AuracronPCGElementLibrary.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeInfo.h"
#include "LandscapeComponent.h"
#include "LandscapeDataAccess.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

// Noise generation includes
#include "Math/RandomStream.h"

namespace AuracronPCGElementUtils
{
    // =============================================================================
    // NOISE GENERATION FUNCTIONS
    // =============================================================================

    float GenerateNoise(EAuracronPCGNoiseType NoiseType, const FVector& Position, float Scale, int32 Octaves)
    {
        FVector ScaledPosition = Position * Scale;
        float NoiseValue = 0.0f;

        switch (NoiseType)
        {
            case EAuracronPCGNoiseType::Perlin:
            {
                NoiseValue = FMath::PerlinNoise3D(ScaledPosition);
                break;
            }
            case EAuracronPCGNoiseType::Simplex:
            {
                // Simplified simplex noise implementation
                NoiseValue = FMath::PerlinNoise3D(ScaledPosition * 0.866f); // Approximate simplex
                break;
            }
            case EAuracronPCGNoiseType::Worley:
            {
                // Worley noise implementation
                FVector CellPosition = FVector(FMath::Floor(ScaledPosition.X), FMath::Floor(ScaledPosition.Y), FMath::Floor(ScaledPosition.Z));
                float MinDistance = FLT_MAX;

                for (int32 X = -1; X <= 1; X++)
                {
                    for (int32 Y = -1; Y <= 1; Y++)
                    {
                        for (int32 Z = -1; Z <= 1; Z++)
                        {
                            FVector NeighborCell = CellPosition + FVector(X, Y, Z);
                            FRandomStream RandomStream(FMath::FloorToInt(NeighborCell.X * 73 + NeighborCell.Y * 311 + NeighborCell.Z * 179));
                            FVector FeaturePoint = NeighborCell + FVector(RandomStream.FRand(), RandomStream.FRand(), RandomStream.FRand());
                            float Distance = FVector::Dist(ScaledPosition, FeaturePoint);
                            MinDistance = FMath::Min(MinDistance, Distance);
                        }
                    }
                }
                NoiseValue = MinDistance;
                break;
            }
            case EAuracronPCGNoiseType::Ridge:
            {
                NoiseValue = 1.0f - FMath::Abs(FMath::PerlinNoise3D(ScaledPosition));
                break;
            }
            case EAuracronPCGNoiseType::Fractal:
            {
                float Amplitude = 1.0f;
                float Frequency = 1.0f;
                float MaxValue = 0.0f;

                for (int32 i = 0; i < Octaves; i++)
                {
                    NoiseValue += FMath::PerlinNoise3D(ScaledPosition * Frequency) * Amplitude;
                    MaxValue += Amplitude;
                    Amplitude *= 0.5f;
                    Frequency *= 2.0f;
                }
                NoiseValue /= MaxValue;
                break;
            }
            case EAuracronPCGNoiseType::Turbulence:
            {
                NoiseValue = FMath::Abs(FMath::PerlinNoise3D(ScaledPosition));
                for (int32 i = 1; i < Octaves; i++)
                {
                    float Frequency = FMath::Pow(2.0f, i);
                    float Amplitude = FMath::Pow(0.5f, i);
                    NoiseValue += FMath::Abs(FMath::PerlinNoise3D(ScaledPosition * Frequency)) * Amplitude;
                }
                break;
            }
            default:
                NoiseValue = FMath::PerlinNoise3D(ScaledPosition);
                break;
        }

        return FMath::Clamp(NoiseValue, -1.0f, 1.0f);
    }

    // =============================================================================
    // DISTRIBUTION PATTERN FUNCTIONS
    // =============================================================================

    TArray<FVector> GenerateDistributionPattern(EAuracronPCGDistributionPattern Pattern, const FBox& Bounds, float Spacing, float Jitter)
    {
        TArray<FVector> Positions;
        FRandomStream RandomStream(FMath::Rand());

        switch (Pattern)
        {
            case EAuracronPCGDistributionPattern::Random:
            {
                int32 NumPoints = FMath::FloorToInt((Bounds.GetSize().X * Bounds.GetSize().Y) / (Spacing * Spacing));
                for (int32 i = 0; i < NumPoints; i++)
                {
                    FVector Position = FVector(
                        RandomStream.FRandRange(Bounds.Min.X, Bounds.Max.X),
                        RandomStream.FRandRange(Bounds.Min.Y, Bounds.Max.Y),
                        RandomStream.FRandRange(Bounds.Min.Z, Bounds.Max.Z)
                    );
                    Positions.Add(Position);
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Grid:
            {
                for (float X = Bounds.Min.X; X <= Bounds.Max.X; X += Spacing)
                {
                    for (float Y = Bounds.Min.Y; Y <= Bounds.Max.Y; Y += Spacing)
                    {
                        FVector Position(X, Y, (Bounds.Min.Z + Bounds.Max.Z) * 0.5f);
                        
                        // Apply jitter
                        if (Jitter > 0.0f)
                        {
                            Position.X += RandomStream.FRandRange(-Spacing * Jitter * 0.5f, Spacing * Jitter * 0.5f);
                            Position.Y += RandomStream.FRandRange(-Spacing * Jitter * 0.5f, Spacing * Jitter * 0.5f);
                        }
                        
                        Positions.Add(Position);
                    }
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Hexagonal:
            {
                float HexSpacing = Spacing * 0.866f; // Hexagonal spacing factor
                bool bOffsetRow = false;
                
                for (float Y = Bounds.Min.Y; Y <= Bounds.Max.Y; Y += HexSpacing)
                {
                    float XOffset = bOffsetRow ? Spacing * 0.5f : 0.0f;
                    for (float X = Bounds.Min.X + XOffset; X <= Bounds.Max.X; X += Spacing)
                    {
                        FVector Position(X, Y, (Bounds.Min.Z + Bounds.Max.Z) * 0.5f);
                        
                        // Apply jitter
                        if (Jitter > 0.0f)
                        {
                            Position.X += RandomStream.FRandRange(-Spacing * Jitter * 0.25f, Spacing * Jitter * 0.25f);
                            Position.Y += RandomStream.FRandRange(-Spacing * Jitter * 0.25f, Spacing * Jitter * 0.25f);
                        }
                        
                        Positions.Add(Position);
                    }
                    bOffsetRow = !bOffsetRow;
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Poisson:
            {
                // Simplified Poisson disk sampling
                TArray<FVector> ActiveList;
                TArray<FVector> Grid;
                
                float CellSize = Spacing / FMath::Sqrt(2.0f);
                int32 GridWidth = FMath::CeilToInt(Bounds.GetSize().X / CellSize);
                int32 GridHeight = FMath::CeilToInt(Bounds.GetSize().Y / CellSize);
                
                Grid.SetNum(GridWidth * GridHeight);
                
                // Initial point
                FVector InitialPoint = Bounds.GetCenter();
                Positions.Add(InitialPoint);
                ActiveList.Add(InitialPoint);
                
                while (ActiveList.Num() > 0)
                {
                    int32 RandomIndex = RandomStream.RandRange(0, ActiveList.Num() - 1);
                    FVector CurrentPoint = ActiveList[RandomIndex];
                    bool bFoundValidPoint = false;
                    
                    for (int32 Attempt = 0; Attempt < 30; Attempt++)
                    {
                        float Angle = RandomStream.FRand() * 2.0f * PI;
                        float Distance = RandomStream.FRandRange(Spacing, 2.0f * Spacing);
                        FVector NewPoint = CurrentPoint + FVector(FMath::Cos(Angle) * Distance, FMath::Sin(Angle) * Distance, 0.0f);
                        
                        if (Bounds.IsInside(NewPoint))
                        {
                            bool bValidPoint = true;
                            for (const FVector& ExistingPoint : Positions)
                            {
                                if (FVector::Dist2D(NewPoint, ExistingPoint) < Spacing)
                                {
                                    bValidPoint = false;
                                    break;
                                }
                            }
                            
                            if (bValidPoint)
                            {
                                Positions.Add(NewPoint);
                                ActiveList.Add(NewPoint);
                                bFoundValidPoint = true;
                                break;
                            }
                        }
                    }
                    
                    if (!bFoundValidPoint)
                    {
                        ActiveList.RemoveAt(RandomIndex);
                    }
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Spiral:
            {
                FVector Center = Bounds.GetCenter();
                float MaxRadius = FMath::Min(Bounds.GetSize().X, Bounds.GetSize().Y) * 0.5f;
                float AngleStep = 2.0f * PI / 8.0f; // 8 points per revolution
                
                for (float Radius = Spacing; Radius <= MaxRadius; Radius += Spacing)
                {
                    float Angle = (Radius / Spacing) * AngleStep;
                    FVector Position = Center + FVector(FMath::Cos(Angle) * Radius, FMath::Sin(Angle) * Radius, 0.0f);
                    
                    if (Bounds.IsInside(Position))
                    {
                        Positions.Add(Position);
                    }
                }
                break;
            }
            case EAuracronPCGDistributionPattern::Radial:
            {
                FVector Center = Bounds.GetCenter();
                float MaxRadius = FMath::Min(Bounds.GetSize().X, Bounds.GetSize().Y) * 0.5f;
                int32 NumRings = FMath::FloorToInt(MaxRadius / Spacing);
                
                for (int32 Ring = 1; Ring <= NumRings; Ring++)
                {
                    float Radius = Ring * Spacing;
                    int32 NumPointsInRing = FMath::Max(1, FMath::FloorToInt(2.0f * PI * Radius / Spacing));
                    
                    for (int32 Point = 0; Point < NumPointsInRing; Point++)
                    {
                        float Angle = (2.0f * PI * Point) / NumPointsInRing;
                        FVector Position = Center + FVector(FMath::Cos(Angle) * Radius, FMath::Sin(Angle) * Radius, 0.0f);
                        
                        if (Bounds.IsInside(Position))
                        {
                            Positions.Add(Position);
                        }
                    }
                }
                break;
            }
            default:
                // Fallback to grid pattern
                return GenerateDistributionPattern(EAuracronPCGDistributionPattern::Grid, Bounds, Spacing, Jitter);
        }

        return Positions;
    }

    // =============================================================================
    // BIOME VALIDATION FUNCTIONS
    // =============================================================================

    bool IsPointValidForBiome(const FVector& Position, EAuracronPCGBiomeType BiomeType, float Intensity)
    {
        // Simplified biome validation - in production this would be more complex
        float NoiseValue = GenerateNoise(EAuracronPCGNoiseType::Perlin, Position, 0.001f, 3);
        float BiomeThreshold = 0.5f - (Intensity * 0.4f); // Adjust threshold based on intensity
        
        switch (BiomeType)
        {
            case EAuracronPCGBiomeType::Forest:
                return NoiseValue > BiomeThreshold && Position.Z > -100.0f;
            case EAuracronPCGBiomeType::Desert:
                return NoiseValue < -BiomeThreshold && Position.Z > 0.0f;
            case EAuracronPCGBiomeType::Mountain:
                return Position.Z > 500.0f;
            case EAuracronPCGBiomeType::Ocean:
                return Position.Z < 0.0f;
            default:
                return true;
        }
    }

    // =============================================================================
    // SURFACE ANALYSIS FUNCTIONS
    // =============================================================================

    FVector CalculateSurfaceNormal(const FVector& Position, float SampleDistance)
    {
        // Sample height at nearby points to calculate normal
        float HeightCenter, HeightX, HeightY;
        
        if (!SampleLandscapeHeight(Position, HeightCenter) ||
            !SampleLandscapeHeight(Position + FVector(SampleDistance, 0, 0), HeightX) ||
            !SampleLandscapeHeight(Position + FVector(0, SampleDistance, 0), HeightY))
        {
            return FVector::UpVector; // Default to up if sampling fails
        }
        
        FVector TangentX(SampleDistance, 0, HeightX - HeightCenter);
        FVector TangentY(0, SampleDistance, HeightY - HeightCenter);
        
        return FVector::CrossProduct(TangentY, TangentX).GetSafeNormal();
    }

    float CalculateSlope(const FVector& Normal)
    {
        return FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(Normal, FVector::UpVector)));
    }

    bool SampleLandscapeHeight(const FVector& Position, float& OutHeight)
    {
        // This is a simplified implementation
        // In production, you would use proper landscape sampling APIs
        OutHeight = 0.0f;
        return true; // Placeholder - always succeeds for now
    }

    bool SampleLandscapeMaterial(const FVector& Position, const FName& LayerName, float& OutWeight)
    {
        // This is a simplified implementation
        // In production, you would use proper landscape material sampling APIs
        OutWeight = 0.5f; // Placeholder value
        return true; // Placeholder - always succeeds for now
    }
}
