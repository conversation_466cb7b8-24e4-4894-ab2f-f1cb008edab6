using UnrealBuildTool;

public class AuracronMetaHumanFramework : ModuleRules
{
    public AuracronMetaHumanFramework(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "RenderCore",
            "RHI",
            "Renderer",
            "Slate",
            "SlateCore",
            "UMG",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "LevelEditor",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "PythonScriptPlugin",
            "MetaHumanSDK",
            "MetaHumanCore",
            "MetaHumanMeshTracker",
            "RigLogicModule",
            "DNAReader",
            "DNACommon",
            "DNAUtils",
            "RigLogicLib",
            "ControlRig",
            "ControlRigEditor",
            "AnimationCore",
            "AnimGraph",
            "AnimGraphRuntime",
            "BlueprintGraph",
            "KismetCompiler",
            "SkeletalMeshUtilitiesCommon",
            "MeshDescription",
            "StaticMeshDescription",
            "GeometryCore",
            "DynamicMesh",
            "GeometryFramework",
            "InteractiveToolsFramework",
            "EditorInteractiveToolsFramework",
            "ModelingComponents",
            "ModelingOperators",
            "MeshConversion",
            "GeometryProcessingInterfaces"
        });

        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Json",
            "JsonObjectConverter",
            "HTTP",
            "ApplicationCore",
            "InputCore",
            "DesktopPlatform",
            "HAL",
            "ImageWrapper",
            "AutomationController",
            "AutomationMessages",
            "AutomationTest",
            "AutomationWorker",
            "FunctionalTesting",
            "ScreenShotComparisonTools",
            "SessionServices",
            "TargetPlatform",
            "LauncherServices",
            "GameplayDebugger",
            "EngineSettings",
            "DeveloperSettings",
            "ClothingSystemRuntimeInterface",
            "ClothingSystemRuntimeCommon",
            "PhysicsCore",
            "Chaos",
            "ChaosCore",
            "GeometryCollectionEngine",
            "FieldSystemEngine",
            "ProceduralMeshComponent",
            "RuntimeMeshComponent",
            "Niagara",
            "NiagaraCore",
            "NiagaraShader",
            "VectorVM",
            "AudioMixer",
            "AudioMixerCore",
            "SignalProcessing",
            "SoundFieldRendering",
            "AudioExtensions",
            "MetasoundEngine",
            "MetasoundFrontend",
            "MetasoundGenerator",
            "MetasoundGraphCore",
            "MetasoundStandardNodes",
            "WaveTable",
            "Synthesis",
            "AudioSynesthesia",
            "AudioAnalyzer"
        });

        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "Persona",
                "SkeletalMeshEditor",
                "AnimationBlueprintEditor",
                "AnimationEditor",
                "SequencerCore",
                "Sequencer",
                "MovieScene",
                "MovieSceneTools",
                "MovieSceneTracks",
                "LevelSequence",
                "LevelSequenceEditor",
                "CinematicCamera",
                "TakeRecorder",
                "TakesCore",
                "LiveLinkInterface",
                "LiveLinkComponents",
                "LiveLinkEditor",
                "LiveLinkAnimationCore",
                "LiveLinkMovieScene",
                "RemoteControl",
                "RemoteControlUI",
                "RemoteControlLogic",
                "RemoteControlProtocol",
                "RemoteControlProtocolWidgets",
                "VirtualCamera",
                "VCamCore",
                "VCamInput",
                "VCamExtensions"
            });
        }

        // Enable RTTI for this module
        bUseRTTI = true;
        
        // Enable exceptions for this module
        bEnableExceptions = true;
        
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronMetaHumanFramework/Public"
        });
        
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronMetaHumanFramework/Private"
        });

        // MetaHuman DNA Calibration support
        PublicDefinitions.Add("WITH_METAHUMAN_DNA_CALIBRATION=1");
        
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("PLATFORM_WINDOWS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Mac)
        {
            PublicDefinitions.Add("PLATFORM_MAC=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Linux)
        {
            PublicDefinitions.Add("PLATFORM_LINUX=1");
        }

        // Performance optimizations
        PublicDefinitions.Add("AURACRON_METAHUMAN_OPTIMIZED=1");
        PublicDefinitions.Add("AURACRON_METAHUMAN_THREADING=1");
        PublicDefinitions.Add("AURACRON_METAHUMAN_GPU_ACCELERATION=1");
    }
}
