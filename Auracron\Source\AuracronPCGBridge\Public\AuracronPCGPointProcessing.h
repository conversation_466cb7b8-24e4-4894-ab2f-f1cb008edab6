// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Point Processing Nodes Header
// Bridge 2.4: PCG Framework - Point Processing Nodes

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"
#include "AuracronPCGElementLibrary.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"
#include "Elements/PCGPointFilter.h"
#include "Elements/PCGTransformPoints.h"
#include "Elements/PCGMergeElement.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Math/UnrealMathUtility.h"

#include "AuracronPCGPointProcessing.generated.h"

// Forward declarations
class UPCGPointData;
class UPCGMetadata;

// Processing operation types
UENUM(BlueprintType)
enum class EAuracronPCGProcessingOperation : uint8
{
    Filter          UMETA(DisplayName = "Filter"),
    Transform       UMETA(DisplayName = "Transform"),
    Merge           UMETA(DisplayName = "Merge"),
    Split           UMETA(DisplayName = "Split"),
    Sort            UMETA(DisplayName = "Sort"),
    Duplicate       UMETA(DisplayName = "Duplicate"),
    Prune           UMETA(DisplayName = "Prune"),
    Cluster         UMETA(DisplayName = "Cluster")
};

// Mathematical operations for point processing
UENUM(BlueprintType)
enum class EAuracronPCGMathOperation : uint8
{
    Add             UMETA(DisplayName = "Add"),
    Subtract        UMETA(DisplayName = "Subtract"),
    Multiply        UMETA(DisplayName = "Multiply"),
    Divide          UMETA(DisplayName = "Divide"),
    Power           UMETA(DisplayName = "Power"),
    Modulo          UMETA(DisplayName = "Modulo"),
    Min             UMETA(DisplayName = "Min"),
    Max             UMETA(DisplayName = "Max"),
    Average         UMETA(DisplayName = "Average"),
    Lerp            UMETA(DisplayName = "Lerp"),
    Clamp           UMETA(DisplayName = "Clamp"),
    Normalize       UMETA(DisplayName = "Normalize"),
    Abs             UMETA(DisplayName = "Absolute"),
    Sign            UMETA(DisplayName = "Sign"),
    Floor           UMETA(DisplayName = "Floor"),
    Ceil            UMETA(DisplayName = "Ceiling"),
    Round           UMETA(DisplayName = "Round"),
    Frac            UMETA(DisplayName = "Fractional"),
    Sqrt            UMETA(DisplayName = "Square Root"),
    Log             UMETA(DisplayName = "Logarithm"),
    Exp             UMETA(DisplayName = "Exponential"),
    Sin             UMETA(DisplayName = "Sine"),
    Cos             UMETA(DisplayName = "Cosine"),
    Tan             UMETA(DisplayName = "Tangent")
};

// Sorting criteria for point sorting
UENUM(BlueprintType)
enum class EAuracronPCGSortCriteria : uint8
{
    Position        UMETA(DisplayName = "Position"),
    Distance        UMETA(DisplayName = "Distance"),
    Density         UMETA(DisplayName = "Density"),
    Scale           UMETA(DisplayName = "Scale"),
    Rotation        UMETA(DisplayName = "Rotation"),
    Attribute       UMETA(DisplayName = "Custom Attribute"),
    Random          UMETA(DisplayName = "Random"),
    Index           UMETA(DisplayName = "Index")
};

// Merge strategies for point merging
UENUM(BlueprintType)
enum class EAuracronPCGMergeStrategy : uint8
{
    Append          UMETA(DisplayName = "Append"),
    Interleave      UMETA(DisplayName = "Interleave"),
    Weighted        UMETA(DisplayName = "Weighted"),
    Spatial         UMETA(DisplayName = "Spatial"),
    Attribute       UMETA(DisplayName = "Attribute Based"),
    Priority        UMETA(DisplayName = "Priority Based")
};

// Split criteria for point splitting
UENUM(BlueprintType)
enum class EAuracronPCGSplitCriteria : uint8
{
    Count           UMETA(DisplayName = "Count"),
    Percentage      UMETA(DisplayName = "Percentage"),
    Attribute       UMETA(DisplayName = "Attribute"),
    Spatial         UMETA(DisplayName = "Spatial"),
    Random          UMETA(DisplayName = "Random"),
    Pattern         UMETA(DisplayName = "Pattern")
};

// =============================================================================
// ADVANCED POINT FILTER
// =============================================================================

/**
 * Advanced Point Filter
 * Enhanced point filtering with mathematical operations and complex criteria
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedPointFilterSettings, FAuracronPCGAdvancedPointFilterElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedPointFilterSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedPointFilterSettings();

    // Filter criteria
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings")
    bool bFilterByPosition = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings", meta = (EditCondition = "bFilterByPosition"))
    FBox PositionBounds = FBox(FVector(-1000.0f), FVector(1000.0f));

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings")
    bool bFilterByDensity = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings", meta = (EditCondition = "bFilterByDensity"))
    FVector2D DensityRange = FVector2D(0.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings")
    bool bFilterByScale = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings", meta = (EditCondition = "bFilterByScale"))
    FVector ScaleMin = FVector(0.1f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings", meta = (EditCondition = "bFilterByScale"))
    FVector ScaleMax = FVector(10.0f);

    // Mathematical operations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Operations")
    bool bUseMathOperation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Operations", meta = (EditCondition = "bUseMathOperation"))
    EAuracronPCGMathOperation MathOperation = EAuracronPCGMathOperation::Add;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Operations", meta = (EditCondition = "bUseMathOperation"))
    FString SourceAttribute = TEXT("Density");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Operations", meta = (EditCondition = "bUseMathOperation"))
    float MathOperand = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Operations", meta = (EditCondition = "bUseMathOperation"))
    FVector2D ResultRange = FVector2D(0.0f, 1.0f);

    // Advanced filtering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Filter")
    bool bUseCustomExpression = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Filter", meta = (EditCondition = "bUseCustomExpression"))
    FString CustomExpression = TEXT("Density > 0.5");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Filter")
    bool bInvertFilter = false;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Settings")
    bool bUseMultithreading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Settings", meta = (ClampMin = "1", ClampMax = "16"))
    int32 BatchSize = 1000;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedPointFilterElement, UAuracronPCGAdvancedPointFilterSettings)

// =============================================================================
// ADVANCED POINT TRANSFORMER
// =============================================================================

/**
 * Advanced Point Transformer
 * Enhanced point transformation with mathematical operations and complex transformations
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedPointTransformerSettings, FAuracronPCGAdvancedPointTransformerElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedPointTransformerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedPointTransformerSettings();

    // Transform operations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform Settings")
    bool bTransformPosition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform Settings")
    bool bTransformRotation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform Settings")
    bool bTransformScale = true;

    // Mathematical transformations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Transform")
    bool bUseMathTransform = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Transform", meta = (EditCondition = "bUseMathTransform"))
    EAuracronPCGMathOperation PositionMathOp = EAuracronPCGMathOperation::Add;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Transform", meta = (EditCondition = "bUseMathTransform"))
    FVector PositionOperand = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Transform", meta = (EditCondition = "bUseMathTransform"))
    EAuracronPCGMathOperation ScaleMathOp = EAuracronPCGMathOperation::Multiply;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Transform", meta = (EditCondition = "bUseMathTransform"))
    FVector ScaleOperand = FVector::OneVector;

    // Matrix transformations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matrix Transform")
    bool bUseMatrixTransform = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matrix Transform", meta = (EditCondition = "bUseMatrixTransform"))
    FMatrix CustomTransformMatrix = FMatrix::Identity;

    // Attribute-driven transformations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Transform")
    bool bUseAttributeTransform = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Transform", meta = (EditCondition = "bUseAttributeTransform"))
    FString PositionAttribute = TEXT("Position");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Transform", meta = (EditCondition = "bUseAttributeTransform"))
    FString RotationAttribute = TEXT("Rotation");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Transform", meta = (EditCondition = "bUseAttributeTransform"))
    FString ScaleAttribute = TEXT("Scale");

    // Noise-based transformations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Transform")
    bool bUseNoiseTransform = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Transform", meta = (EditCondition = "bUseNoiseTransform"))
    EAuracronPCGNoiseType NoiseType = EAuracronPCGNoiseType::Perlin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Transform", meta = (EditCondition = "bUseNoiseTransform", ClampMin = "0.001", ClampMax = "1.0"))
    float NoiseScale = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Transform", meta = (EditCondition = "bUseNoiseTransform", ClampMin = "0.0", ClampMax = "1.0"))
    float NoiseIntensity = 0.5f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedPointTransformerElement, UAuracronPCGAdvancedPointTransformerSettings)

// =============================================================================
// ADVANCED POINT MERGER
// =============================================================================

/**
 * Advanced Point Merger
 * Enhanced point merging with multiple strategies and conflict resolution
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedPointMergerSettings, FAuracronPCGAdvancedPointMergerElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedPointMergerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedPointMergerSettings();

    // Merge strategy
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Merge Settings")
    EAuracronPCGMergeStrategy MergeStrategy = EAuracronPCGMergeStrategy::Append;

    // Weighted merging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weighted Merge", meta = (EditCondition = "MergeStrategy == EAuracronPCGMergeStrategy::Weighted"))
    TArray<float> InputWeights;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weighted Merge", meta = (EditCondition = "MergeStrategy == EAuracronPCGMergeStrategy::Weighted"))
    bool bNormalizeWeights = true;

    // Spatial merging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial Merge", meta = (EditCondition = "MergeStrategy == EAuracronPCGMergeStrategy::Spatial"))
    float SpatialThreshold = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial Merge", meta = (EditCondition = "MergeStrategy == EAuracronPCGMergeStrategy::Spatial"))
    bool bMergeOverlapping = true;

    // Attribute merging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Merge")
    bool bMergeAttributes = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Merge", meta = (EditCondition = "bMergeAttributes"))
    TArray<FString> AttributesToMerge;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Merge", meta = (EditCondition = "bMergeAttributes"))
    EAuracronPCGMathOperation AttributeMergeOperation = EAuracronPCGMathOperation::Average;

    // Conflict resolution
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conflict Resolution")
    bool bResolveConflicts = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conflict Resolution", meta = (EditCondition = "bResolveConflicts"))
    bool bPreferHigherDensity = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conflict Resolution", meta = (EditCondition = "bResolveConflicts"))
    bool bPreferLargerScale = false;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedPointMergerElement, UAuracronPCGAdvancedPointMergerSettings)

// =============================================================================
// ADVANCED POINT SPLITTER
// =============================================================================

/**
 * Advanced Point Splitter
 * Enhanced point splitting with multiple criteria and distribution strategies
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedPointSplitterSettings, FAuracronPCGAdvancedPointSplitterElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedPointSplitterSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedPointSplitterSettings();

    // Split criteria
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Split Settings")
    EAuracronPCGSplitCriteria SplitCriteria = EAuracronPCGSplitCriteria::Count;

    // Count-based splitting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Count Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Count", ClampMin = "2", ClampMax = "16"))
    int32 OutputCount = 2;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Count Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Count"))
    TArray<int32> PointsPerOutput;

    // Percentage-based splitting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Percentage Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Percentage"))
    TArray<float> OutputPercentages;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Percentage Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Percentage"))
    bool bNormalizePercentages = true;

    // Attribute-based splitting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Attribute"))
    FString SplitAttribute = TEXT("Density");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Attribute"))
    TArray<FVector2D> AttributeRanges;

    // Spatial splitting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Spatial"))
    TArray<FBox> SpatialRegions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Spatial"))
    bool bUseOverlappingRegions = false;

    // Random splitting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Random Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Random"))
    int32 RandomSeed = 12345;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Random Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Random"))
    bool bEnsureMinimumPoints = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Random Split", meta = (EditCondition = "SplitCriteria == EAuracronPCGSplitCriteria::Random", ClampMin = "1"))
    int32 MinimumPointsPerOutput = 1;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedPointSplitterElement, UAuracronPCGAdvancedPointSplitterSettings)

// =============================================================================
// ADVANCED POINT SORTER
// =============================================================================

/**
 * Advanced Point Sorter
 * Enhanced point sorting with multiple criteria and custom comparisons
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedPointSorterSettings, FAuracronPCGAdvancedPointSorterElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedPointSorterSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedPointSorterSettings();

    // Sort criteria
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sort Settings")
    EAuracronPCGSortCriteria PrimarySortCriteria = EAuracronPCGSortCriteria::Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sort Settings")
    bool bUseSecondaryCriteria = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sort Settings", meta = (EditCondition = "bUseSecondaryCriteria"))
    EAuracronPCGSortCriteria SecondarySortCriteria = EAuracronPCGSortCriteria::Density;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sort Settings")
    bool bDescendingOrder = false;

    // Position sorting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position Sort", meta = (EditCondition = "PrimarySortCriteria == EAuracronPCGSortCriteria::Position || SecondarySortCriteria == EAuracronPCGSortCriteria::Position"))
    FVector SortAxis = FVector::ForwardVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Position Sort", meta = (EditCondition = "PrimarySortCriteria == EAuracronPCGSortCriteria::Position || SecondarySortCriteria == EAuracronPCGSortCriteria::Position"))
    bool bUseAbsolutePosition = false;

    // Distance sorting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Sort", meta = (EditCondition = "PrimarySortCriteria == EAuracronPCGSortCriteria::Distance || SecondarySortCriteria == EAuracronPCGSortCriteria::Distance"))
    FVector ReferencePoint = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Sort", meta = (EditCondition = "PrimarySortCriteria == EAuracronPCGSortCriteria::Distance || SecondarySortCriteria == EAuracronPCGSortCriteria::Distance"))
    bool bUse2DDistance = false;

    // Attribute sorting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute Sort", meta = (EditCondition = "PrimarySortCriteria == EAuracronPCGSortCriteria::Attribute || SecondarySortCriteria == EAuracronPCGSortCriteria::Attribute"))
    FString SortAttribute = TEXT("Density");

    // Mathematical sorting
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Sort")
    bool bUseMathOperation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Sort", meta = (EditCondition = "bUseMathOperation"))
    EAuracronPCGMathOperation SortMathOperation = EAuracronPCGMathOperation::Add;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Sort", meta = (EditCondition = "bUseMathOperation"))
    FString MathSourceAttribute = TEXT("Density");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Math Sort", meta = (EditCondition = "bUseMathOperation"))
    float MathOperand = 1.0f;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Settings")
    bool bUseStableSort = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Settings")
    bool bUseParallelSort = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Settings", meta = (ClampMin = "100", ClampMax = "10000"))
    int32 ParallelSortThreshold = 1000;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedPointSorterElement, UAuracronPCGAdvancedPointSorterSettings)

// =============================================================================
// POINT PROCESSING UTILITIES
// =============================================================================

/**
 * Point Processing Utilities
 * Utility functions for advanced point processing operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGPointProcessingUtils : public UObject
{
    GENERATED_BODY()

public:
    // Mathematical operations on point attributes
    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static float ApplyMathOperation(EAuracronPCGMathOperation Operation, float ValueA, float ValueB);

    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static FVector ApplyMathOperationVector(EAuracronPCGMathOperation Operation, const FVector& ValueA, const FVector& ValueB);

    // Point comparison functions
    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static bool ComparePoints(const FPCGPoint& PointA, const FPCGPoint& PointB, EAuracronPCGSortCriteria Criteria, const FVector& ReferencePoint = FVector::ZeroVector);

    // Spatial operations
    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static float CalculatePointDistance(const FPCGPoint& PointA, const FPCGPoint& PointB, bool bUse2D = false);

    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static bool IsPointInRegion(const FPCGPoint& Point, const FBox& Region);

    // Attribute operations
    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static bool GetPointAttributeValue(const FPCGPoint& Point, const UPCGMetadata* Metadata, const FString& AttributeName, float& OutValue);

    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static bool SetPointAttributeValue(FPCGPoint& Point, UPCGMetadata* Metadata, const FString& AttributeName, float Value);

    // Batch operations
    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static void ProcessPointsBatch(TArray<FPCGPoint>& Points, EAuracronPCGProcessingOperation Operation, const TMap<FString, float>& Parameters);

    // Performance utilities
    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static int32 GetOptimalBatchSize(int32 TotalPoints, int32 ThreadCount = 0);

    UFUNCTION(BlueprintCallable, Category = "Point Processing Utils")
    static bool ShouldUseParallelProcessing(int32 PointCount, int32 Threshold = 1000);
};

// Namespace for point processing utility functions
namespace AuracronPCGPointProcessingUtils
{
    AURACRONPCGFRAMEWORK_API float EvaluateCustomExpression(const FString& Expression, const TMap<FString, float>& Variables);
    AURACRONPCGFRAMEWORK_API TArray<FPCGPoint> FilterPointsByExpression(const TArray<FPCGPoint>& Points, const FString& Expression, const UPCGMetadata* Metadata);
    AURACRONPCGFRAMEWORK_API void SortPointsParallel(TArray<FPCGPoint>& Points, TFunction<bool(const FPCGPoint&, const FPCGPoint&)> Comparator);
    AURACRONPCGFRAMEWORK_API TArray<TArray<FPCGPoint>> SplitPointsIntoGroups(const TArray<FPCGPoint>& Points, int32 GroupCount, EAuracronPCGSplitCriteria Criteria);
    AURACRONPCGFRAMEWORK_API TArray<FPCGPoint> MergePointGroups(const TArray<TArray<FPCGPoint>>& PointGroups, EAuracronPCGMergeStrategy Strategy, const TArray<float>& Weights = {});
    AURACRONPCGFRAMEWORK_API void ApplyNoiseTransformation(TArray<FPCGPoint>& Points, EAuracronPCGNoiseType NoiseType, float Scale, float Intensity);
    AURACRONPCGFRAMEWORK_API FMatrix CalculateOptimalTransformMatrix(const TArray<FPCGPoint>& SourcePoints, const TArray<FPCGPoint>& TargetPoints);
    AURACRONPCGFRAMEWORK_API void OptimizePointDistribution(TArray<FPCGPoint>& Points, float MinDistance, float MaxDistance);
}
