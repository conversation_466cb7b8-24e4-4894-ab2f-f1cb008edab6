// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Mesh Generation Nodes Implementation
// Bridge 2.6: PCG Framework - Mesh Generation Nodes

#include "AuracronPCGMeshGeneration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "ProceduralMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Async/ParallelFor.h"

// =============================================================================
// ADVANCED STATIC MESH SPAWNER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedStaticMeshSpawnerSettings::UAuracronPCGAdvancedStaticMeshSpawnerSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Static Mesh Spawner");
    NodeMetadata.NodeDescription = TEXT("Enhanced version of the native Static Mesh Spawner with advanced features");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Spawner;
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.Tags.Add(TEXT("Spawner"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("Static"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.2f);
}

void UAuracronPCGAdvancedStaticMeshSpawnerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAdvancedStaticMeshSpawnerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Spatial;

    FPCGPinProperties& ActorsPin = OutputPins.Emplace_GetRef();
    ActorsPin.Label = TEXT("Actors");
    ActorsPin.AllowedTypes = EPCGDataType::Actor;
    ActorsPin.bAdvancedPin = true;
}

FAuracronPCGElementResult FAuracronPCGAdvancedStaticMeshSpawnerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                    FPCGDataCollection& OutputData, 
                                                                                    const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedStaticMeshSpawnerSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedStaticMeshSpawnerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Static Mesh Spawner");
            return Result;
        }

        if (Settings->MeshEntries.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No mesh entries configured");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 MeshesSpawned = 0;
        TArray<AActor*> SpawnedActors;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            
            // Process points in batches for performance
            if (Settings->bUseBatching && InputPoints.Num() > Settings->BatchSize)
            {
                ProcessPointsBatched(InputPoints, InputPointData->Metadata, SpawnedActors, Settings, MeshesSpawned);
            }
            else
            {
                ProcessPointsSequential(InputPoints, InputPointData->Metadata, SpawnedActors, Settings, MeshesSpawned);
            }

            TotalProcessed += InputPoints.Num();
        }

        // Create output data
        if (SpawnedActors.Num() > 0)
        {
            // Create spatial data output
            UPCGSpatialData* SpatialData = CreateSpatialDataFromActors(SpawnedActors);
            if (SpatialData)
            {
                FPCGTaggedData& SpatialTaggedData = OutputData.TaggedData.Emplace_GetRef();
                SpatialTaggedData.Data = SpatialData;
                SpatialTaggedData.Pin = TEXT("Output");
            }

            // Create actor data output
            UPCGActorData* ActorData = CreateActorDataFromActors(SpawnedActors);
            if (ActorData)
            {
                FPCGTaggedData& ActorTaggedData = OutputData.TaggedData.Emplace_GetRef();
                ActorTaggedData.Data = ActorData;
                ActorTaggedData.Pin = TEXT("Actors");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Static Mesh Spawner processed %d points and spawned %d meshes"), 
                                  TotalProcessed, MeshesSpawned);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Static Mesh Spawner error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGAdvancedStaticMeshSpawnerElement::ProcessPointsSequential(const TArray<FPCGPoint>& Points, 
                                                                           const UPCGMetadata* Metadata,
                                                                           TArray<AActor*>& OutSpawnedActors,
                                                                           const UAuracronPCGAdvancedStaticMeshSpawnerSettings* Settings,
                                                                           int32& OutMeshesSpawned) const
{
    for (const FPCGPoint& Point : Points)
    {
        AActor* SpawnedActor = SpawnMeshForPoint(Point, Metadata, Settings);
        if (SpawnedActor)
        {
            OutSpawnedActors.Add(SpawnedActor);
            OutMeshesSpawned++;
        }
    }
}

void FAuracronPCGAdvancedStaticMeshSpawnerElement::ProcessPointsBatched(const TArray<FPCGPoint>& Points, 
                                                                        const UPCGMetadata* Metadata,
                                                                        TArray<AActor*>& OutSpawnedActors,
                                                                        const UAuracronPCGAdvancedStaticMeshSpawnerSettings* Settings,
                                                                        int32& OutMeshesSpawned) const
{
    // Process in batches
    int32 BatchCount = FMath::CeilToInt(static_cast<float>(Points.Num()) / Settings->BatchSize);
    
    for (int32 BatchIndex = 0; BatchIndex < BatchCount; BatchIndex++)
    {
        int32 StartIndex = BatchIndex * Settings->BatchSize;
        int32 EndIndex = FMath::Min(StartIndex + Settings->BatchSize, Points.Num());
        
        TArray<AActor*> BatchActors;
        for (int32 i = StartIndex; i < EndIndex; i++)
        {
            AActor* SpawnedActor = SpawnMeshForPoint(Points[i], Metadata, Settings);
            if (SpawnedActor)
            {
                BatchActors.Add(SpawnedActor);
                OutMeshesSpawned++;
            }
        }
        
        OutSpawnedActors.Append(BatchActors);
    }
}

AActor* FAuracronPCGAdvancedStaticMeshSpawnerElement::SpawnMeshForPoint(const FPCGPoint& Point, 
                                                                        const UPCGMetadata* Metadata,
                                                                        const UAuracronPCGAdvancedStaticMeshSpawnerSettings* Settings) const
{
    // Select mesh based on selection mode
    int32 MeshIndex = UAuracronPCGMeshGenerationUtils::SelectMeshIndex(
        Settings->MeshEntries, Point, Metadata, Settings->SelectionMode, Settings->SelectionAttribute);
    
    if (MeshIndex < 0 || MeshIndex >= Settings->MeshEntries.Num())
    {
        return nullptr;
    }

    const FAuracronPCGMeshEntry& MeshEntry = Settings->MeshEntries[MeshIndex];
    
    // Validate mesh entry
    FString ValidationError;
    if (!UAuracronPCGMeshGenerationUtils::ValidateMeshEntry(MeshEntry, ValidationError))
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Invalid mesh entry: %s"), *ValidationError);
        return nullptr;
    }

    // Load mesh
    UStaticMesh* StaticMesh = MeshEntry.Mesh.LoadSynchronous();
    if (!StaticMesh)
    {
        return nullptr;
    }

    // Calculate transform
    FTransform SpawnTransform = AuracronPCGMeshGenerationUtils::CalculateInstanceTransform(Point, MeshEntry);

    // Spawn actor based on generation type
    AActor* SpawnedActor = nullptr;
    switch (Settings->GenerationType)
    {
        case EAuracronPCGMeshGenerationType::StaticMesh:
            SpawnedActor = SpawnStaticMeshActor(StaticMesh, SpawnTransform, MeshEntry, Settings);
            break;
        case EAuracronPCGMeshGenerationType::InstancedMesh:
            SpawnedActor = SpawnInstancedMeshActor(StaticMesh, SpawnTransform, MeshEntry, Settings);
            break;
        default:
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Unsupported generation type"));
            break;
    }

    return SpawnedActor;
}

AActor* FAuracronPCGAdvancedStaticMeshSpawnerElement::SpawnStaticMeshActor(UStaticMesh* StaticMesh, 
                                                                           const FTransform& Transform,
                                                                           const FAuracronPCGMeshEntry& MeshEntry,
                                                                           const UAuracronPCGAdvancedStaticMeshSpawnerSettings* Settings) const
{
    // Get world context
    UWorld* World = GetWorld();
    if (!World)
    {
        return nullptr;
    }

    // Spawn static mesh actor
    AStaticMeshActor* MeshActor = World->SpawnActor<AStaticMeshActor>(Transform.GetLocation(), Transform.GetRotation().Rotator());
    if (!MeshActor)
    {
        return nullptr;
    }

    // Configure static mesh component
    UStaticMeshComponent* MeshComponent = MeshActor->GetStaticMeshComponent();
    if (MeshComponent)
    {
        MeshComponent->SetStaticMesh(StaticMesh);
        MeshComponent->SetWorldTransform(Transform);
        
        // Apply material overrides
        AuracronPCGMeshGenerationUtils::SetupMeshMaterials(MeshComponent, MeshEntry.MaterialOverrides);
        
        // Configure rendering settings
        MeshComponent->SetCastShadow(MeshEntry.bCastShadows);
        MeshComponent->SetReceivesDecals(MeshEntry.bReceiveDecals);
        
        // Configure collision
        switch (MeshEntry.CollisionMode)
        {
            case EAuracronPCGCollisionMode::None:
                MeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
                break;
            case EAuracronPCGCollisionMode::Simple:
                MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                break;
            case EAuracronPCGCollisionMode::Complex:
                MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);
                break;
            default:
                break;
        }
        
        // Generate LODs if requested
        if (MeshEntry.bGenerateLODs && Settings->LODDistances.Num() > 0)
        {
            UAuracronPCGMeshGenerationUtils::GenerateLODs(StaticMesh, Settings->LODDistances, MeshEntry.LODCount);
        }
    }

    return MeshActor;
}

AActor* FAuracronPCGAdvancedStaticMeshSpawnerElement::SpawnInstancedMeshActor(UStaticMesh* StaticMesh, 
                                                                              const FTransform& Transform,
                                                                              const FAuracronPCGMeshEntry& MeshEntry,
                                                                              const UAuracronPCGAdvancedStaticMeshSpawnerSettings* Settings) const
{
    // For instanced meshes, we would typically manage a pool of instanced mesh components
    // This is a simplified implementation - in production you'd have proper instancing management
    return SpawnStaticMeshActor(StaticMesh, Transform, MeshEntry, Settings);
}

UPCGSpatialData* FAuracronPCGAdvancedStaticMeshSpawnerElement::CreateSpatialDataFromActors(const TArray<AActor*>& Actors) const
{
    // Create spatial data from spawned actors
    // This is a simplified implementation
    UPCGSpatialData* SpatialData = NewObject<UPCGSpatialData>();
    return SpatialData;
}

UPCGActorData* FAuracronPCGAdvancedStaticMeshSpawnerElement::CreateActorDataFromActors(const TArray<AActor*>& Actors) const
{
    // Create actor data from spawned actors
    // This is a simplified implementation
    if (Actors.Num() > 0)
    {
        UPCGActorData* ActorData = NewObject<UPCGActorData>();
        ActorData->Actor = Actors[0]; // Simplified - would handle multiple actors properly
        return ActorData;
    }
    return nullptr;
}

UWorld* FAuracronPCGAdvancedStaticMeshSpawnerElement::GetWorld() const
{
    // Get world from context - simplified implementation
    return GWorld;
}

// =============================================================================
// INSTANCED MESH GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGInstancedMeshGeneratorSettings::UAuracronPCGInstancedMeshGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("Instanced Mesh Generator");
    NodeMetadata.NodeDescription = TEXT("Generates instanced static meshes with advanced instancing features");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Spawner;
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.Tags.Add(TEXT("Instanced"));
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.Tags.Add(TEXT("Generator"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.8f);
}

void UAuracronPCGInstancedMeshGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGInstancedMeshGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Spatial;

    FPCGPinProperties& ComponentsPin = OutputPins.Emplace_GetRef();
    ComponentsPin.Label = TEXT("Components");
    ComponentsPin.AllowedTypes = EPCGDataType::Component;
    ComponentsPin.bAdvancedPin = true;
}

FAuracronPCGElementResult FAuracronPCGInstancedMeshGeneratorElement::ProcessData(const FPCGDataCollection& InputData,
                                                                                 FPCGDataCollection& OutputData,
                                                                                 const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGInstancedMeshGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGInstancedMeshGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Instanced Mesh Generator");
            return Result;
        }

        if (Settings->MeshEntries.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No mesh entries configured");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 InstancesCreated = 0;
        TArray<UInstancedStaticMeshComponent*> CreatedComponents;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();

            // Group points by mesh type for efficient instancing
            TMap<int32, TArray<FPCGPoint>> PointsByMesh;
            GroupPointsByMesh(InputPoints, InputPointData->Metadata, PointsByMesh, Settings);

            // Create instanced mesh components for each mesh type
            for (const auto& MeshGroup : PointsByMesh)
            {
                int32 MeshIndex = MeshGroup.Key;
                const TArray<FPCGPoint>& MeshPoints = MeshGroup.Value;

                if (MeshIndex >= 0 && MeshIndex < Settings->MeshEntries.Num())
                {
                    UInstancedStaticMeshComponent* Component = CreateInstancedMeshComponent(
                        MeshIndex, MeshPoints, Settings, InstancesCreated);

                    if (Component)
                    {
                        CreatedComponents.Add(Component);
                    }
                }
            }

            TotalProcessed += InputPoints.Num();
        }

        // Create output data
        if (CreatedComponents.Num() > 0)
        {
            // Create spatial data output
            UPCGSpatialData* SpatialData = CreateSpatialDataFromComponents(CreatedComponents);
            if (SpatialData)
            {
                FPCGTaggedData& SpatialTaggedData = OutputData.TaggedData.Emplace_GetRef();
                SpatialTaggedData.Data = SpatialData;
                SpatialTaggedData.Pin = TEXT("Output");
            }

            // Create component data output
            UPCGComponentData* ComponentData = CreateComponentDataFromComponents(CreatedComponents);
            if (ComponentData)
            {
                FPCGTaggedData& ComponentTaggedData = OutputData.TaggedData.Emplace_GetRef();
                ComponentTaggedData.Data = ComponentData;
                ComponentTaggedData.Pin = TEXT("Components");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Instanced Mesh Generator processed %d points and created %d instances"),
                                  TotalProcessed, InstancesCreated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Instanced Mesh Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGInstancedMeshGeneratorElement::GroupPointsByMesh(const TArray<FPCGPoint>& Points,
                                                                  const UPCGMetadata* Metadata,
                                                                  TMap<int32, TArray<FPCGPoint>>& OutPointsByMesh,
                                                                  const UAuracronPCGInstancedMeshGeneratorSettings* Settings) const
{
    OutPointsByMesh.Empty();

    for (const FPCGPoint& Point : Points)
    {
        int32 MeshIndex = UAuracronPCGMeshGenerationUtils::SelectMeshIndex(
            Settings->MeshEntries, Point, Metadata, EAuracronPCGMeshSelectionMode::Random, TEXT(""));

        if (MeshIndex >= 0 && MeshIndex < Settings->MeshEntries.Num())
        {
            OutPointsByMesh.FindOrAdd(MeshIndex).Add(Point);
        }
    }
}

UInstancedStaticMeshComponent* FAuracronPCGInstancedMeshGeneratorElement::CreateInstancedMeshComponent(int32 MeshIndex,
                                                                                                       const TArray<FPCGPoint>& Points,
                                                                                                       const UAuracronPCGInstancedMeshGeneratorSettings* Settings,
                                                                                                       int32& OutInstancesCreated) const
{
    if (MeshIndex < 0 || MeshIndex >= Settings->MeshEntries.Num())
    {
        return nullptr;
    }

    const FAuracronPCGMeshEntry& MeshEntry = Settings->MeshEntries[MeshIndex];

    // Load mesh
    UStaticMesh* StaticMesh = MeshEntry.Mesh.LoadSynchronous();
    if (!StaticMesh)
    {
        return nullptr;
    }

    // Validate mesh for instancing
    if (!AuracronPCGMeshGenerationUtils::ValidateMeshForInstancing(StaticMesh))
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Mesh not suitable for instancing"));
        return nullptr;
    }

    // Create instanced mesh component
    UInstancedStaticMeshComponent* Component = AuracronPCGMeshGenerationUtils::CreateInstancedMeshComponent(
        nullptr, StaticMesh, Settings->bUseHierarchicalInstancing);

    if (!Component)
    {
        return nullptr;
    }

    // Configure component
    ConfigureInstancedMeshComponent(Component, MeshEntry, Settings);

    // Add instances
    for (const FPCGPoint& Point : Points)
    {
        FTransform InstanceTransform = AuracronPCGMeshGenerationUtils::CalculateInstanceTransform(Point, MeshEntry);

        // Apply variation if enabled
        if (Settings->bUsePerInstanceVariation)
        {
            ApplyInstanceVariation(InstanceTransform, Point, Settings);
        }

        Component->AddInstance(InstanceTransform);
        OutInstancesCreated++;
    }

    // Optimize component
    AuracronPCGMeshGenerationUtils::OptimizeInstancedMeshComponent(Component);

    return Component;
}

void FAuracronPCGInstancedMeshGeneratorElement::ConfigureInstancedMeshComponent(UInstancedStaticMeshComponent* Component,
                                                                                const FAuracronPCGMeshEntry& MeshEntry,
                                                                                const UAuracronPCGInstancedMeshGeneratorSettings* Settings) const
{
    if (!Component)
    {
        return;
    }

    // Configure rendering settings
    Component->SetCastShadow(MeshEntry.bCastShadows);
    Component->SetReceivesDecals(MeshEntry.bReceiveDecals);

    // Configure LOD settings
    if (Settings->bUseLODOptimization && Settings->LODScreenSizes.Num() > 0)
    {
        // Configure LOD screen sizes - simplified implementation
        Component->SetCullDistances(0, Settings->LODScreenSizes[0] * 1000.0f);
    }

    // Configure collision
    switch (MeshEntry.CollisionMode)
    {
        case EAuracronPCGCollisionMode::None:
            Component->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            break;
        case EAuracronPCGCollisionMode::Simple:
            Component->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            break;
        default:
            break;
    }

    // Setup materials
    AuracronPCGMeshGenerationUtils::SetupMeshMaterials(Component, MeshEntry.MaterialOverrides);
}

void FAuracronPCGInstancedMeshGeneratorElement::ApplyInstanceVariation(FTransform& InstanceTransform,
                                                                       const FPCGPoint& Point,
                                                                       const UAuracronPCGInstancedMeshGeneratorSettings* Settings) const
{
    // Apply scale variation
    if (Settings->ScaleVariation.Size() > 0.0f)
    {
        FVector CurrentScale = InstanceTransform.GetScale3D();
        FVector Variation = FVector(
            FMath::RandRange(-Settings->ScaleVariation.X, Settings->ScaleVariation.X),
            FMath::RandRange(-Settings->ScaleVariation.Y, Settings->ScaleVariation.Y),
            FMath::RandRange(-Settings->ScaleVariation.Z, Settings->ScaleVariation.Z)
        );
        InstanceTransform.SetScale3D(CurrentScale + Variation);
    }

    // Apply rotation variation
    if (Settings->RotationVariation.Size() > 0.0f)
    {
        FRotator CurrentRotation = InstanceTransform.GetRotation().Rotator();
        FRotator Variation = FRotator(
            FMath::RandRange(-Settings->RotationVariation.X, Settings->RotationVariation.X),
            FMath::RandRange(-Settings->RotationVariation.Y, Settings->RotationVariation.Y),
            FMath::RandRange(-Settings->RotationVariation.Z, Settings->RotationVariation.Z)
        );
        InstanceTransform.SetRotation((CurrentRotation + Variation).Quaternion());
    }
}

UPCGSpatialData* FAuracronPCGInstancedMeshGeneratorElement::CreateSpatialDataFromComponents(const TArray<UInstancedStaticMeshComponent*>& Components) const
{
    // Create spatial data from instanced mesh components
    // This is a simplified implementation
    UPCGSpatialData* SpatialData = NewObject<UPCGSpatialData>();
    return SpatialData;
}

UPCGComponentData* FAuracronPCGInstancedMeshGeneratorElement::CreateComponentDataFromComponents(const TArray<UInstancedStaticMeshComponent*>& Components) const
{
    // Create component data from instanced mesh components
    // This is a simplified implementation
    if (Components.Num() > 0)
    {
        UPCGComponentData* ComponentData = NewObject<UPCGComponentData>();
        ComponentData->Component = Components[0]; // Simplified - would handle multiple components properly
        return ComponentData;
    }
    return nullptr;
}
