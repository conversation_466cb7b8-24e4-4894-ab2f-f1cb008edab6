// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Interaction System Implementation
// Bridge 4.9: Foliage - Interaction System

#include "AuracronFoliageInteraction.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageCollision.h"
#include "AuracronFoliageBridge.h"

// UE5.6 Interaction includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "InteractiveFoliageActor.h"

// Physics includes
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"

// Player interaction includes
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "Components/CapsuleComponent.h"
#include "Components/SphereComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Materials
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// =============================================================================
// FOLIAGE INTERACTION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageInteractionManager* UAuracronFoliageInteractionManager::Instance = nullptr;

UAuracronFoliageInteractionManager* UAuracronFoliageInteractionManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageInteractionManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageInteractionManager::Initialize(const FAuracronFoliageInteractionConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Interaction Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    PlayerInteractions.Empty();
    FoliageBending.Empty();
    RecoverySimulations.Empty();

    // Initialize performance data
    PerformanceData = FAuracronInteractionPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastInteractionUpdate = 0.0f;
    LastBendingUpdate = 0.0f;
    LastRecoveryUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Interaction Manager initialized with interaction type: %s, bending: %s"), 
                              *UEnum::GetValueAsString(Configuration.DefaultInteractionType),
                              Configuration.bEnableFoliageBending ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageInteractionManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    PlayerInteractions.Empty();
    FoliageBending.Empty();
    RecoverySimulations.Empty();

    // Reset references
    ManagedWorld.Reset();
    CollisionManager.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Interaction Manager shutdown completed"));
}

bool UAuracronFoliageInteractionManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageInteractionManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update player interactions
    LastInteractionUpdate += DeltaTime;
    if (LastInteractionUpdate >= Configuration.InteractionUpdateInterval)
    {
        FScopeLock Lock(&InteractionLock);
        
        int32 UpdatedInteractions = 0;
        const int32 MaxUpdatesThisFrame = Configuration.MaxInteractionUpdatesPerFrame;

        for (auto& InteractionPair : PlayerInteractions)
        {
            if (UpdatedInteractions >= MaxUpdatesThisFrame)
            {
                break;
            }

            FAuracronPlayerInteractionData& InteractionData = InteractionPair.Value;
            if (InteractionData.bIsActive)
            {
                UpdatePlayerInteractionInternal(InteractionData, DeltaTime);
                UpdatedInteractions++;
            }
        }

        LastInteractionUpdate = 0.0f;
    }

    // Update foliage bending
    if (Configuration.bEnableFoliageBending)
    {
        LastBendingUpdate += DeltaTime;
        if (LastBendingUpdate >= Configuration.InteractionUpdateInterval)
        {
            UpdateRealTimeBending(DeltaTime);
            LastBendingUpdate = 0.0f;
        }
    }

    // Update recovery simulations
    if (Configuration.bEnableRecoverySimulation)
    {
        LastRecoveryUpdate += DeltaTime;
        if (LastRecoveryUpdate >= Configuration.InteractionUpdateInterval)
        {
            for (auto& RecoveryPair : RecoverySimulations)
            {
                FAuracronRecoverySimulationData& RecoveryData = RecoveryPair.Value;
                if (RecoveryData.bIsRecovering)
                {
                    UpdateRecoverySimulationInternal(RecoveryData, DeltaTime);
                }
            }
            LastRecoveryUpdate = 0.0f;
        }
    }

    // Update advanced trampling effects
    if (Configuration.bEnableAdvancedTrampling)
    {
        UpdateAdvancedTramplingEffects(DeltaTime);
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliageInteractionManager::SetConfiguration(const FAuracronFoliageInteractionConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Interaction configuration updated"));
}

FAuracronFoliageInteractionConfiguration UAuracronFoliageInteractionManager::GetConfiguration() const
{
    return Configuration;
}

FString UAuracronFoliageInteractionManager::CreatePlayerInteraction(APawn* Player, const FVector& Location, EAuracronPlayerInteractionType InteractionType)
{
    if (!bIsInitialized || !Player)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Interaction Manager not initialized or invalid player"));
        return FString();
    }

    FScopeLock Lock(&InteractionLock);

    FString InteractionId = GenerateInteractionId();

    FAuracronPlayerInteractionData NewInteractionData;
    NewInteractionData.InteractionId = InteractionId;
    NewInteractionData.InteractingPlayer = Player;
    NewInteractionData.InteractionType = InteractionType;
    NewInteractionData.InteractionLocation = Location;
    NewInteractionData.InteractionRadius = Configuration.InteractionRadius;
    NewInteractionData.InteractionForce = Configuration.InteractionForce;
    NewInteractionData.InteractionIntensity = Configuration.InteractionSensitivity;
    NewInteractionData.bIsActive = true;
    NewInteractionData.InteractionStartTime = FDateTime::Now();

    // Calculate player velocity and mass
    if (ACharacter* Character = Cast<ACharacter>(Player))
    {
        NewInteractionData.PlayerVelocity = Character->GetVelocity().Size();
        NewInteractionData.PlayerMass = Character->GetMass();
    }

    // Calculate interaction direction
    if (Player->GetActorLocation() != Location)
    {
        NewInteractionData.InteractionDirection = (Location - Player->GetActorLocation()).GetSafeNormal();
    }

    PlayerInteractions.Add(InteractionId, NewInteractionData);

    OnPlayerInteractionStarted.Broadcast(InteractionId, Player);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Player interaction created: %s (type: %s)"), 
                              *InteractionId, 
                              *UEnum::GetValueAsString(InteractionType));

    return InteractionId;
}

void UAuracronFoliageInteractionManager::ProcessPlayerMovement(APawn* Player, const FVector& OldLocation, const FVector& NewLocation)
{
    if (!bIsInitialized || !Player || !Configuration.bEnableInteractionSystem)
    {
        return;
    }

    // Calculate movement vector and speed
    FVector MovementVector = NewLocation - OldLocation;
    float MovementSpeed = MovementVector.Size();

    if (MovementSpeed < 1.0f) // Ignore very small movements
    {
        return;
    }

    // Determine interaction type based on movement speed
    EAuracronPlayerInteractionType InteractionType = EAuracronPlayerInteractionType::Walk;
    
    if (ACharacter* Character = Cast<ACharacter>(Player))
    {
        float CharacterSpeed = Character->GetVelocity().Size();
        
        if (CharacterSpeed > 600.0f) // Running
        {
            InteractionType = EAuracronPlayerInteractionType::Run;
        }
        else if (CharacterSpeed < 100.0f) // Crouching/slow movement
        {
            InteractionType = EAuracronPlayerInteractionType::Crouch;
        }
    }

    // Create interaction at new location
    CreatePlayerInteraction(Player, NewLocation, InteractionType);
}

void UAuracronFoliageInteractionManager::ValidateConfiguration()
{
    // Validate interaction settings
    Configuration.InteractionRadius = FMath::Max(50.0f, Configuration.InteractionRadius);
    Configuration.InteractionForce = FMath::Max(1.0f, Configuration.InteractionForce);
    Configuration.InteractionSensitivity = FMath::Clamp(Configuration.InteractionSensitivity, 0.1f, 5.0f);

    // Validate bending settings
    Configuration.BendingStrength = FMath::Clamp(Configuration.BendingStrength, 0.1f, 5.0f);
    Configuration.BendingRadius = FMath::Max(50.0f, Configuration.BendingRadius);
    Configuration.MaxBendAngle = FMath::Clamp(Configuration.MaxBendAngle, 5.0f, 90.0f);

    // Validate recovery settings
    Configuration.RecoveryTime = FMath::Max(0.1f, Configuration.RecoveryTime);
    Configuration.RecoverySpeed = FMath::Max(0.1f, Configuration.RecoverySpeed);
    Configuration.RecoveryDamping = FMath::Clamp(Configuration.RecoveryDamping, 0.1f, 2.0f);

    // Validate trampling settings
    Configuration.TramplingThreshold = FMath::Max(1.0f, Configuration.TramplingThreshold);
    Configuration.TramplingDuration = FMath::Max(0.1f, Configuration.TramplingDuration);

    // Validate performance settings
    Configuration.MaxInteractionUpdatesPerFrame = FMath::Max(1, Configuration.MaxInteractionUpdatesPerFrame);
    Configuration.InteractionUpdateInterval = FMath::Max(0.01f, Configuration.InteractionUpdateInterval);
    Configuration.MaxInteractionDistance = FMath::Max(100.0f, Configuration.MaxInteractionDistance);

    // Validate audio settings
    Configuration.AudioVolumeMultiplier = FMath::Clamp(Configuration.AudioVolumeMultiplier, 0.0f, 2.0f);
    Configuration.AudioPitchVariation = FMath::Clamp(Configuration.AudioPitchVariation, 0.0f, 1.0f);

    // Validate visual effects settings
    Configuration.ParticleIntensity = FMath::Clamp(Configuration.ParticleIntensity, 0.0f, 5.0f);
    Configuration.ParticleLifetime = FMath::Max(0.1f, Configuration.ParticleLifetime);
}

FString UAuracronFoliageInteractionManager::GenerateInteractionId() const
{
    return FString::Printf(TEXT("Interaction_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageInteractionManager::GenerateBendingId() const
{
    return FString::Printf(TEXT("Bending_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageInteractionManager::GenerateRecoveryId() const
{
    return FString::Printf(TEXT("Recovery_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageInteractionManager::UpdatePlayerInteractionInternal(FAuracronPlayerInteractionData& InteractionData, float DeltaTime)
{
    // Update interaction duration
    InteractionData.InteractionDuration += DeltaTime;

    // Check if player is still valid and active
    if (!InteractionData.InteractingPlayer.IsValid())
    {
        InteractionData.bIsActive = false;
        return;
    }

    APawn* Player = InteractionData.InteractingPlayer.Get();

    // Update player velocity
    if (ACharacter* Character = Cast<ACharacter>(Player))
    {
        InteractionData.PlayerVelocity = Character->GetVelocity().Size();
    }

    // Process interaction with foliage
    ProcessPlayerInteractionWithFoliage(InteractionData);

    // Check for interaction timeout (optional)
    if (InteractionData.InteractionDuration > 5.0f) // 5 second timeout
    {
        InteractionData.bIsActive = false;
        OnPlayerInteractionEnded.Broadcast(InteractionData.InteractionId);
    }
}

void UAuracronFoliageInteractionManager::UpdateFoliageBendingInternal(FAuracronFoliageBendingData& BendingData, float DeltaTime)
{
    if (!BendingData.bIsActive)
    {
        return;
    }

    // Calculate bending progress
    CalculateBendingTransform(BendingData, DeltaTime);

    // Check if bending should start recovery
    if (!BendingData.bIsRecovering && BendingData.CurrentBendProgress >= 1.0f)
    {
        BendingData.bIsRecovering = true;

        // Start recovery simulation
        StartRecoveryForFoliage(BendingData.FoliageInstanceId);
    }
}

void UAuracronFoliageInteractionManager::UpdateRecoverySimulationInternal(FAuracronRecoverySimulationData& RecoveryData, float DeltaTime)
{
    if (!RecoveryData.bIsRecovering)
    {
        return;
    }

    // Calculate recovery progress
    CalculateRecoveryTransform(RecoveryData, DeltaTime);

    // Check if recovery is complete
    if (RecoveryData.RecoveryProgress >= 1.0f)
    {
        RecoveryData.bIsRecovering = false;
        RecoveryData.RecoveryProgress = 1.0f;

        OnFoliageRecoveryCompleted.Broadcast(RecoveryData.RecoveryId);

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage recovery completed: %s"), *RecoveryData.RecoveryId);
    }
}

void UAuracronFoliageInteractionManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&InteractionLock);

    // Reset counters
    PerformanceData.TotalInteractions = PlayerInteractions.Num();
    PerformanceData.ActiveInteractions = 0;
    PerformanceData.BendingInstances = FoliageBending.Num();
    PerformanceData.RecoveringInstances = 0;

    // Count active interactions
    for (const auto& InteractionPair : PlayerInteractions)
    {
        if (InteractionPair.Value.bIsActive)
        {
            PerformanceData.ActiveInteractions++;
        }
    }

    // Count recovering instances
    for (const auto& RecoveryPair : RecoverySimulations)
    {
        if (RecoveryPair.Value.bIsRecovering)
        {
            PerformanceData.RecoveringInstances++;
        }
    }

    // Calculate memory usage (simplified)
    PerformanceData.MemoryUsageMB = (PlayerInteractions.Num() * sizeof(FAuracronPlayerInteractionData) +
                                    FoliageBending.Num() * sizeof(FAuracronFoliageBendingData) +
                                    RecoverySimulations.Num() * sizeof(FAuracronRecoverySimulationData)) / (1024.0f * 1024.0f);
}

void UAuracronFoliageInteractionManager::ProcessPlayerInteractionWithFoliage(const FAuracronPlayerInteractionData& InteractionData)
{
    // Get foliage instances in interaction radius
    TArray<FString> AffectedInstances = GetFoliageInstancesInRadius(InteractionData.InteractionLocation, InteractionData.InteractionRadius);

    for (const FString& InstanceId : AffectedInstances)
    {
        // Apply interaction effects to each foliage instance
        ApplyInteractionEffects(InstanceId, InteractionData);
    }
}

void UAuracronFoliageInteractionManager::CalculateBendingTransform(FAuracronFoliageBendingData& BendingData, float DeltaTime)
{
    // Update bending progress
    BendingData.CurrentBendProgress += BendingData.BendingSpeed * DeltaTime;
    BendingData.CurrentBendProgress = FMath::Clamp(BendingData.CurrentBendProgress, 0.0f, 1.0f);

    // Calculate current bending angle
    float CurrentAngle = BendingData.MaxBendingAngle * BendingData.CurrentBendProgress * BendingData.BendingStrength;
    BendingData.BendingAngle = CurrentAngle;

    // Calculate bending rotation
    FVector BendAxis = FVector::CrossProduct(BendingData.BendingDirection, FVector::UpVector).GetSafeNormal();
    FQuat BendRotation = FQuat(BendAxis, FMath::DegreesToRadians(CurrentAngle));

    // Apply bending to current transform
    BendingData.CurrentTransform = BendingData.OriginalTransform;
    BendingData.CurrentTransform.SetRotation(BendingData.OriginalTransform.GetRotation() * BendRotation);
}

void UAuracronFoliageInteractionManager::CalculateRecoveryTransform(FAuracronRecoverySimulationData& RecoveryData, float DeltaTime)
{
    // Update recovery progress based on recovery type
    switch (RecoveryData.RecoveryType)
    {
        case EAuracronRecoverySimulationType::Linear:
            {
                RecoveryData.RecoveryProgress += (RecoveryData.RecoverySpeed / RecoveryData.RecoveryTime) * DeltaTime;
            }
            break;

        case EAuracronRecoverySimulationType::Exponential:
            {
                float ExponentialRate = 3.0f / RecoveryData.RecoveryTime; // 95% recovery in RecoveryTime
                RecoveryData.RecoveryProgress += (1.0f - RecoveryData.RecoveryProgress) * ExponentialRate * DeltaTime;
            }
            break;

        case EAuracronRecoverySimulationType::Spring:
            {
                // Spring physics simulation
                FVector CurrentPos = RecoveryData.CurrentTransform.GetLocation();
                FVector TargetPos = RecoveryData.TargetTransform.GetLocation();
                FVector Displacement = TargetPos - CurrentPos;

                // Spring force
                FVector SpringForce = Displacement * RecoveryData.SpringConstant;

                // Damping force
                FVector DampingForce = RecoveryData.Velocity * -RecoveryData.RecoveryDamping;

                // Total force
                FVector TotalForce = SpringForce + DampingForce;

                // Update velocity and position
                RecoveryData.Acceleration = TotalForce;
                RecoveryData.Velocity += RecoveryData.Acceleration * DeltaTime;

                FVector NewPosition = CurrentPos + RecoveryData.Velocity * DeltaTime;
                RecoveryData.CurrentTransform.SetLocation(NewPosition);

                // Update progress based on distance to target
                float Distance = FVector::Dist(NewPosition, TargetPos);
                float MaxDistance = FVector::Dist(RecoveryData.CurrentTransform.GetLocation(), TargetPos);
                RecoveryData.RecoveryProgress = 1.0f - (Distance / FMath::Max(MaxDistance, 1.0f));
            }
            break;

        default:
            RecoveryData.RecoveryProgress += (RecoveryData.RecoverySpeed / RecoveryData.RecoveryTime) * DeltaTime;
            break;
    }

    RecoveryData.RecoveryProgress = FMath::Clamp(RecoveryData.RecoveryProgress, 0.0f, 1.0f);
}
