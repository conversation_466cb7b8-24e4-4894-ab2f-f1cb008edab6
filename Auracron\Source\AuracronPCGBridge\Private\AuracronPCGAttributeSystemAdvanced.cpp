// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Attribute System Advanced Nodes Implementation
// Bridge 2.5: PCG Framework - Attribute System

#include "AuracronPCGAttributeSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"

// Engine includes
#include "Async/ParallelFor.h"

// =============================================================================
// ATTRIBUTE VALIDATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeValidatorSettings::UAuracronPCGAttributeValidatorSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Validator");
    NodeMetadata.NodeDescription = TEXT("Validates attributes against specified rules and constraints");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Attribute;
    NodeMetadata.Tags.Add(TEXT("Attribute"));
    NodeMetadata.Tags.Add(TEXT("Validate"));
    NodeMetadata.Tags.Add(TEXT("Quality"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.4f, 0.2f);
}

void UAuracronPCGAttributeValidatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeValidatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& ValidPin = OutputPins.Emplace_GetRef();
    ValidPin.Label = TEXT("Valid");
    ValidPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;

    if (bOutputValidationResults)
    {
        FPCGPinProperties& InvalidPin = OutputPins.Emplace_GetRef();
        InvalidPin.Label = TEXT("Invalid");
        InvalidPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
        InvalidPin.bAdvancedPin = true;

        FPCGPinProperties& ResultsPin = OutputPins.Emplace_GetRef();
        ResultsPin.Label = TEXT("Results");
        ResultsPin.AllowedTypes = EPCGDataType::AttributeSet;
        ResultsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAttributeValidatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                             FPCGDataCollection& OutputData, 
                                                                             const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        const UAuracronPCGAttributeValidatorSettings* Settings = GetTypedSettings<UAuracronPCGAttributeValidatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Validator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 ValidEntries = 0;
        int32 InvalidEntries = 0;
        TArray<FString> AllValidationErrors;

        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSpatialData* InputSpatialData = Cast<UPCGSpatialData>(TaggedData.Data);
            if (!InputSpatialData)
            {
                continue;
            }

            const UPCGPointData* InputPointData = InputSpatialData->ToPointData(nullptr);
            if (!InputPointData || !InputPointData->Metadata)
            {
                continue;
            }

            UPCGPointData* ValidData = NewObject<UPCGPointData>();
            UPCGPointData* InvalidData = Settings->bOutputValidationResults ? NewObject<UPCGPointData>() : nullptr;

            TArray<FString> ValidationErrors;
            int32 ValidCount = 0;
            int32 InvalidCount = 0;

            if (ProcessValidation(InputPointData, ValidData, InvalidData, ValidationErrors, ValidCount, InvalidCount, Settings))
            {
                TotalProcessed += InputPointData->GetPoints().Num();
                ValidEntries += ValidCount;
                InvalidEntries += InvalidCount;
                AllValidationErrors.Append(ValidationErrors);

                // Add valid data to output
                FPCGTaggedData& ValidTaggedData = OutputData.TaggedData.Emplace_GetRef();
                ValidTaggedData.Data = ValidData;
                ValidTaggedData.Pin = TEXT("Valid");
                ValidTaggedData.Tags = TaggedData.Tags;

                // Add invalid data if requested
                if (InvalidData && Settings->bOutputValidationResults)
                {
                    FPCGTaggedData& InvalidTaggedData = OutputData.TaggedData.Emplace_GetRef();
                    InvalidTaggedData.Data = InvalidData;
                    InvalidTaggedData.Pin = TEXT("Invalid");
                    InvalidTaggedData.Tags = TaggedData.Tags;
                }
            }
        }

        // Create validation results attribute set
        if (Settings->bOutputValidationResults)
        {
            UPCGAttributeSet* ResultsAttributeSet = CreateValidationResultsAttributeSet(AllValidationErrors, ValidEntries, InvalidEntries);
            if (ResultsAttributeSet)
            {
                FPCGTaggedData& ResultsTaggedData = OutputData.TaggedData.Emplace_GetRef();
                ResultsTaggedData.Data = ResultsAttributeSet;
                ResultsTaggedData.Pin = TEXT("Results");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        if (Settings->bLogValidationErrors && AllValidationErrors.Num() > 0)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Validation errors found: %s"), 
                                      *FString::Join(AllValidationErrors, TEXT("; ")));
        }

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Validator processed %d points (%d valid, %d invalid)"), 
                                  TotalProcessed, ValidEntries, InvalidEntries);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Validator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGAttributeValidatorElement::ProcessValidation(const UPCGPointData* InputData, 
                                                              UPCGPointData* ValidData, 
                                                              UPCGPointData* InvalidData,
                                                              TArray<FString>& ValidationErrors,
                                                              int32& ValidCount,
                                                              int32& InvalidCount,
                                                              const UAuracronPCGAttributeValidatorSettings* Settings) const
{
    if (!InputData || !ValidData)
    {
        return false;
    }

    // Initialize output data
    ValidData->InitializeFromData(InputData);
    if (InvalidData)
    {
        InvalidData->InitializeFromData(InputData);
    }

    const TArray<FPCGPoint>& InputPoints = InputData->GetPoints();
    TArray<FPCGPoint>& ValidPoints = ValidData->GetMutablePoints();
    TArray<FPCGPoint>* InvalidPoints = InvalidData ? &InvalidData->GetMutablePoints() : nullptr;

    ValidPoints.Empty();
    if (InvalidPoints)
    {
        InvalidPoints->Empty();
    }

    ValidCount = 0;
    InvalidCount = 0;

    // Validate each point
    for (int32 i = 0; i < InputPoints.Num(); i++)
    {
        const FPCGPoint& Point = InputPoints[i];
        TArray<FString> PointErrors;
        bool bIsValid = ValidatePoint(Point, InputData->Metadata, PointErrors, Settings);

        if (bIsValid)
        {
            ValidPoints.Add(Point);
            ValidCount++;
        }
        else
        {
            if (InvalidPoints)
            {
                InvalidPoints->Add(Point);
            }
            InvalidCount++;
            ValidationErrors.Append(PointErrors);

            if (Settings->bStopOnFirstError)
            {
                break;
            }
        }
    }

    // Remove invalid entries if requested
    if (Settings->bRemoveInvalidEntries && InvalidCount > 0)
    {
        // Already handled by separating valid and invalid points
    }

    return true;
}

bool FAuracronPCGAttributeValidatorElement::ValidatePoint(const FPCGPoint& Point, 
                                                          const UPCGMetadata* Metadata,
                                                          TArray<FString>& PointErrors,
                                                          const UAuracronPCGAttributeValidatorSettings* Settings) const
{
    bool bIsValid = true;
    PointErrors.Empty();

    if (!Metadata)
    {
        PointErrors.Add(TEXT("No metadata available"));
        return false;
    }

    // Validate against each rule
    for (const FAuracronPCGAttributeDescriptor& Rule : Settings->ValidationRules)
    {
        TArray<FString> RuleErrors;
        if (!UAuracronPCGAttributeSystemUtils::ValidateAttribute(Metadata, Rule, RuleErrors))
        {
            bIsValid = false;
            PointErrors.Append(RuleErrors);
        }
    }

    // Custom validation
    if (Settings->bUseCustomValidation && !Settings->CustomValidationExpression.IsEmpty())
    {
        // Simplified custom validation - in production you'd implement proper expression evaluation
        if (!EvaluateCustomValidation(Point, Metadata, Settings->CustomValidationExpression))
        {
            bIsValid = false;
            PointErrors.Add(FString::Printf(TEXT("Custom validation failed: %s"), *Settings->CustomValidationExpression));
        }
    }

    return bIsValid;
}

bool FAuracronPCGAttributeValidatorElement::EvaluateCustomValidation(const FPCGPoint& Point, 
                                                                     const UPCGMetadata* Metadata,
                                                                     const FString& Expression) const
{
    // Simplified custom validation - in production you'd implement proper expression evaluation
    // For now, just return true for any non-empty expression
    return !Expression.IsEmpty();
}

UPCGAttributeSet* FAuracronPCGAttributeValidatorElement::CreateValidationResultsAttributeSet(const TArray<FString>& ValidationErrors,
                                                                                              int32 ValidCount,
                                                                                              int32 InvalidCount) const
{
    UPCGAttributeSet* AttributeSet = NewObject<UPCGAttributeSet>();
    
    // Create metadata for results
    UPCGMetadata* Metadata = NewObject<UPCGMetadata>();
    Metadata->Initialize(1); // Single entry with summary

    // Create attributes for validation results
    Metadata->CreateIntegerAttribute(FName("ValidCount"), ValidCount, false);
    Metadata->CreateIntegerAttribute(FName("InvalidCount"), InvalidCount, false);
    Metadata->CreateIntegerAttribute(FName("TotalErrors"), ValidationErrors.Num(), false);
    Metadata->CreateStringAttribute(FName("ErrorSummary"), FString::Join(ValidationErrors, TEXT("; ")), false);

    AttributeSet->Metadata = Metadata;
    return AttributeSet;
}

// =============================================================================
// ATTRIBUTE AGGREGATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAttributeAggregatorSettings::UAuracronPCGAttributeAggregatorSettings()
{
    NodeMetadata.NodeName = TEXT("Attribute Aggregator");
    NodeMetadata.NodeDescription = TEXT("Aggregates attribute values using various statistical operations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Attribute;
    NodeMetadata.Tags.Add(TEXT("Attribute"));
    NodeMetadata.Tags.Add(TEXT("Aggregate"));
    NodeMetadata.Tags.Add(TEXT("Statistics"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.8f);
}

void UAuracronPCGAttributeAggregatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAttributeAggregatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point | EPCGDataType::Spatial;

    if (bOutputGroupStatistics)
    {
        FPCGPinProperties& StatsPin = OutputPins.Emplace_GetRef();
        StatsPin.Label = TEXT("Statistics");
        StatsPin.AllowedTypes = EPCGDataType::AttributeSet;
        StatsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAttributeAggregatorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                              FPCGDataCollection& OutputData, 
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        const UAuracronPCGAttributeAggregatorSettings* Settings = GetTypedSettings<UAuracronPCGAttributeAggregatorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Attribute Aggregator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 OperationsPerformed = 0;

        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGSpatialData* InputSpatialData = Cast<UPCGSpatialData>(TaggedData.Data);
            if (!InputSpatialData)
            {
                continue;
            }

            const UPCGPointData* InputPointData = InputSpatialData->ToPointData(nullptr);
            if (!InputPointData || !InputPointData->Metadata)
            {
                continue;
            }

            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            UPCGAttributeSet* StatisticsAttributeSet = nullptr;
            if (Settings->bOutputGroupStatistics)
            {
                StatisticsAttributeSet = NewObject<UPCGAttributeSet>();
            }

            if (ProcessAggregation(OutputPointData, StatisticsAttributeSet, Settings))
            {
                TotalProcessed += InputPointData->GetPoints().Num();
                OperationsPerformed += Settings->AggregationOperations.Num();

                // Add output data
                FPCGTaggedData& OutputTaggedData = OutputData.TaggedData.Emplace_GetRef();
                OutputTaggedData.Data = OutputPointData;
                OutputTaggedData.Pin = TEXT("Output");
                OutputTaggedData.Tags = TaggedData.Tags;

                // Add statistics if requested
                if (StatisticsAttributeSet)
                {
                    FPCGTaggedData& StatsTaggedData = OutputData.TaggedData.Emplace_GetRef();
                    StatsTaggedData.Data = StatisticsAttributeSet;
                    StatsTaggedData.Pin = TEXT("Statistics");
                    StatsTaggedData.Tags = TaggedData.Tags;
                }
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Attribute Aggregator processed %d points with %d operations"), 
                                  TotalProcessed, OperationsPerformed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Attribute Aggregator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGAttributeAggregatorElement::ProcessAggregation(UPCGPointData* PointData, 
                                                                UPCGAttributeSet* StatisticsAttributeSet,
                                                                const UAuracronPCGAttributeAggregatorSettings* Settings) const
{
    if (!PointData || !PointData->Metadata)
    {
        return false;
    }

    // Process each aggregation operation
    for (const FAuracronPCGAttributeOperation& Operation : Settings->AggregationOperations)
    {
        ProcessSingleAggregation(PointData, Operation, Settings);
    }

    // Generate statistics if requested
    if (StatisticsAttributeSet && Settings->bOutputGroupStatistics)
    {
        GenerateStatistics(PointData, StatisticsAttributeSet, Settings);
    }

    return true;
}

void FAuracronPCGAttributeAggregatorElement::ProcessSingleAggregation(UPCGPointData* PointData, 
                                                                       const FAuracronPCGAttributeOperation& Operation,
                                                                       const UAuracronPCGAttributeAggregatorSettings* Settings) const
{
    UPCGMetadata* Metadata = PointData->Metadata;
    if (!Metadata || !Metadata->HasAttribute(FName(*Operation.SourceAttribute)))
    {
        return;
    }

    // Create target attribute if needed
    if (!Metadata->HasAttribute(FName(*Operation.TargetAttribute)) && Operation.bCreateIfNotExists)
    {
        const UPCGMetadataAttributeBase* SourceAttr = Metadata->GetConstAttribute(FName(*Operation.SourceAttribute));
        if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
        {
            Metadata->CreateFloatAttribute(FName(*Operation.TargetAttribute), 0.0f, true);
        }
        // Add more type handling as needed
    }

    // Perform aggregation based on operation type
    switch (Operation.Operation)
    {
        case EAuracronPCGAttributeAggregation::Sum:
        case EAuracronPCGAttributeAggregation::Average:
        case EAuracronPCGAttributeAggregation::Min:
        case EAuracronPCGAttributeAggregation::Max:
        case EAuracronPCGAttributeAggregation::Count:
            PerformNumericAggregation(PointData, Operation);
            break;
        default:
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Unsupported aggregation operation"));
            break;
    }
}

void FAuracronPCGAttributeAggregatorElement::PerformNumericAggregation(UPCGPointData* PointData, 
                                                                        const FAuracronPCGAttributeOperation& Operation) const
{
    // Simplified numeric aggregation implementation
    // In production, you'd handle all attribute types and operations properly
    UPCGMetadata* Metadata = PointData->Metadata;
    const UPCGMetadataAttributeBase* SourceAttr = Metadata->GetConstAttribute(FName(*Operation.SourceAttribute));
    UPCGMetadataAttributeBase* TargetAttr = Metadata->GetMutableAttribute(FName(*Operation.TargetAttribute));

    if (!SourceAttr || !TargetAttr)
    {
        return;
    }

    // Handle float attributes
    if (SourceAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id && 
        TargetAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
    {
        const UPCGMetadataAttribute<float>* FloatSourceAttr = static_cast<const UPCGMetadataAttribute<float>*>(SourceAttr);
        UPCGMetadataAttribute<float>* FloatTargetAttr = static_cast<UPCGMetadataAttribute<float>*>(TargetAttr);

        const TArray<FPCGPoint>& Points = PointData->GetPoints();
        
        // Calculate aggregated value
        float AggregatedValue = 0.0f;
        float MinValue = FLT_MAX;
        float MaxValue = -FLT_MAX;
        float Sum = 0.0f;
        int32 Count = 0;

        for (const FPCGPoint& Point : Points)
        {
            float Value = FloatSourceAttr->GetValueFromItemKey(Point.MetadataEntry);
            Sum += Value;
            MinValue = FMath::Min(MinValue, Value);
            MaxValue = FMath::Max(MaxValue, Value);
            Count++;
        }

        switch (Operation.Operation)
        {
            case EAuracronPCGAttributeAggregation::Sum:
                AggregatedValue = Sum;
                break;
            case EAuracronPCGAttributeAggregation::Average:
                AggregatedValue = Count > 0 ? Sum / Count : 0.0f;
                break;
            case EAuracronPCGAttributeAggregation::Min:
                AggregatedValue = MinValue;
                break;
            case EAuracronPCGAttributeAggregation::Max:
                AggregatedValue = MaxValue;
                break;
            case EAuracronPCGAttributeAggregation::Count:
                AggregatedValue = static_cast<float>(Count);
                break;
            default:
                AggregatedValue = 0.0f;
                break;
        }

        // Set aggregated value to all points
        for (const FPCGPoint& Point : Points)
        {
            FloatTargetAttr->SetValueFromItemKey(Point.MetadataEntry, AggregatedValue);
        }
    }
}

void FAuracronPCGAttributeAggregatorElement::GenerateStatistics(UPCGPointData* PointData, 
                                                                UPCGAttributeSet* StatisticsAttributeSet,
                                                                const UAuracronPCGAttributeAggregatorSettings* Settings) const
{
    if (!StatisticsAttributeSet)
    {
        return;
    }

    // Create metadata for statistics
    UPCGMetadata* StatsMetadata = NewObject<UPCGMetadata>();
    StatsMetadata->Initialize(1); // Single entry with summary

    // Add basic statistics
    StatsMetadata->CreateIntegerAttribute(FName("TotalPoints"), PointData->GetPoints().Num(), false);
    StatsMetadata->CreateIntegerAttribute(FName("OperationsPerformed"), Settings->AggregationOperations.Num(), false);
    StatsMetadata->CreateStringAttribute(FName("ProcessingMode"), Settings->bUseGrouping ? TEXT("Grouped") : TEXT("Global"), false);

    StatisticsAttributeSet->Metadata = StatsMetadata;
}
