// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Debug System Extended Implementation
// Bridge 2.14: PCG Framework - Debugging Tools

#include "AuracronPCGDebugSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "HAL/IConsoleManager.h"

// =============================================================================
// GRAPH VISUALIZER IMPLEMENTATION
// =============================================================================

void UAuracronPCGGraphVisualizer::VisualizeGraph(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Calculate node layout
    TMap<FString, FVector> NodeLayout = CalculateNodeLayout(Graph, CenterLocation);
    
    // Draw nodes and connections
    DrawNodeConnections(World, Graph, CenterLocation);
    DrawNodeLabels(World, Graph, CenterLocation);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Graph visualization drawn at location: %s"), *CenterLocation.ToString());
}

void UAuracronPCGGraphVisualizer::VisualizeNodeHierarchy(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Simplified hierarchy visualization - in production you'd analyze actual node hierarchy
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Node hierarchy visualization not fully implemented"));
}

void UAuracronPCGGraphVisualizer::VisualizeDataFlow(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation, float AnimationTime)
{
    if (!World || !Graph)
    {
        return;
    }

    // Animated data flow visualization
    float AnimationPhase = FMath::Fmod(AnimationTime, 2.0f * PI);
    
    // Simplified data flow animation - in production you'd animate actual data flow
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Data flow visualization at animation time: %.3f"), AnimationTime);
}

void UAuracronPCGGraphVisualizer::VisualizeExecutionOrder(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Simplified execution order visualization - in production you'd show actual execution order
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Execution order visualization not fully implemented"));
}

void UAuracronPCGGraphVisualizer::DrawNode(UWorld* World, const UPCGSettings* NodeSettings, const FVector& Location, const FLinearColor& Color)
{
    if (!World || !NodeSettings)
    {
        return;
    }

    // Draw node as a box
    FVector BoxExtent(50.0f, 50.0f, 25.0f);
    DrawDebugBox(World, Location, BoxExtent, Color.ToFColor(true), false, 5.0f, 0, 2.0f);
    
    // Draw node name
    FString NodeName = GetNodeDisplayName(NodeSettings);
    DrawDebugString(World, Location + FVector(0, 0, 30), NodeName, nullptr, Color.ToFColor(true), 5.0f);
}

void UAuracronPCGGraphVisualizer::DrawNodeConnections(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Simplified connection drawing - in production you'd draw actual node connections
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Drawing node connections"));
}

void UAuracronPCGGraphVisualizer::DrawNodeLabels(UWorld* World, const UPCGGraph* Graph, const FVector& CenterLocation)
{
    if (!World || !Graph)
    {
        return;
    }

    // Simplified label drawing - in production you'd draw actual node labels
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Drawing node labels"));
}

TMap<FString, FVector> UAuracronPCGGraphVisualizer::CalculateNodeLayout(const UPCGGraph* Graph, const FVector& CenterLocation)
{
    TMap<FString, FVector> NodeLayout;
    
    if (!Graph)
    {
        return NodeLayout;
    }

    // Simplified layout calculation - in production you'd calculate actual node positions
    NodeLayout.Add(TEXT("InputNode"), CenterLocation + FVector(-200, 0, 0));
    NodeLayout.Add(TEXT("ProcessorNode"), CenterLocation);
    NodeLayout.Add(TEXT("OutputNode"), CenterLocation + FVector(200, 0, 0));
    
    return NodeLayout;
}

FVector UAuracronPCGGraphVisualizer::CalculateNodePosition(const UPCGSettings* NodeSettings, int32 NodeIndex, int32 TotalNodes, const FVector& CenterLocation)
{
    if (!NodeSettings || TotalNodes == 0)
    {
        return CenterLocation;
    }

    // Simple circular layout
    float Angle = (2.0f * PI * NodeIndex) / TotalNodes;
    float Radius = 200.0f;
    
    FVector Position;
    Position.X = CenterLocation.X + FMath::Cos(Angle) * Radius;
    Position.Y = CenterLocation.Y + FMath::Sin(Angle) * Radius;
    Position.Z = CenterLocation.Z;
    
    return Position;
}

void UAuracronPCGGraphVisualizer::ClearGraphVisualization(UWorld* World)
{
    if (World)
    {
        FlushDebugStrings(World);
        FlushPersistentDebugLines(World);
    }
}

FLinearColor UAuracronPCGGraphVisualizer::GetNodeColor(const UPCGSettings* NodeSettings)
{
    if (!NodeSettings)
    {
        return FLinearColor::White;
    }

    // Color coding based on node type
    FString ClassName = NodeSettings->GetClass()->GetName();
    
    if (ClassName.Contains(TEXT("Generator")))
    {
        return FLinearColor::Green;
    }
    else if (ClassName.Contains(TEXT("Modifier")))
    {
        return FLinearColor::Blue;
    }
    else if (ClassName.Contains(TEXT("Filter")))
    {
        return FLinearColor::Yellow;
    }
    else if (ClassName.Contains(TEXT("Sampler")))
    {
        return FLinearColor::Red;
    }
    
    return FLinearColor::White;
}

FString UAuracronPCGGraphVisualizer::GetNodeDisplayName(const UPCGSettings* NodeSettings)
{
    if (!NodeSettings)
    {
        return TEXT("Unknown Node");
    }

    return NodeSettings->GetClass()->GetName();
}

// =============================================================================
// STEP BY STEP EXECUTOR IMPLEMENTATION
// =============================================================================

bool UAuracronPCGStepByStepExecutor::bIsExecutionActive = false;
bool UAuracronPCGStepByStepExecutor::bIsExecutionPaused = false;
bool UAuracronPCGStepByStepExecutor::bIsReplayActive = false;
FString UAuracronPCGStepByStepExecutor::CurrentNodeName;
int32 UAuracronPCGStepByStepExecutor::CurrentStepIndex = 0;
TArray<FString> UAuracronPCGStepByStepExecutor::ExecutionHistory;
TArray<FString> UAuracronPCGStepByStepExecutor::Breakpoints;
FAuracronPCGDebugExecutionDescriptor UAuracronPCGStepByStepExecutor::CurrentDescriptor;

void UAuracronPCGStepByStepExecutor::StartStepByStepExecution(const UPCGGraph* Graph, const FAuracronPCGDebugExecutionDescriptor& Descriptor)
{
    if (!Graph)
    {
        return;
    }

    CurrentDescriptor = Descriptor;
    bIsExecutionActive = true;
    bIsExecutionPaused = false;
    CurrentStepIndex = 0;
    ExecutionHistory.Empty();
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Step-by-step execution started"));
}

void UAuracronPCGStepByStepExecutor::StopStepByStepExecution()
{
    bIsExecutionActive = false;
    bIsExecutionPaused = false;
    bIsReplayActive = false;
    CurrentNodeName.Empty();
    CurrentStepIndex = 0;
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Step-by-step execution stopped"));
}

void UAuracronPCGStepByStepExecutor::PauseExecution()
{
    if (bIsExecutionActive)
    {
        bIsExecutionPaused = true;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Execution paused"));
    }
}

void UAuracronPCGStepByStepExecutor::ResumeExecution()
{
    if (bIsExecutionActive && bIsExecutionPaused)
    {
        bIsExecutionPaused = false;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Execution resumed"));
    }
}

void UAuracronPCGStepByStepExecutor::StepForward()
{
    if (!bIsExecutionActive)
    {
        return;
    }

    CurrentStepIndex++;
    FString StepInfo = FString::Printf(TEXT("Step %d"), CurrentStepIndex);
    ExecutionHistory.Add(StepInfo);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Stepped forward to step %d"), CurrentStepIndex);
}

void UAuracronPCGStepByStepExecutor::StepBackward()
{
    if (!bIsExecutionActive || CurrentStepIndex <= 0)
    {
        return;
    }

    CurrentStepIndex--;
    if (ExecutionHistory.Num() > 0)
    {
        ExecutionHistory.RemoveAt(ExecutionHistory.Num() - 1);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Stepped backward to step %d"), CurrentStepIndex);
}

bool UAuracronPCGStepByStepExecutor::IsExecutionActive()
{
    return bIsExecutionActive;
}

bool UAuracronPCGStepByStepExecutor::IsExecutionPaused()
{
    return bIsExecutionPaused;
}

void UAuracronPCGStepByStepExecutor::AddBreakpoint(const FString& NodeName)
{
    if (!Breakpoints.Contains(NodeName))
    {
        Breakpoints.Add(NodeName);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Breakpoint added for node: %s"), *NodeName);
    }
}

void UAuracronPCGStepByStepExecutor::RemoveBreakpoint(const FString& NodeName)
{
    if (Breakpoints.Remove(NodeName) > 0)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Breakpoint removed for node: %s"), *NodeName);
    }
}

void UAuracronPCGStepByStepExecutor::ClearAllBreakpoints()
{
    int32 RemovedCount = Breakpoints.Num();
    Breakpoints.Empty();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleared %d breakpoints"), RemovedCount);
}

TArray<FString> UAuracronPCGStepByStepExecutor::GetBreakpoints()
{
    return Breakpoints;
}

bool UAuracronPCGStepByStepExecutor::HasBreakpoint(const FString& NodeName)
{
    return Breakpoints.Contains(NodeName);
}

FString UAuracronPCGStepByStepExecutor::GetCurrentNode()
{
    return CurrentNodeName;
}

int32 UAuracronPCGStepByStepExecutor::GetCurrentStep()
{
    return CurrentStepIndex;
}

int32 UAuracronPCGStepByStepExecutor::GetTotalSteps()
{
    return ExecutionHistory.Num();
}

TArray<FString> UAuracronPCGStepByStepExecutor::GetExecutionHistory()
{
    return ExecutionHistory;
}

void UAuracronPCGStepByStepExecutor::StartReplay()
{
    if (ExecutionHistory.Num() > 0)
    {
        bIsReplayActive = true;
        CurrentStepIndex = 0;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Replay started"));
    }
}

void UAuracronPCGStepByStepExecutor::StopReplay()
{
    bIsReplayActive = false;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Replay stopped"));
}

void UAuracronPCGStepByStepExecutor::SetReplaySpeed(float Speed)
{
    // Store replay speed in descriptor
    CurrentDescriptor.SlowMotionFactor = Speed;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Replay speed set to: %.2f"), Speed);
}

bool UAuracronPCGStepByStepExecutor::IsReplayActive()
{
    return bIsReplayActive;
}

// =============================================================================
// DEBUG CONSOLE COMMANDS IMPLEMENTATION
// =============================================================================

TArray<IConsoleCommand*> UAuracronPCGDebugConsoleCommands::RegisteredCommands;

void UAuracronPCGDebugConsoleCommands::RegisterConsoleCommands()
{
    // Register console commands for PCG debugging
    IConsoleCommand* ToggleVisCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.togglevis"),
        TEXT("Toggle PCG debug visualization"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::ToggleVisualization),
        ECVF_Default
    );
    RegisteredCommands.Add(ToggleVisCmd);

    IConsoleCommand* SetVisModeCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.setvismode"),
        TEXT("Set PCG debug visualization mode (0=None, 1=Points, 2=Connections, etc.)"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::SetVisualizationMode),
        ECVF_Default
    );
    RegisteredCommands.Add(SetVisModeCmd);

    IConsoleCommand* StartProfilingCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.startprofiling"),
        TEXT("Start PCG performance profiling"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::StartProfiling),
        ECVF_Default
    );
    RegisteredCommands.Add(StartProfilingCmd);

    IConsoleCommand* StopProfilingCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.stopprofiling"),
        TEXT("Stop PCG performance profiling"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::StopProfiling),
        ECVF_Default
    );
    RegisteredCommands.Add(StopProfilingCmd);

    IConsoleCommand* AddBreakpointCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.addbreakpoint"),
        TEXT("Add breakpoint for specified node name"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::AddBreakpoint),
        ECVF_Default
    );
    RegisteredCommands.Add(AddBreakpointCmd);

    IConsoleCommand* RemoveBreakpointCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.removebreakpoint"),
        TEXT("Remove breakpoint for specified node name"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::RemoveBreakpoint),
        ECVF_Default
    );
    RegisteredCommands.Add(RemoveBreakpointCmd);

    IConsoleCommand* ClearDebugCmd = IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("pcg.debug.clear"),
        TEXT("Clear all debug display"),
        FConsoleCommandWithArgsDelegate::CreateStatic(&UAuracronPCGDebugConsoleCommands::ClearDebugDisplay),
        ECVF_Default
    );
    RegisteredCommands.Add(ClearDebugCmd);

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug console commands registered"));
}

void UAuracronPCGDebugConsoleCommands::UnregisterConsoleCommands()
{
    for (IConsoleCommand* Command : RegisteredCommands)
    {
        if (Command)
        {
            IConsoleManager::Get().UnregisterConsoleObject(Command);
        }
    }
    RegisteredCommands.Empty();
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug console commands unregistered"));
}

void UAuracronPCGDebugConsoleCommands::ToggleVisualization(const TArray<FString>& Args)
{
    bool bCurrentlyEnabled = UAuracronPCGVisualDebugger::IsVisualizationEnabled();
    UAuracronPCGVisualDebugger::ToggleVisualization(!bCurrentlyEnabled);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug visualization %s"), 
                              !bCurrentlyEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronPCGDebugConsoleCommands::SetVisualizationMode(const TArray<FString>& Args)
{
    if (Args.Num() > 0)
    {
        int32 ModeValue = FCString::Atoi(*Args[0]);
        EAuracronPCGDebugVisualizationMode Mode = static_cast<EAuracronPCGDebugVisualizationMode>(ModeValue);
        UAuracronPCGVisualDebugger::SetVisualizationMode(Mode);
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug visualization mode set to: %d"), ModeValue);
    }
}

void UAuracronPCGDebugConsoleCommands::StartProfiling(const TArray<FString>& Args)
{
    FAuracronPCGDebugProfilingDescriptor Descriptor;
    UAuracronPCGPerformanceProfiler::StartProfiling(Descriptor);
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling started via console"));
}

void UAuracronPCGDebugConsoleCommands::StopProfiling(const TArray<FString>& Args)
{
    UAuracronPCGPerformanceProfiler::StopProfiling();
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling stopped via console"));
}

void UAuracronPCGDebugConsoleCommands::InspectGraph(const TArray<FString>& Args)
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Graph inspection requested via console"));
}

void UAuracronPCGDebugConsoleCommands::StepByStepExecution(const TArray<FString>& Args)
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Step-by-step execution requested via console"));
}

void UAuracronPCGDebugConsoleCommands::AddBreakpoint(const TArray<FString>& Args)
{
    if (Args.Num() > 0)
    {
        UAuracronPCGStepByStepExecutor::AddBreakpoint(Args[0]);
    }
}

void UAuracronPCGDebugConsoleCommands::RemoveBreakpoint(const TArray<FString>& Args)
{
    if (Args.Num() > 0)
    {
        UAuracronPCGStepByStepExecutor::RemoveBreakpoint(Args[0]);
    }
}

void UAuracronPCGDebugConsoleCommands::ClearDebugDisplay(const TArray<FString>& Args)
{
    if (UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull))
    {
        UAuracronPCGVisualDebugger::ClearDebugDisplay(World);
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Debug display cleared"));
    }
}

void UAuracronPCGDebugConsoleCommands::ExportProfilingData(const TArray<FString>& Args)
{
    FString FilePath = TEXT("Saved/Profiling/PCG/ConsoleExport.csv");
    if (Args.Num() > 0)
    {
        FilePath = Args[0];
    }
    
    bool bSuccess = UAuracronPCGPerformanceProfiler::ExportProfilingData(FilePath, true);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Profiling data export %s: %s"), 
                              bSuccess ? TEXT("successful") : TEXT("failed"), *FilePath);
}
