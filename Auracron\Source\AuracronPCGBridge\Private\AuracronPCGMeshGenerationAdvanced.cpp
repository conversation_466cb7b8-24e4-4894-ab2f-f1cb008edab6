// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Mesh Generation Advanced Utilities Implementation
// Bridge 2.6: PCG Framework - Mesh Generation Nodes

#include "AuracronPCGMeshGeneration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "ProceduralMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "PhysicsEngine/BodySetup.h"

namespace AuracronPCGMeshGenerationUtils
{
    // =============================================================================
    // STATIC MESH CREATION
    // =============================================================================

    UStaticMesh* CreateStaticMeshFromProcMesh(const FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, const FString& MeshName)
    {
        // Create new static mesh
        UStaticMesh* StaticMesh = NewObject<UStaticMesh>();
        if (!StaticMesh)
        {
            return nullptr;
        }

        // Set up static mesh data
        StaticMesh->SetNumSourceModels(1);
        FStaticMeshSourceModel& SourceModel = StaticMesh->GetSourceModel(0);
        
        // Create mesh description
        FMeshDescription* MeshDescription = StaticMesh->CreateMeshDescription(0);
        if (!MeshDescription)
        {
            return nullptr;
        }

        // Initialize mesh description
        FStaticMeshAttributes Attributes(*MeshDescription);
        Attributes.Register();

        // Add vertices
        TArray<FVertexID> VertexIDs;
        VertexIDs.Reserve(MeshDescriptor.Vertices.Num());
        
        for (const FVector& Vertex : MeshDescriptor.Vertices)
        {
            FVertexID VertexID = MeshDescription->CreateVertex();
            MeshDescription->SetVertexPosition(VertexID, Vertex);
            VertexIDs.Add(VertexID);
        }

        // Add triangles
        TArray<FVertexInstanceID> VertexInstanceIDs;
        for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
        {
            if (i + 2 < MeshDescriptor.Triangles.Num())
            {
                TArray<FVertexID> TriangleVertices;
                TriangleVertices.Add(VertexIDs[MeshDescriptor.Triangles[i]]);
                TriangleVertices.Add(VertexIDs[MeshDescriptor.Triangles[i + 1]]);
                TriangleVertices.Add(VertexIDs[MeshDescriptor.Triangles[i + 2]]);

                // Create polygon
                FPolygonID PolygonID = MeshDescription->CreatePolygon(FPolygonGroupID(0), TriangleVertices);
            }
        }

        // Set material if available
        if (MeshDescriptor.Material.IsValid())
        {
            UMaterialInterface* Material = MeshDescriptor.Material.LoadSynchronous();
            if (Material)
            {
                StaticMesh->SetMaterial(0, Material);
            }
        }

        // Build static mesh
        StaticMesh->CommitMeshDescription(0);
        StaticMesh->Build();

        return StaticMesh;
    }

    // =============================================================================
    // INSTANCED MESH COMPONENT CREATION
    // =============================================================================

    UInstancedStaticMeshComponent* CreateInstancedMeshComponent(AActor* Owner, UStaticMesh* Mesh, bool bUseHierarchical)
    {
        if (!Mesh)
        {
            return nullptr;
        }

        UInstancedStaticMeshComponent* Component = nullptr;
        
        if (bUseHierarchical)
        {
            Component = NewObject<UHierarchicalInstancedStaticMeshComponent>(Owner ? Owner : GetTransientPackage());
        }
        else
        {
            Component = NewObject<UInstancedStaticMeshComponent>(Owner ? Owner : GetTransientPackage());
        }

        if (Component)
        {
            Component->SetStaticMesh(Mesh);
            
            // Configure default settings
            Component->SetCastShadow(true);
            Component->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            Component->SetCollisionResponseToAllChannels(ECR_Block);
            
            // Attach to owner if provided
            if (Owner && Owner->GetRootComponent())
            {
                Component->AttachToComponent(Owner->GetRootComponent(), 
                    FAttachmentTransformRules::KeepWorldTransform);
            }
        }

        return Component;
    }

    // =============================================================================
    // MATERIAL SETUP
    // =============================================================================

    bool SetupMeshMaterials(UMeshComponent* MeshComponent, const TArray<TSoftObjectPtr<UMaterialInterface>>& Materials)
    {
        if (!MeshComponent)
        {
            return false;
        }

        // Apply material overrides
        for (int32 i = 0; i < Materials.Num(); i++)
        {
            if (Materials[i].IsValid())
            {
                UMaterialInterface* Material = Materials[i].LoadSynchronous();
                if (Material)
                {
                    MeshComponent->SetMaterial(i, Material);
                }
            }
        }

        return true;
    }

    // =============================================================================
    // TRANSFORM CALCULATION
    // =============================================================================

    FTransform CalculateInstanceTransform(const FPCGPoint& Point, const FAuracronPCGMeshEntry& MeshEntry)
    {
        // Start with point transform
        FTransform InstanceTransform = Point.Transform;

        // Apply local offset
        FVector LocalOffset = MeshEntry.LocalOffset;
        InstanceTransform.AddToTranslation(InstanceTransform.TransformVector(LocalOffset));

        // Apply local rotation
        FQuat LocalRotation = MeshEntry.LocalRotation.Quaternion();
        InstanceTransform.SetRotation(InstanceTransform.GetRotation() * LocalRotation);

        // Apply local scale
        FVector CurrentScale = InstanceTransform.GetScale3D();
        FVector NewScale = CurrentScale * MeshEntry.LocalScale;
        InstanceTransform.SetScale3D(NewScale);

        return InstanceTransform;
    }

    // =============================================================================
    // MESH VALIDATION
    // =============================================================================

    bool ValidateMeshForInstancing(UStaticMesh* Mesh)
    {
        if (!Mesh)
        {
            return false;
        }

        // Check if mesh has valid render data
        if (!Mesh->GetRenderData() || Mesh->GetRenderData()->LODResources.Num() == 0)
        {
            return false;
        }

        // Check if mesh has valid collision
        if (!Mesh->GetBodySetup())
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Mesh has no collision setup"));
        }

        // Check triangle count for performance
        const FStaticMeshLODResources& LODResource = Mesh->GetRenderData()->LODResources[0];
        int32 TriangleCount = LODResource.GetNumTriangles();
        
        if (TriangleCount > 10000)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Mesh has high triangle count (%d) - consider using LODs"), TriangleCount);
        }

        return true;
    }

    // =============================================================================
    // COMPONENT OPTIMIZATION
    // =============================================================================

    void OptimizeInstancedMeshComponent(UInstancedStaticMeshComponent* Component)
    {
        if (!Component)
        {
            return;
        }

        // Enable GPU culling for better performance
        Component->SetCullDistances(0, 10000.0f);
        
        // Configure LOD settings
        Component->bUseDefaultCollision = true;
        
        // For hierarchical instanced mesh components, configure clustering
        if (UHierarchicalInstancedStaticMeshComponent* HISMComponent = Cast<UHierarchicalInstancedStaticMeshComponent>(Component))
        {
            // Configure cluster settings for better performance
            HISMComponent->SetCullDistances(0, 15000.0f);
            HISMComponent->bUseDefaultCollision = true;
        }

        // Mark render state dirty to apply changes
        Component->MarkRenderStateDirty();
    }

    // =============================================================================
    // BOOLEAN OPERATIONS
    // =============================================================================

    bool PerformBooleanOperation(const FAuracronPCGProceduralMeshDescriptor& MeshA, 
                                 const FAuracronPCGProceduralMeshDescriptor& MeshB, 
                                 EAuracronPCGMeshCombineMode Operation, 
                                 FAuracronPCGProceduralMeshDescriptor& Result)
    {
        // Simplified boolean operations - in production you'd use proper CSG libraries
        Result = MeshA; // Start with mesh A

        switch (Operation)
        {
            case EAuracronPCGMeshCombineMode::Union:
            {
                // Simple union - just append vertices and triangles
                int32 VertexOffset = Result.Vertices.Num();
                Result.Vertices.Append(MeshB.Vertices);
                Result.Normals.Append(MeshB.Normals);
                Result.UVs.Append(MeshB.UVs);

                // Offset triangle indices for mesh B
                for (int32 Triangle : MeshB.Triangles)
                {
                    Result.Triangles.Add(Triangle + VertexOffset);
                }
                break;
            }
            case EAuracronPCGMeshCombineMode::Merge:
            {
                // Simple merge - same as union for now
                return PerformBooleanOperation(MeshA, MeshB, EAuracronPCGMeshCombineMode::Union, Result);
            }
            default:
                AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Boolean operation not implemented"));
                return false;
        }

        return true;
    }

    // =============================================================================
    // MESH SIMPLIFICATION
    // =============================================================================

    void SimplifyMesh(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, float SimplificationRatio, bool bPreserveBoundaries)
    {
        if (SimplificationRatio >= 1.0f)
        {
            return; // No simplification needed
        }

        // Simplified mesh simplification - in production you'd use proper decimation algorithms
        int32 TargetTriangleCount = FMath::FloorToInt(MeshDescriptor.Triangles.Num() / 3 * SimplificationRatio) * 3;
        
        if (TargetTriangleCount < MeshDescriptor.Triangles.Num())
        {
            // Simple approach: remove every nth triangle
            TArray<int32> SimplifiedTriangles;
            int32 Step = MeshDescriptor.Triangles.Num() / TargetTriangleCount;
            
            for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += Step * 3)
            {
                if (i + 2 < MeshDescriptor.Triangles.Num())
                {
                    SimplifiedTriangles.Add(MeshDescriptor.Triangles[i]);
                    SimplifiedTriangles.Add(MeshDescriptor.Triangles[i + 1]);
                    SimplifiedTriangles.Add(MeshDescriptor.Triangles[i + 2]);
                }
            }
            
            MeshDescriptor.Triangles = SimplifiedTriangles;
        }
    }

    // =============================================================================
    // LIGHTMAP UV GENERATION
    // =============================================================================

    bool GenerateLightmapUVs(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, int32 Resolution)
    {
        // Simplified lightmap UV generation
        // In production, you'd use proper UV unwrapping algorithms
        
        if (MeshDescriptor.UVs.Num() != MeshDescriptor.Vertices.Num())
        {
            // Generate basic UVs first
            UAuracronPCGMeshGenerationUtils::GenerateUVs(MeshDescriptor, 1.0f, false);
        }

        // For now, just copy the existing UVs
        // In production, you'd generate proper lightmap UVs with padding and no overlaps
        return true;
    }
}

// =============================================================================
// PROCEDURAL MESH CREATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGProceduralMeshCreatorSettings::UAuracronPCGProceduralMeshCreatorSettings()
{
    NodeMetadata.NodeName = TEXT("Procedural Mesh Creator");
    NodeMetadata.NodeDescription = TEXT("Creates procedural meshes from point data and geometric primitives");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Spawner;
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.Tags.Add(TEXT("Procedural"));
    NodeMetadata.Tags.Add(TEXT("Generator"));
    NodeMetadata.Tags.Add(TEXT("Primitive"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.2f, 0.8f);
}

void UAuracronPCGProceduralMeshCreatorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGProceduralMeshCreatorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Spatial;

    FPCGPinProperties& MeshPin = OutputPins.Emplace_GetRef();
    MeshPin.Label = TEXT("Meshes");
    MeshPin.AllowedTypes = EPCGDataType::StaticMesh;
    MeshPin.bAdvancedPin = true;
}

// =============================================================================
// MESH COMBINER IMPLEMENTATION
// =============================================================================

UAuracronPCGMeshCombinerSettings::UAuracronPCGMeshCombinerSettings()
{
    NodeMetadata.NodeName = TEXT("Mesh Combiner");
    NodeMetadata.NodeDescription = TEXT("Combines multiple meshes using various combination modes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Utility;
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.Tags.Add(TEXT("Combine"));
    NodeMetadata.Tags.Add(TEXT("Boolean"));
    NodeMetadata.Tags.Add(TEXT("Merge"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.8f, 0.2f);
}

void UAuracronPCGMeshCombinerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputAPin = InputPins.Emplace_GetRef();
    InputAPin.Label = TEXT("Mesh A");
    InputAPin.AllowedTypes = EPCGDataType::Spatial;

    FPCGPinProperties& InputBPin = InputPins.Emplace_GetRef();
    InputBPin.Label = TEXT("Mesh B");
    InputBPin.AllowedTypes = EPCGDataType::Spatial;
    InputBPin.bAllowMultipleConnections = true;
}

void UAuracronPCGMeshCombinerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Spatial;

    FPCGPinProperties& MeshPin = OutputPins.Emplace_GetRef();
    MeshPin.Label = TEXT("Combined Mesh");
    MeshPin.AllowedTypes = EPCGDataType::StaticMesh;
    MeshPin.bAdvancedPin = true;
}
