// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Noise System Implementation
// Bridge 2.9: PCG Framework - Noise e Randomization

#include "AuracronPCGNoiseSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Async/ParallelFor.h"

// =============================================================================
// ADVANCED NOISE GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedNoiseGeneratorSettings::UAuracronPCGAdvancedNoiseGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Noise Generator");
    NodeMetadata.NodeDescription = TEXT("Generates various types of noise with advanced parameters and fractal support");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Generator;
    NodeMetadata.Tags.Add(TEXT("Noise"));
    NodeMetadata.Tags.Add(TEXT("Procedural"));
    NodeMetadata.Tags.Add(TEXT("Generator"));
    NodeMetadata.Tags.Add(TEXT("Perlin"));
    NodeMetadata.Tags.Add(TEXT("Simplex"));
    NodeMetadata.Tags.Add(TEXT("Worley"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.4f, 0.8f);
}

void UAuracronPCGAdvancedNoiseGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseDomainWarp)
    {
        FPCGPinProperties& WarpPin = InputPins.Emplace_GetRef();
        WarpPin.Label = TEXT("Domain Warp");
        WarpPin.AllowedTypes = EPCGDataType::Point;
        WarpPin.bAdvancedPin = true;
    }
}

void UAuracronPCGAdvancedNoiseGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bGenerateVectorNoise)
    {
        FPCGPinProperties& VectorPin = OutputPins.Emplace_GetRef();
        VectorPin.Label = TEXT("Vector Noise");
        VectorPin.AllowedTypes = EPCGDataType::Point;
        VectorPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAdvancedNoiseGeneratorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                 FPCGDataCollection& OutputData, 
                                                                                 const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedNoiseGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedNoiseGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Noise Generator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 NoiseGenerated = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Generate noise for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Generate noise value
                float NoiseValue = GenerateNoiseForPoint(OutputPoint, Settings);
                
                // Apply noise to point based on settings
                ApplyNoiseToPoint(OutputPoint, NoiseValue, Settings);

                // Generate vector noise if requested
                if (Settings->bGenerateVectorNoise)
                {
                    FVector VectorNoise = GenerateVectorNoiseForPoint(OutputPoint, Settings);
                    ApplyVectorNoiseToPoint(OutputPoint, VectorNoise, Settings);
                }

                OutputPoints[i] = OutputPoint;
                NoiseGenerated++;
            }

            ProcessedPointData.Add(OutputPointData);
            TotalProcessed += InputPoints.Num();
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Noise Generator processed %d points and generated %d noise values"), 
                                  TotalProcessed, NoiseGenerated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Noise Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

float FAuracronPCGAdvancedNoiseGeneratorElement::GenerateNoiseForPoint(const FPCGPoint& Point, const UAuracronPCGAdvancedNoiseGeneratorSettings* Settings) const
{
    FVector Position = Point.Transform.GetLocation();
    
    // Apply offset
    Position += Settings->NoiseDescriptor.Offset;
    
    // Apply frequency scaling
    Position *= Settings->NoiseDescriptor.Frequency;

    // Generate base noise
    float NoiseValue = UAuracronPCGNoiseSystemUtils::GenerateNoise(Position, Settings->NoiseDescriptor);

    // Apply domain warp if enabled
    if (Settings->bUseDomainWarp)
    {
        FVector WarpOffset = GenerateDomainWarp(Position, Settings);
        FVector WarpedPosition = Position + WarpOffset * Settings->DomainWarpStrength;
        NoiseValue = UAuracronPCGNoiseSystemUtils::GenerateNoise(WarpedPosition, Settings->NoiseDescriptor);
    }

    // Apply output transformations
    if (Settings->NoiseDescriptor.bAbsolute)
    {
        NoiseValue = FMath::Abs(NoiseValue);
    }

    if (Settings->NoiseDescriptor.bInvert)
    {
        NoiseValue = 1.0f - NoiseValue;
    }

    // Remap to output range
    NoiseValue = UAuracronPCGNoiseSystemUtils::RemapNoiseValue(
        NoiseValue, -1.0f, 1.0f, 
        Settings->NoiseDescriptor.OutputMin, 
        Settings->NoiseDescriptor.OutputMax);

    return NoiseValue;
}

FVector FAuracronPCGAdvancedNoiseGeneratorElement::GenerateVectorNoiseForPoint(const FPCGPoint& Point, const UAuracronPCGAdvancedNoiseGeneratorSettings* Settings) const
{
    FVector Position = Point.Transform.GetLocation();
    
    return UAuracronPCGNoiseSystemUtils::GenerateVectorNoise(
        Position, 
        Settings->NoiseDescriptor, 
        Settings->NoiseDescriptorY, 
        Settings->NoiseDescriptorZ);
}

FVector FAuracronPCGAdvancedNoiseGeneratorElement::GenerateDomainWarp(const FVector& Position, const UAuracronPCGAdvancedNoiseGeneratorSettings* Settings) const
{
    float WarpX = UAuracronPCGNoiseSystemUtils::GenerateNoise(Position, Settings->DomainWarpDescriptor);
    float WarpY = UAuracronPCGNoiseSystemUtils::GenerateNoise(Position + FVector(1000.0f, 0.0f, 0.0f), Settings->DomainWarpDescriptor);
    float WarpZ = UAuracronPCGNoiseSystemUtils::GenerateNoise(Position + FVector(0.0f, 1000.0f, 0.0f), Settings->DomainWarpDescriptor);
    
    return FVector(WarpX, WarpY, WarpZ);
}

void FAuracronPCGAdvancedNoiseGeneratorElement::ApplyNoiseToPoint(FPCGPoint& Point, float NoiseValue, const UAuracronPCGAdvancedNoiseGeneratorSettings* Settings) const
{
    // Set noise as attribute
    if (!Settings->OutputAttribute.IsEmpty())
    {
        // Simplified attribute setting - in production you'd use proper metadata
        Point.SetLocalBounds(FBox(FVector(-NoiseValue), FVector(NoiseValue)));
    }

    // Apply to position
    if (Settings->bOutputToPosition)
    {
        FVector CurrentPosition = Point.Transform.GetLocation();
        CurrentPosition.Z += NoiseValue * 100.0f; // Scale for visibility
        Point.Transform.SetLocation(CurrentPosition);
    }

    // Apply to scale
    if (Settings->bOutputToScale)
    {
        FVector CurrentScale = Point.Transform.GetScale3D();
        float ScaleFactor = FMath::Lerp(0.5f, 2.0f, (NoiseValue + 1.0f) * 0.5f);
        Point.Transform.SetScale3D(CurrentScale * ScaleFactor);
    }

    // Apply to rotation
    if (Settings->bOutputToRotation)
    {
        FRotator CurrentRotation = Point.Transform.GetRotation().Rotator();
        CurrentRotation.Yaw += NoiseValue * 180.0f;
        Point.Transform.SetRotation(CurrentRotation.Quaternion());
    }

    // Apply to density
    if (Settings->bOutputToDensity)
    {
        Point.Density = FMath::Clamp((NoiseValue + 1.0f) * 0.5f, 0.0f, 1.0f);
    }

    // Apply to color
    if (Settings->bOutputToColor)
    {
        float ColorValue = (NoiseValue + 1.0f) * 0.5f;
        Point.Color = FVector4(ColorValue, ColorValue, ColorValue, 1.0f);
    }
}

void FAuracronPCGAdvancedNoiseGeneratorElement::ApplyVectorNoiseToPoint(FPCGPoint& Point, const FVector& VectorNoise, const UAuracronPCGAdvancedNoiseGeneratorSettings* Settings) const
{
    // Apply vector noise to position offset
    FVector CurrentPosition = Point.Transform.GetLocation();
    CurrentPosition += VectorNoise * 50.0f; // Scale for visibility
    Point.Transform.SetLocation(CurrentPosition);

    // Apply vector noise to color
    FVector4 NoiseColor = FVector4(
        (VectorNoise.X + 1.0f) * 0.5f,
        (VectorNoise.Y + 1.0f) * 0.5f,
        (VectorNoise.Z + 1.0f) * 0.5f,
        1.0f
    );
    Point.Color = NoiseColor;
}

// =============================================================================
// ADVANCED RANDOMIZER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedRandomizerSettings::UAuracronPCGAdvancedRandomizerSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Randomizer");
    NodeMetadata.NodeDescription = TEXT("Provides advanced randomization capabilities with deterministic control");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Random"));
    NodeMetadata.Tags.Add(TEXT("Randomization"));
    NodeMetadata.Tags.Add(TEXT("Deterministic"));
    NodeMetadata.Tags.Add(TEXT("Seed"));
    NodeMetadata.NodeColor = FLinearColor(0.4f, 0.8f, 0.4f);
}

void UAuracronPCGAdvancedRandomizerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseWeightedRandomization)
    {
        FPCGPinProperties& WeightPin = InputPins.Emplace_GetRef();
        WeightPin.Label = TEXT("Weights");
        WeightPin.AllowedTypes = EPCGDataType::Point;
        WeightPin.bAdvancedPin = true;
    }
}

void UAuracronPCGAdvancedRandomizerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputRandomSeed)
    {
        FPCGPinProperties& SeedPin = OutputPins.Emplace_GetRef();
        SeedPin.Label = TEXT("Random Seeds");
        SeedPin.AllowedTypes = EPCGDataType::Attribute;
        SeedPin.bAdvancedPin = true;
    }

    if (bOutputStatistics)
    {
        FPCGPinProperties& StatsPin = OutputPins.Emplace_GetRef();
        StatsPin.Label = TEXT("Statistics");
        StatsPin.AllowedTypes = EPCGDataType::Attribute;
        StatsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAdvancedRandomizerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                             FPCGDataCollection& OutputData, 
                                                                             const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedRandomizerSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedRandomizerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Randomizer");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 PointsRandomized = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Randomize each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Generate seed for this point
                int32 PointSeed = GeneratePointSeed(OutputPoint, i, Settings);
                
                // Apply randomization
                ApplyRandomizationToPoint(OutputPoint, PointSeed, Settings);

                OutputPoints[i] = OutputPoint;
                PointsRandomized++;
            }

            ProcessedPointData.Add(OutputPointData);
            TotalProcessed += InputPoints.Num();
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Randomizer processed %d points and randomized %d points"), 
                                  TotalProcessed, PointsRandomized);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Randomizer error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

int32 FAuracronPCGAdvancedRandomizerElement::GeneratePointSeed(const FPCGPoint& Point, int32 PointIndex, const UAuracronPCGAdvancedRandomizerSettings* Settings) const
{
    int32 Seed = Settings->RandomizationDescriptor.BaseSeed;

    switch (Settings->RandomizationDescriptor.RandomizationMode)
    {
        case EAuracronPCGRandomizationMode::Deterministic:
            Seed += PointIndex;
            break;
        case EAuracronPCGRandomizationMode::SeedBased:
            Seed = Settings->RandomizationDescriptor.BaseSeed;
            break;
        case EAuracronPCGRandomizationMode::PositionBased:
        {
            FVector Position = Point.Transform.GetLocation();
            Seed += FMath::FloorToInt(Position.X) + FMath::FloorToInt(Position.Y) * 1000 + FMath::FloorToInt(Position.Z) * 1000000;
            break;
        }
        case EAuracronPCGRandomizationMode::TimeBased:
            Seed += FMath::FloorToInt(FPlatformTime::Seconds() * 1000.0);
            break;
        default:
            Seed += PointIndex;
            break;
    }

    return Seed;
}

void FAuracronPCGAdvancedRandomizerElement::ApplyRandomizationToPoint(FPCGPoint& Point, int32 Seed, const UAuracronPCGAdvancedRandomizerSettings* Settings) const
{
    FRandomStream RandomStream(Seed);

    // Randomize position
    if (Settings->bRandomizePosition)
    {
        FVector CurrentPosition = Point.Transform.GetLocation();
        FVector RandomOffset = FVector(
            RandomStream.FRandRange(-Settings->PositionRange.X, Settings->PositionRange.X),
            RandomStream.FRandRange(-Settings->PositionRange.Y, Settings->PositionRange.Y),
            RandomStream.FRandRange(-Settings->PositionRange.Z, Settings->PositionRange.Z)
        );
        Point.Transform.SetLocation(CurrentPosition + RandomOffset);
    }

    // Randomize rotation
    if (Settings->bRandomizeRotation)
    {
        FRotator RandomRotation = FRotator(
            RandomStream.FRandRange(-Settings->RotationRange.X, Settings->RotationRange.X),
            RandomStream.FRandRange(-Settings->RotationRange.Y, Settings->RotationRange.Y),
            RandomStream.FRandRange(-Settings->RotationRange.Z, Settings->RotationRange.Z)
        );
        Point.Transform.SetRotation(RandomRotation.Quaternion());
    }

    // Randomize scale
    if (Settings->bRandomizeScale)
    {
        float RandomScale = RandomStream.FRandRange(Settings->ScaleRange.X, Settings->ScaleRange.Y);
        Point.Transform.SetScale3D(FVector(RandomScale));
    }

    // Randomize density
    if (Settings->bRandomizeDensity)
    {
        Point.Density = RandomStream.FRandRange(Settings->DensityRange.X, Settings->DensityRange.Y);
    }

    // Randomize color
    if (Settings->bRandomizeColor)
    {
        Point.Color = FVector4(
            RandomStream.FRand(),
            RandomStream.FRand(),
            RandomStream.FRand(),
            1.0f
        );
    }

    // Apply Gaussian distribution if enabled
    if (Settings->RandomizationDescriptor.bGaussianDistribution)
    {
        float GaussianValue = UAuracronPCGNoiseSystemUtils::GenerateGaussianRandom(
            Seed, 
            Settings->RandomizationDescriptor.GaussianMean, 
            Settings->RandomizationDescriptor.GaussianStdDev);
        
        // Apply Gaussian value to density as an example
        Point.Density = FMath::Clamp(GaussianValue, 0.0f, 1.0f);
    }
}

// =============================================================================
// NOISE COMBINER IMPLEMENTATION
// =============================================================================

UAuracronPCGNoiseCombinerSettings::UAuracronPCGNoiseCombinerSettings()
{
    NodeMetadata.NodeName = TEXT("Noise Combiner");
    NodeMetadata.NodeDescription = TEXT("Combines multiple noise sources using various blending modes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Noise"));
    NodeMetadata.Tags.Add(TEXT("Combiner"));
    NodeMetadata.Tags.Add(TEXT("Blend"));
    NodeMetadata.Tags.Add(TEXT("Mix"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.6f, 0.8f);
}

void UAuracronPCGNoiseCombinerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseMask)
    {
        FPCGPinProperties& MaskPin = InputPins.Emplace_GetRef();
        MaskPin.Label = TEXT("Mask");
        MaskPin.AllowedTypes = EPCGDataType::Point;
        MaskPin.bAdvancedPin = true;
    }
}

void UAuracronPCGNoiseCombinerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputIndividualNoises)
    {
        for (int32 i = 0; i < NoiseDescriptors.Num(); i++)
        {
            FPCGPinProperties& NoisePin = OutputPins.Emplace_GetRef();
            NoisePin.Label = FText::FromString(FString::Printf(TEXT("Noise %d"), i + 1));
            NoisePin.AllowedTypes = EPCGDataType::Point;
            NoisePin.bAdvancedPin = true;
        }
    }
}

FAuracronPCGElementResult FAuracronPCGNoiseCombinerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                        FPCGDataCollection& OutputData,
                                                                        const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGNoiseCombinerSettings* Settings = GetTypedSettings<UAuracronPCGNoiseCombinerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Noise Combiner");
            return Result;
        }

        if (Settings->NoiseDescriptors.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No noise descriptors provided");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 NoisesCombined = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Combine noise for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Generate individual noise values
                TArray<float> NoiseValues;
                GenerateIndividualNoises(OutputPoint, Settings, NoiseValues);

                // Combine noise values
                float CombinedNoise = CombineNoiseValues(NoiseValues, Settings);

                // Apply mask if enabled
                if (Settings->bUseMask)
                {
                    float MaskValue = GenerateMaskValue(OutputPoint, Settings);
                    CombinedNoise = UAuracronPCGNoiseSystemUtils::ApplyNoiseMask(CombinedNoise, MaskValue, Settings->bInvertMask);
                }

                // Apply combined noise to point
                ApplyCombinedNoiseToPoint(OutputPoint, CombinedNoise, Settings);

                OutputPoints[i] = OutputPoint;
                NoisesCombined++;
            }

            ProcessedPointData.Add(OutputPointData);
            TotalProcessed += InputPoints.Num();
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Noise Combiner processed %d points and combined %d noise values"),
                                  TotalProcessed, NoisesCombined);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Noise Combiner error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGNoiseCombinerElement::GenerateIndividualNoises(const FPCGPoint& Point, const UAuracronPCGNoiseCombinerSettings* Settings, TArray<float>& OutNoiseValues) const
{
    OutNoiseValues.Empty();
    OutNoiseValues.Reserve(Settings->NoiseDescriptors.Num());

    FVector Position = Point.Transform.GetLocation();

    for (const FAuracronPCGNoiseDescriptor& NoiseDescriptor : Settings->NoiseDescriptors)
    {
        float NoiseValue = UAuracronPCGNoiseSystemUtils::GenerateNoise(Position, NoiseDescriptor);
        OutNoiseValues.Add(NoiseValue);
    }
}

float FAuracronPCGNoiseCombinerElement::CombineNoiseValues(const TArray<float>& NoiseValues, const UAuracronPCGNoiseCombinerSettings* Settings) const
{
    if (NoiseValues.Num() == 0)
    {
        return 0.0f;
    }

    if (NoiseValues.Num() == 1)
    {
        return NoiseValues[0];
    }

    // Ensure weights array matches noise values
    TArray<float> Weights = Settings->NoiseWeights;
    while (Weights.Num() < NoiseValues.Num())
    {
        Weights.Add(1.0f);
    }

    return UAuracronPCGNoiseSystemUtils::CombineNoiseValues(NoiseValues, Weights, Settings->CombineMode);
}

float FAuracronPCGNoiseCombinerElement::GenerateMaskValue(const FPCGPoint& Point, const UAuracronPCGNoiseCombinerSettings* Settings) const
{
    FVector Position = Point.Transform.GetLocation();
    return UAuracronPCGNoiseSystemUtils::GenerateNoise(Position, Settings->MaskDescriptor);
}

void FAuracronPCGNoiseCombinerElement::ApplyCombinedNoiseToPoint(FPCGPoint& Point, float CombinedNoise, const UAuracronPCGNoiseCombinerSettings* Settings) const
{
    // Apply combination strength
    CombinedNoise *= Settings->CombinationStrength;

    // Normalize if requested
    if (Settings->bNormalizeResult)
    {
        CombinedNoise = FMath::Clamp(CombinedNoise, -1.0f, 1.0f);
    }

    // Set as attribute
    if (!Settings->OutputAttribute.IsEmpty())
    {
        // Simplified attribute setting - in production you'd use proper metadata
        Point.SetLocalBounds(FBox(FVector(-CombinedNoise), FVector(CombinedNoise)));
    }

    // Apply to point properties
    Point.Density = FMath::Clamp((CombinedNoise + 1.0f) * 0.5f, 0.0f, 1.0f);

    float ColorValue = (CombinedNoise + 1.0f) * 0.5f;
    Point.Color = FVector4(ColorValue, ColorValue, ColorValue, 1.0f);
}

// =============================================================================
// NOISE FIELD GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGNoiseFieldGeneratorSettings::UAuracronPCGNoiseFieldGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("Noise Field Generator");
    NodeMetadata.NodeDescription = TEXT("Generates 2D/3D noise fields for various applications");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Generator;
    NodeMetadata.Tags.Add(TEXT("Noise"));
    NodeMetadata.Tags.Add(TEXT("Field"));
    NodeMetadata.Tags.Add(TEXT("3D"));
    NodeMetadata.Tags.Add(TEXT("Volume"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.6f, 0.4f);
}

void UAuracronPCGNoiseFieldGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    // Optional input for field bounds
    FPCGPinProperties& BoundsPin = InputPins.Emplace_GetRef();
    BoundsPin.Label = TEXT("Bounds");
    BoundsPin.AllowedTypes = EPCGDataType::Spatial;
    BoundsPin.bAdvancedPin = true;
}

void UAuracronPCGNoiseFieldGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    if (bOutputAsPointCloud)
    {
        FPCGPinProperties& PointsPin = OutputPins.Emplace_GetRef();
        PointsPin.Label = TEXT("Points");
        PointsPin.AllowedTypes = EPCGDataType::Point;
    }

    if (bOutputAsTexture)
    {
        FPCGPinProperties& TexturePin = OutputPins.Emplace_GetRef();
        TexturePin.Label = TEXT("Texture");
        TexturePin.AllowedTypes = EPCGDataType::Texture;
        TexturePin.bAdvancedPin = true;
    }

    if (bOutputAsVolumeTexture)
    {
        FPCGPinProperties& VolumePin = OutputPins.Emplace_GetRef();
        VolumePin.Label = TEXT("Volume");
        VolumePin.AllowedTypes = EPCGDataType::Volume;
        VolumePin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGNoiseFieldGeneratorElement::ProcessData(const FPCGDataCollection& InputData,
                                                                              FPCGDataCollection& OutputData,
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGNoiseFieldGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGNoiseFieldGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Noise Field Generator");
            return Result;
        }

        int32 TotalGenerated = 0;

        // Generate noise field
        if (Settings->bOutputAsPointCloud)
        {
            UPCGPointData* PointData = GenerateNoisePointField(Settings, TotalGenerated);
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Points");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalGenerated;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Noise Field Generator generated %d noise field points"), TotalGenerated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Noise Field Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

UPCGPointData* FAuracronPCGNoiseFieldGeneratorElement::GenerateNoisePointField(const UAuracronPCGNoiseFieldGeneratorSettings* Settings, int32& OutTotalGenerated) const
{
    UPCGPointData* PointData = NewObject<UPCGPointData>();
    TArray<FPCGPoint> Points;

    FVector FieldMin = Settings->FieldCenter - Settings->FieldSize * 0.5f;
    FVector FieldMax = Settings->FieldCenter + Settings->FieldSize * 0.5f;
    FVector StepSize = Settings->FieldSize / FVector(Settings->Resolution);

    OutTotalGenerated = 0;

    // Generate points in 3D grid
    for (int32 x = 0; x < Settings->Resolution.X; x++)
    {
        for (int32 y = 0; y < Settings->Resolution.Y; y++)
        {
            for (int32 z = 0; z < Settings->Resolution.Z; z++)
            {
                FVector Position = FieldMin + FVector(x, y, z) * StepSize;

                // Generate noise value at this position
                float NoiseValue = UAuracronPCGNoiseSystemUtils::GenerateNoise(Position, Settings->NoiseDescriptor);

                // Apply threshold filtering if enabled
                if (Settings->bFilterByThreshold)
                {
                    bool PassesThreshold = Settings->bInvertThreshold ?
                        (NoiseValue < Settings->Threshold) :
                        (NoiseValue >= Settings->Threshold);

                    if (!PassesThreshold)
                    {
                        continue;
                    }
                }

                // Create point
                FPCGPoint Point;
                Point.Transform = FTransform(Position);
                Point.Density = FMath::Clamp((NoiseValue + 1.0f) * 0.5f, 0.0f, 1.0f);

                float ColorValue = (NoiseValue + 1.0f) * 0.5f;
                Point.Color = FVector4(ColorValue, ColorValue, ColorValue, 1.0f);

                Points.Add(Point);
                OutTotalGenerated++;
            }
        }
    }

    PointData->GetMutablePoints() = Points;
    return PointData;
}
