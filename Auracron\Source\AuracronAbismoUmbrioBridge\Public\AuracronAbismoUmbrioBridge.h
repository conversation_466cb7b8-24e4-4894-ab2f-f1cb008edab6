// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema Abismo Umbrio Bridge
// Integração C++ para o sistema subterrâneo procedural

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "ProceduralContentGeneration/Public/PCGComponent.h"
#include "ProceduralContentGeneration/Public/PCGGraph.h"
#include "ProceduralContentGeneration/Public/PCGNode.h"
#include "ProceduralContentGeneration/Public/Elements/PCGSurfaceSampler.h"
#include "ProceduralContentGeneration/Public/Elements/PCGSplineSampler.h"
#include "ProceduralContentGeneration/Public/Elements/PCGStaticMeshSpawner.h"
#include "Rendering/SkeletalMeshLODImporterData.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Particles/ParticleSystem.h"
#include "Particles/ParticleSystemComponent.h"
#include "Sound/SoundCue.h"
#include "Components/AudioComponent.h"
#include "AI/NavigationSystemBase.h"
#include "NavigationSystem.h"
#include "NavMesh/RecastNavMesh.h"
#include "Engine/World.h"
#include "Engine/Level.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "Rendering/RenderingCommon.h"
#include "RenderGraph.h"
#include "GlobalShader.h"
#include "ShaderParameterStruct.h"
#include "AuracronAbismoUmbrioBridge.generated.h"

// Forward Declarations
class UPCGComponent;
class UPCGGraph;
class UStaticMesh;
class UMaterialInterface;
class UParticleSystem;
class USoundCue;
class ARecastNavMesh;

/**
 * Estrutura para propriedades de cavernas subterrâneas
 */
USTRUCT(BlueprintType)
struct AURACRONABISMOUMBRIOBRIDGE_API FAuracronCaveProperties
{
    GENERATED_BODY()

    /** Profundidade da caverna em metros */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties", meta = (ClampMin = "10.0", ClampMax = "1000.0"))
    float Depth = 50.0f;

    /** Largura média da caverna */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties", meta = (ClampMin = "5.0", ClampMax = "500.0"))
    float Width = 25.0f;

    /** Altura média da caverna */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties", meta = (ClampMin = "3.0", ClampMax = "100.0"))
    float Height = 15.0f;

    /** Complexidade do sistema de túneis (0-1) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float TunnelComplexity = 0.5f;

    /** Densidade de stalactites/stalagmites */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float FormationDensity = 0.3f;

    /** Nível de umidade (afeta crescimento de fungos/musgos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float HumidityLevel = 0.7f;

    /** Temperatura da caverna em Celsius */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties", meta = (ClampMin = "-10.0", ClampMax = "40.0"))
    float Temperature = 12.0f;

    /** Presença de água subterrânea */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties")
    bool bHasUndergroundWater = true;

    /** Presença de cristais luminosos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties")
    bool bHasLuminousCrystals = false;

    /** Estabilidade estrutural (0-1, 0 = instável) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cave Properties", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float StructuralStability = 0.8f;
};

/**
 * Estrutura para configuração de iluminação subterrânea
 */
USTRUCT(BlueprintType)
struct AURACRONABISMOUMBRIOBRIDGE_API FAuracronUndergroundLighting
{
    GENERATED_BODY()

    /** Intensidade da luz ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground Lighting", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AmbientLightIntensity = 0.1f;

    /** Cor da luz ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground Lighting")
    FLinearColor AmbientLightColor = FLinearColor(0.2f, 0.3f, 0.5f, 1.0f);

    /** Densidade de fog volumétrico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground Lighting", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float VolumetricFogDensity = 0.3f;

    /** Cor do fog volumétrico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground Lighting")
    FLinearColor VolumetricFogColor = FLinearColor(0.1f, 0.1f, 0.2f, 1.0f);

    /** Intensidade de cristais luminosos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground Lighting", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float CrystalLuminosity = 2.0f;

    /** Cor dos cristais luminosos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground Lighting")
    FLinearColor CrystalColor = FLinearColor(0.3f, 0.8f, 1.0f, 1.0f);

    /** Usar Lumen para iluminação global */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground Lighting")
    bool bUseLumenGI = true;

    /** Usar ray tracing para reflexões */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground Lighting")
    bool bUseRayTracedReflections = true;
};

/**
 * Enumeração para tipos de formações geológicas
 */
UENUM(BlueprintType)
enum class EAuracronGeologicalFormation : uint8
{
    Stalactite      UMETA(DisplayName = "Stalactite"),
    Stalagmite      UMETA(DisplayName = "Stalagmite"),
    Column          UMETA(DisplayName = "Column"),
    Flowstone       UMETA(DisplayName = "Flowstone"),
    Crystal         UMETA(DisplayName = "Crystal"),
    UndergroundLake UMETA(DisplayName = "Underground Lake"),
    Chasm           UMETA(DisplayName = "Chasm"),
    RockFormation   UMETA(DisplayName = "Rock Formation")
};

/**
 * Estrutura para configuração de formações geológicas
 */
USTRUCT(BlueprintType)
struct AURACRONABISMOUMBRIOBRIDGE_API FAuracronGeologicalFormationConfig
{
    GENERATED_BODY()

    /** Tipo de formação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    EAuracronGeologicalFormation FormationType = EAuracronGeologicalFormation::Stalactite;

    /** Mesh estático para a formação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    TSoftObjectPtr<UStaticMesh> FormationMesh;

    /** Material para a formação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    TSoftObjectPtr<UMaterialInterface> FormationMaterial;

    /** Escala mínima */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    FVector ScaleMin = FVector(0.5f, 0.5f, 0.5f);

    /** Escala máxima */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    FVector ScaleMax = FVector(2.0f, 2.0f, 2.0f);

    /** Densidade de spawn (0-1) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float SpawnDensity = 0.3f;

    /** Rotação aleatória */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    bool bRandomRotation = true;

    /** Alinhar com normal da superfície */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    bool bAlignToSurfaceNormal = true;
};

/**
 * Enumeração para tipos de biomas subterrâneos
 */
UENUM(BlueprintType)
enum class EAuracronUndergroundBiome : uint8
{
    DryCave         UMETA(DisplayName = "Dry Cave"),
    WetCave         UMETA(DisplayName = "Wet Cave"),
    CrystalCavern   UMETA(DisplayName = "Crystal Cavern"),
    UndergroundRiver UMETA(DisplayName = "Underground River"),
    MushroomGrove   UMETA(DisplayName = "Mushroom Grove"),
    LavaChasm       UMETA(DisplayName = "Lava Chasm"),
    IceCave         UMETA(DisplayName = "Ice Cave"),
    AbyssalDepths   UMETA(DisplayName = "Abyssal Depths")
};

/**
 * Estrutura para configuração de biomas subterrâneos
 */
USTRUCT(BlueprintType)
struct AURACRONABISMOUMBRIOBRIDGE_API FAuracronUndergroundBiomeConfig
{
    GENERATED_BODY()

    /** Tipo de bioma */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    EAuracronUndergroundBiome BiomeType = EAuracronUndergroundBiome::DryCave;

    /** Configurações de formações geológicas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    TArray<FAuracronGeologicalFormationConfig> GeologicalFormations;

    /** Configuração de iluminação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    FAuracronUndergroundLighting LightingConfig;

    /** Propriedades da caverna */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    FAuracronCaveProperties CaveProperties;

    /** Sistema de partículas ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    TSoftObjectPtr<UParticleSystem> AmbientParticleSystem;

    /** Som ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    TSoftObjectPtr<USoundCue> AmbientSound;

    /** Temperatura do bioma */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "-50.0", ClampMax = "100.0"))
    float BiomeTemperature = 12.0f;

    /** Nível de perigo (0-1) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float DangerLevel = 0.3f;
};

/**
 * Classe principal do Bridge para Sistema Abismo Umbrio
 * Responsável pela geração procedural de sistemas subterrâneos complexos
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Abismo Umbrio", meta = (DisplayName = "AURACRON Abismo Umbrio Bridge"))
class AURACRONABISMOUMBRIOBRIDGE_API UAuracronAbismoUmbrioBridge : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronAbismoUmbrioBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Cave Generation ===

    /**
     * Gerar sistema de cavernas procedural
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Generation", CallInEditor = true)
    bool GenerateUndergroundSystem(const FVector& Origin, const FAuracronCaveProperties& Properties);

    /**
     * Gerar caverna individual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Generation", CallInEditor = true)
    bool GenerateCave(const FVector& Location, const FAuracronUndergroundBiomeConfig& BiomeConfig);

    /**
     * Gerar rede de túneis conectando cavernas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Generation", CallInEditor = true)
    bool GenerateTunnelNetwork(const TArray<FVector>& CaveLocations, float TunnelWidth = 5.0f);

    /**
     * Gerar formações geológicas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Generation", CallInEditor = true)
    bool GenerateGeologicalFormations(const FVector& CaveCenter, const TArray<FAuracronGeologicalFormationConfig>& Formations);

    // === Biome Management ===

    /**
     * Aplicar bioma a uma caverna
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Biomes", CallInEditor = true)
    bool ApplyBiomeToCave(const FVector& CaveLocation, const FAuracronUndergroundBiomeConfig& BiomeConfig);

    /**
     * Obter configuração de bioma por tipo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Biomes", CallInEditor = true)
    FAuracronUndergroundBiomeConfig GetBiomeConfiguration(EAuracronUndergroundBiome BiomeType) const;

    /**
     * Definir configuração de bioma
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Biomes", CallInEditor = true)
    void SetBiomeConfiguration(EAuracronUndergroundBiome BiomeType, const FAuracronUndergroundBiomeConfig& Config);

    // === Lighting and Atmosphere ===

    /**
     * Configurar iluminação subterrânea
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Lighting", CallInEditor = true)
    bool SetupUndergroundLighting(const FVector& CaveLocation, const FAuracronUndergroundLighting& LightingConfig);

    /**
     * Adicionar cristais luminosos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Lighting", CallInEditor = true)
    bool AddLuminousCrystals(const FVector& CaveLocation, int32 CrystalCount = 10, float LuminosityRange = 500.0f);

    /**
     * Configurar fog volumétrico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Lighting", CallInEditor = true)
    bool SetupVolumetricFog(const FVector& CaveLocation, const FAuracronUndergroundLighting& LightingConfig);

    // === Navigation and Pathfinding ===

    /**
     * Gerar navmesh para cavernas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Navigation", CallInEditor = true)
    bool GenerateCaveNavMesh(const FVector& CaveLocation, float CaveRadius = 50.0f);

    /**
     * Criar pontos de navegação 3D
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Navigation", CallInEditor = true)
    bool Create3DNavigationPoints(const TArray<FVector>& CaveLocations);

    /**
     * Validar conectividade de túneis
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Navigation", CallInEditor = true)
    bool ValidateTunnelConnectivity(const TArray<FVector>& CaveLocations);

protected:
    // === Internal Generation Methods ===
    
    /** Gerar geometria da caverna usando PCG */
    bool GenerateCaveGeometry(const FVector& Location, const FAuracronCaveProperties& Properties);
    
    /** Aplicar erosão procedural */
    bool ApplyProceduralErosion(const FVector& CaveLocation, float ErosionStrength = 0.5f);
    
    /** Gerar sistema de drenagem */
    bool GenerateDrainageSystem(const FVector& CaveLocation, const FAuracronCaveProperties& Properties);
    
    /** Calcular estabilidade estrutural */
    float CalculateStructuralStability(const FVector& CaveLocation, const FAuracronCaveProperties& Properties);
    
    /** Aplicar física de colapso */
    bool ApplyCollapsePhysics(const FVector& CaveLocation, float StabilityThreshold = 0.3f);

public:
    // === Configuration Properties ===

    /** Configurações de biomas disponíveis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<EAuracronUndergroundBiome, FAuracronUndergroundBiomeConfig> BiomeConfigurations;

    /** Componente PCG para geração procedural */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UPCGComponent> PCGComponent;

    /** Seed para geração procedural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "0", ClampMax = "999999"))
    int32 GenerationSeed = 12345;

    /** Usar multi-threading para geração */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseMultiThreading = true;

    /** Nível de detalhe para geração */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "5"))
    int32 DetailLevel = 3;

    /** Raio máximo do sistema subterrâneo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "100.0", ClampMax = "10000.0"))
    float MaxSystemRadius = 2000.0f;

    /** Profundidade máxima do sistema */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "10.0", ClampMax = "1000.0"))
    float MaxSystemDepth = 200.0f;

private:
    // === Internal State ===
    
    /** Cavernas geradas */
    TArray<FVector> GeneratedCaves;
    
    /** Túneis gerados */
    TArray<TPair<FVector, FVector>> GeneratedTunnels;
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Componentes gerados */
    TArray<TObjectPtr<UStaticMeshComponent>> GeneratedComponents;
    
    /** Timer para atualizações */
    FTimerHandle UpdateTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection GenerationMutex;

public:
    // === Python Integration ===
    
    /**
     * Inicializar bindings Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Python", CallInEditor = true)
    bool InitializePythonBindings();
    
    /**
     * Executar script Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Python", CallInEditor = true)
    bool ExecutePythonScript(const FString& ScriptPath);
    
    /**
     * Obter dados para Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Python", CallInEditor = true)
    FString GetSystemDataForPython() const;

    // === Utility Functions ===
    
    /**
     * Limpar sistema gerado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Utility", CallInEditor = true)
    void ClearGeneratedSystem();
    
    /**
     * Obter estatísticas do sistema
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Utility", CallInEditor = true)
    FString GetSystemStatistics() const;
    
    /**
     * Validar integridade do sistema
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Utility", CallInEditor = true)
    bool ValidateSystemIntegrity() const;

    // === UE 5.6 ADVANCED FEATURES ===

    /**
     * Configure advanced atmospheric effects using UE 5.6 volumetric systems
     * @param CaveLocation Location of the cave
     * @param AtmosphericDensity Density of atmospheric effects (0-1)
     * @param ParticleIntensity Intensity of particle effects (0-1)
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool ConfigureAdvancedAtmosphericEffects(const FVector& CaveLocation, float AtmosphericDensity = 0.5f, float ParticleIntensity = 0.7f);

    /**
     * Setup dynamic cave lighting with Lumen integration
     * @param CaveLocation Location of the cave
     * @param LightingIntensity Overall lighting intensity
     * @param bEnableRayTracing Enable hardware ray tracing if available
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool SetupDynamicCaveLighting(const FVector& CaveLocation, float LightingIntensity = 0.3f, bool bEnableRayTracing = true);

    /**
     * Generate procedural cave acoustics using UE 5.6 audio systems
     * @param CaveLocation Location of the cave
     * @param ReverbIntensity Reverb intensity (0-1)
     * @param EchoDelay Echo delay in seconds
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool GenerateProceduralCaveAcoustics(const FVector& CaveLocation, float ReverbIntensity = 0.8f, float EchoDelay = 0.5f);

    /**
     * Create advanced geological formations using Nanite virtualized geometry
     * @param CaveLocation Location of the cave
     * @param FormationComplexity Complexity level (1-10)
     * @param bUseNaniteGeometry Use Nanite for high-detail geometry
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool CreateAdvancedGeologicalFormations(const FVector& CaveLocation, int32 FormationComplexity = 5, bool bUseNaniteGeometry = true);

    /**
     * Setup dynamic weather effects for cave entrances
     * @param CaveLocation Location of the cave
     * @param WeatherIntensity Weather effect intensity (0-1)
     * @param bEnableWindEffects Enable wind particle effects
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool SetupDynamicWeatherEffects(const FVector& CaveLocation, float WeatherIntensity = 0.6f, bool bEnableWindEffects = true);

    /**
     * Generate advanced cave water systems with fluid simulation
     * @param CaveLocation Location of the cave
     * @param WaterLevel Water level (0-1)
     * @param bEnableFluidSimulation Enable advanced fluid simulation
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool GenerateAdvancedWaterSystems(const FVector& CaveLocation, float WaterLevel = 0.3f, bool bEnableFluidSimulation = true);

    /**
     * Create procedural cave ecosystems with advanced AI
     * @param CaveLocation Location of the cave
     * @param BiodiversityLevel Biodiversity level (1-10)
     * @param bEnableAdvancedAI Enable advanced AI behaviors
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool CreateProceduralCaveEcosystems(const FVector& CaveLocation, int32 BiodiversityLevel = 5, bool bEnableAdvancedAI = true);

    /**
     * Setup advanced cave physics with destruction systems
     * @param CaveLocation Location of the cave
     * @param StructuralIntegrity Structural integrity (0-1)
     * @param bEnableDestruction Enable destruction physics
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool SetupAdvancedCavePhysics(const FVector& CaveLocation, float StructuralIntegrity = 0.8f, bool bEnableDestruction = true);

    /**
     * Generate advanced cave materials with procedural texturing
     * @param CaveLocation Location of the cave
     * @param MaterialComplexity Material complexity level (1-10)
     * @param bUseProceduralTextures Use procedural texture generation
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool GenerateAdvancedCaveMaterials(const FVector& CaveLocation, int32 MaterialComplexity = 7, bool bUseProceduralTextures = true);

    /**
     * Create advanced cave navigation with 3D pathfinding
     * @param CaveLocation Location of the cave
     * @param NavigationComplexity Navigation complexity (1-10)
     * @param bEnable3DPathfinding Enable 3D pathfinding
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Abismo Umbrio|Advanced", CallInEditor = true)
    bool CreateAdvancedCaveNavigation(const FVector& CaveLocation, int32 NavigationComplexity = 6, bool bEnable3DPathfinding = true);
};
