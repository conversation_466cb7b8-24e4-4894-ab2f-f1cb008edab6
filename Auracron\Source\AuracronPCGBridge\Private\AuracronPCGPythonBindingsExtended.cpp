// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Python Bindings Extended Implementation
// Bridge 2.15: PCG Framework - Python Integration

#include "AuracronPCGPythonBindings.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"
#include "Async/Async.h"

// =============================================================================
// PYTHON BINDING MANAGER EXTENDED IMPLEMENTATION
// =============================================================================

void UAuracronPCGPythonBindingManager::BindGraphClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind graph-related classes
        pybind11::class_<UPCGGraph>(PCGModule, "PCGGraph")
            .def("get_class_name", [](const UPCGGraph& self) {
                return TCHAR_TO_UTF8(*self.GetClass()->GetName());
            });

        pybind11::class_<UPCGComponent>(PCGModule, "PCGComponent")
            .def("get_class_name", [](const UPCGComponent& self) {
                return TCHAR_TO_UTF8(*self.GetClass()->GetName());
            });

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Graph classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind graph classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

void UAuracronPCGPythonBindingManager::BindExecutionClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind execution-related enums and structures
        pybind11::enum_<EAuracronPCGExecutionMode>(PCGModule, "PCGExecutionMode")
            .value("Synchronous", EAuracronPCGExecutionMode::Synchronous)
            .value("Asynchronous", EAuracronPCGExecutionMode::Asynchronous)
            .value("Threaded", EAuracronPCGExecutionMode::Threaded);

        pybind11::class_<FAuracronPCGElementResult>(PCGModule, "PCGElementResult")
            .def(pybind11::init<>())
            .def_readwrite("success", &FAuracronPCGElementResult::bSuccess)
            .def_readwrite("error_message", &FAuracronPCGElementResult::ErrorMessage)
            .def_readwrite("execution_time", &FAuracronPCGElementResult::ExecutionTime)
            .def_readwrite("points_processed", &FAuracronPCGElementResult::PointsProcessed);

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Execution classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind execution classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

void UAuracronPCGPythonBindingManager::BindDebugClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind debug-related classes
        pybind11::enum_<EAuracronPCGDebugVisualizationMode>(PCGModule, "PCGDebugVisualizationMode")
            .value("None", EAuracronPCGDebugVisualizationMode::None)
            .value("Points", EAuracronPCGDebugVisualizationMode::Points)
            .value("Connections", EAuracronPCGDebugVisualizationMode::Connections)
            .value("BoundingBoxes", EAuracronPCGDebugVisualizationMode::BoundingBoxes)
            .value("All", EAuracronPCGDebugVisualizationMode::All);

        pybind11::class_<FAuracronPCGDebugVisualizationDescriptor>(PCGModule, "PCGDebugVisualizationDescriptor")
            .def(pybind11::init<>())
            .def_readwrite("visualization_mode", &FAuracronPCGDebugVisualizationDescriptor::VisualizationMode)
            .def_readwrite("show_points", &FAuracronPCGDebugVisualizationDescriptor::bShowPoints)
            .def_readwrite("point_size", &FAuracronPCGDebugVisualizationDescriptor::PointSize);

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind debug classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

void UAuracronPCGPythonBindingManager::BindCustomClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind custom node classes
        pybind11::enum_<EAuracronPCGCustomNodeTemplateType>(PCGModule, "PCGCustomNodeTemplateType")
            .value("Generator", EAuracronPCGCustomNodeTemplateType::Generator)
            .value("Modifier", EAuracronPCGCustomNodeTemplateType::Modifier)
            .value("Filter", EAuracronPCGCustomNodeTemplateType::Filter)
            .value("Custom", EAuracronPCGCustomNodeTemplateType::Custom);

        pybind11::class_<FAuracronPCGCustomNodeTemplate>(PCGModule, "PCGCustomNodeTemplate")
            .def(pybind11::init<>())
            .def_readwrite("template_name", &FAuracronPCGCustomNodeTemplate::TemplateName)
            .def_readwrite("display_name", &FAuracronPCGCustomNodeTemplate::DisplayName)
            .def_readwrite("description", &FAuracronPCGCustomNodeTemplate::Description)
            .def_readwrite("template_type", &FAuracronPCGCustomNodeTemplate::TemplateType);

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Custom classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind custom classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

void UAuracronPCGPythonBindingManager::BindUtilityClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind utility functions as module functions
        PCGModule.def("log_message", [](const std::string& message) {
            AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python: %s"), UTF8_TO_TCHAR(message.c_str()));
        });

        PCGModule.def("log_warning", [](const std::string& message) {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Python: %s"), UTF8_TO_TCHAR(message.c_str()));
        });

        PCGModule.def("log_error", [](const std::string& message) {
            AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Python: %s"), UTF8_TO_TCHAR(message.c_str()));
        });

        PCGModule.def("get_framework_version", []() {
            return "2.15.0";
        });

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Utility classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind utility classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

// =============================================================================
// PYTHON SCRIPT EXECUTOR IMPLEMENTATION
// =============================================================================

TMap<FString, FAuracronPCGPythonExecutionResult> UAuracronPCGPythonScriptExecutor::ExecutionCache;
bool UAuracronPCGPythonScriptExecutor::bCacheEnabled = true;

FAuracronPCGPythonExecutionResult UAuracronPCGPythonScriptExecutor::ExecuteScript(const FString& ScriptContent, const TMap<FString, FString>& Variables)
{
    FAuracronPCGPythonExecutionResult Result;
    
    // Check cache first
    if (bCacheEnabled && ExecutionCache.Contains(ScriptContent))
    {
        Result = ExecutionCache[ScriptContent];
        Result.Output += TEXT(" (from cache)");
        return Result;
    }

    UAuracronPCGPythonBindingManager* BindingManager = UAuracronPCGPythonBindingManager::GetInstance();
    if (!BindingManager || !BindingManager->IsInitialized())
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Python binding manager not initialized");
        return Result;
    }

    // Set variables in Python context
    for (const auto& VarPair : Variables)
    {
        FString SetVarCode = FString::Printf(TEXT("%s = '%s'"), *VarPair.Key, *VarPair.Value);
        BindingManager->ExecutePythonCode(SetVarCode);
    }

    // Execute the main script
    Result = BindingManager->ExecutePythonCode(ScriptContent);
    
    // Cache result if successful
    if (bCacheEnabled && Result.bSuccess)
    {
        ExecutionCache.Add(ScriptContent, Result);
    }

    return Result;
}

FAuracronPCGPythonExecutionResult UAuracronPCGPythonScriptExecutor::ExecuteScriptFile(const FString& FilePath, const TMap<FString, FString>& Variables)
{
    FAuracronPCGPythonExecutionResult Result;
    
    FString ScriptContent;
    if (!FFileHelper::LoadFileToString(ScriptContent, *FilePath))
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Failed to load script file: %s"), *FilePath);
        return Result;
    }

    return ExecuteScript(ScriptContent, Variables);
}

FAuracronPCGPythonExecutionResult UAuracronPCGPythonScriptExecutor::ExecuteScriptWithGraph(const FString& ScriptContent, UPCGGraph* Graph, const TMap<FString, FString>& Variables)
{
    FAuracronPCGPythonExecutionResult Result;
    
    if (!Graph)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Graph is null");
        return Result;
    }

    // Add graph context to variables
    TMap<FString, FString> ExtendedVariables = Variables;
    ExtendedVariables.Add(TEXT("graph_name"), Graph->GetName());
    ExtendedVariables.Add(TEXT("graph_class"), Graph->GetClass()->GetName());

    return ExecuteScript(ScriptContent, ExtendedVariables);
}

void UAuracronPCGPythonScriptExecutor::ExecuteScriptAsync(const FString& ScriptContent, const FString& CallbackFunction)
{
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [ScriptContent, CallbackFunction]()
    {
        FAuracronPCGPythonExecutionResult Result = ExecuteScript(ScriptContent);
        
        // Execute callback on game thread if provided
        if (!CallbackFunction.IsEmpty())
        {
            AsyncTask(ENamedThreads::GameThread, [CallbackFunction, Result]()
            {
                UAuracronPCGPythonBindingManager* BindingManager = UAuracronPCGPythonBindingManager::GetInstance();
                if (BindingManager && BindingManager->IsInitialized())
                {
                    FString CallbackCode = FString::Printf(TEXT("%s(%s, '%s', %.3f)"), 
                                                          *CallbackFunction, 
                                                          Result.bSuccess ? TEXT("True") : TEXT("False"),
                                                          *Result.ErrorMessage,
                                                          Result.ExecutionTime);
                    BindingManager->ExecutePythonCode(CallbackCode);
                }
            });
        }
    });
}

void UAuracronPCGPythonScriptExecutor::ExecuteScriptFileAsync(const FString& FilePath, const FString& CallbackFunction)
{
    FString ScriptContent;
    if (FFileHelper::LoadFileToString(ScriptContent, *FilePath))
    {
        ExecuteScriptAsync(ScriptContent, CallbackFunction);
    }
    else
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to load script file for async execution: %s"), *FilePath);
    }
}

bool UAuracronPCGPythonScriptExecutor::ValidateScript(const FString& ScriptContent, TArray<FString>& OutErrors)
{
    OutErrors.Empty();
    
    UAuracronPCGPythonBindingManager* BindingManager = UAuracronPCGPythonBindingManager::GetInstance();
    if (!BindingManager || !BindingManager->IsInitialized())
    {
        OutErrors.Add(TEXT("Python binding manager not initialized"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Try to compile the script without executing it
        pybind11::exec(TCHAR_TO_UTF8(*ScriptContent), pybind11::globals(), pybind11::dict(), true);
        return true;
    }
    catch (const std::exception& e)
    {
        OutErrors.Add(FString::Printf(TEXT("Script validation error: %s"), UTF8_TO_TCHAR(e.what())));
        return false;
    }
#else
    OutErrors.Add(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonScriptExecutor::ValidateScriptFile(const FString& FilePath, TArray<FString>& OutErrors)
{
    FString ScriptContent;
    if (!FFileHelper::LoadFileToString(ScriptContent, *FilePath))
    {
        OutErrors.Add(FString::Printf(TEXT("Failed to load script file: %s"), *FilePath));
        return false;
    }

    return ValidateScript(ScriptContent, OutErrors);
}

FString UAuracronPCGPythonScriptExecutor::FormatScript(const FString& ScriptContent)
{
    // Simplified script formatting - in production you'd use a proper Python formatter
    FString FormattedScript = ScriptContent;
    
    // Basic indentation fixes
    FormattedScript = FormattedScript.Replace(TEXT("\t"), TEXT("    "));
    
    // Remove trailing whitespace
    TArray<FString> Lines;
    FormattedScript.ParseIntoArrayLines(Lines);
    
    FString Result;
    for (const FString& Line : Lines)
    {
        FString TrimmedLine = Line.TrimEnd();
        Result += TrimmedLine + TEXT("\n");
    }
    
    return Result;
}

TArray<FString> UAuracronPCGPythonScriptExecutor::GetScriptDependencies(const FString& ScriptContent)
{
    TArray<FString> Dependencies;
    
    TArray<FString> Lines;
    ScriptContent.ParseIntoArrayLines(Lines);
    
    for (const FString& Line : Lines)
    {
        FString TrimmedLine = Line.TrimStartAndEnd();
        
        // Look for import statements
        if (TrimmedLine.StartsWith(TEXT("import ")) || TrimmedLine.StartsWith(TEXT("from ")))
        {
            Dependencies.Add(TrimmedLine);
        }
    }
    
    return Dependencies;
}

bool UAuracronPCGPythonScriptExecutor::HasScriptFunction(const FString& ScriptContent, const FString& FunctionName)
{
    FString SearchPattern = FString::Printf(TEXT("def %s("), *FunctionName);
    return ScriptContent.Contains(SearchPattern);
}
