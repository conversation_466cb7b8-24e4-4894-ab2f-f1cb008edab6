// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Collision Integration System Implementation
// Bridge 4.7: Foliage - Collision Integration

#include "AuracronFoliageCollision.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageBridge.h"
#include "AuracronPCGCollisionSystem.h"

// UE5.6 Collision includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"

// Physics includes
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"

// Destructible includes
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Materials
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// Geometry processing
#include "DynamicMesh/DynamicMesh3.h"
#include "DynamicMesh/MeshNormals.h"
#include "Operations/MeshSimplification.h"

// =============================================================================
// FOLIAGE COLLISION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageCollisionManager* UAuracronFoliageCollisionManager::Instance = nullptr;

UAuracronFoliageCollisionManager* UAuracronFoliageCollisionManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageCollisionManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageCollisionManager::Initialize(const FAuracronFoliageCollisionConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    CollisionMeshes.Empty();
    DestructibleFoliage.Empty();
    TramplingEffects.Empty();
    FoliagePhysicsTypes.Empty();

    // Initialize performance data
    PerformanceData = FAuracronCollisionPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastCollisionUpdate = 0.0f;
    LastPhysicsUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision Manager initialized with collision type: %s, physics: %s"), 
                              *UEnum::GetValueAsString(Configuration.DefaultCollisionType),
                              Configuration.bEnablePhysicsInteraction ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageCollisionManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    CollisionMeshes.Empty();
    DestructibleFoliage.Empty();
    TramplingEffects.Empty();
    FoliagePhysicsTypes.Empty();

    // Reset references
    ManagedWorld.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision Manager shutdown completed"));
}

bool UAuracronFoliageCollisionManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageCollisionManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update collision meshes
    LastCollisionUpdate += DeltaTime;
    if (LastCollisionUpdate >= Configuration.CollisionUpdateInterval)
    {
        FScopeLock Lock(&CollisionLock);
        
        int32 UpdatedMeshes = 0;
        const int32 MaxUpdatesThisFrame = Configuration.MaxCollisionUpdatesPerFrame;

        for (auto& MeshPair : CollisionMeshes)
        {
            if (UpdatedMeshes >= MaxUpdatesThisFrame)
            {
                break;
            }

            FAuracronCollisionMeshData& MeshData = MeshPair.Value;
            UpdateCollisionMeshInternal(MeshData, DeltaTime);
            UpdatedMeshes++;
        }

        LastCollisionUpdate = 0.0f;
    }

    // Update destructible foliage
    if (Configuration.bEnableDestructibleFoliage)
    {
        for (auto& DestructiblePair : DestructibleFoliage)
        {
            FAuracronDestructibleFoliageData& DestructibleData = DestructiblePair.Value;
            UpdateDestructibleFoliageInternal(DestructibleData, DeltaTime);
        }
    }

    // Update trampling effects
    if (Configuration.bEnableTramplingEffects)
    {
        for (auto& TramplingPair : TramplingEffects)
        {
            FAuracronTramplingEffectData& TramplingData = TramplingPair.Value;
            UpdateTramplingEffectInternal(TramplingData, DeltaTime);
        }
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliageCollisionManager::SetConfiguration(const FAuracronFoliageCollisionConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision configuration updated"));
}

FAuracronFoliageCollisionConfiguration UAuracronFoliageCollisionManager::GetConfiguration() const
{
    return Configuration;
}

FString UAuracronFoliageCollisionManager::GenerateCollisionMesh(UStaticMesh* SourceMesh, EAuracronFoliageCollisionType CollisionType)
{
    if (!bIsInitialized || !SourceMesh)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized or invalid source mesh"));
        return FString();
    }

    FScopeLock Lock(&CollisionLock);

    FString CollisionMeshId = GenerateCollisionMeshId();

    FAuracronCollisionMeshData NewMeshData;
    NewMeshData.CollisionMeshId = CollisionMeshId;
    NewMeshData.SourceMesh = SourceMesh;
    NewMeshData.CollisionType = CollisionType;
    NewMeshData.GenerationTime = FDateTime::Now();

    // Generate simplified collision mesh based on type
    switch (CollisionType)
    {
        case EAuracronFoliageCollisionType::QueryOnly:
        case EAuracronFoliageCollisionType::PhysicsOnly:
        case EAuracronFoliageCollisionType::CollisionEnabled:
            {
                UStaticMesh* SimplifiedMesh = GenerateSimplifiedCollisionMesh(SourceMesh, NewMeshData.CollisionComplexity);
                if (SimplifiedMesh)
                {
                    NewMeshData.CollisionMesh = SimplifiedMesh;
                    NewMeshData.bIsGenerated = true;
                }
            }
            break;

        case EAuracronFoliageCollisionType::Destructible:
            {
                // For destructible, we need more complex collision setup
                NewMeshData.bUseComplexCollision = true;
                NewMeshData.bGenerateOverlapEvents = true;
                NewMeshData.CollisionMesh = SourceMesh; // Use original mesh for destructible
                NewMeshData.bIsGenerated = true;
            }
            break;

        case EAuracronFoliageCollisionType::Interactive:
        case EAuracronFoliageCollisionType::Trampling:
            {
                // For interactive/trampling, we need overlap events
                NewMeshData.bGenerateOverlapEvents = true;
                UStaticMesh* SimplifiedMesh = GenerateSimplifiedCollisionMesh(SourceMesh, 0.3f); // Lower complexity for interaction
                if (SimplifiedMesh)
                {
                    NewMeshData.CollisionMesh = SimplifiedMesh;
                    NewMeshData.bIsGenerated = true;
                }
            }
            break;

        default:
            NewMeshData.CollisionMesh = SourceMesh;
            NewMeshData.bIsGenerated = true;
            break;
    }

    CollisionMeshes.Add(CollisionMeshId, NewMeshData);

    OnCollisionMeshGenerated.Broadcast(CollisionMeshId, NewMeshData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh generated: %s (type: %s)"), 
                              *CollisionMeshId, 
                              *UEnum::GetValueAsString(CollisionType));

    return CollisionMeshId;
}

bool UAuracronFoliageCollisionManager::UpdateCollisionMesh(const FString& CollisionMeshId, const FAuracronCollisionMeshData& MeshData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    if (!CollisionMeshes.Contains(CollisionMeshId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision mesh not found: %s"), *CollisionMeshId);
        return false;
    }

    FAuracronCollisionMeshData UpdatedMeshData = MeshData;
    UpdatedMeshData.CollisionMeshId = CollisionMeshId;

    CollisionMeshes[CollisionMeshId] = UpdatedMeshData;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh updated: %s"), *CollisionMeshId);

    return true;
}

bool UAuracronFoliageCollisionManager::RemoveCollisionMesh(const FString& CollisionMeshId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    if (!CollisionMeshes.Contains(CollisionMeshId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision mesh not found: %s"), *CollisionMeshId);
        return false;
    }

    CollisionMeshes.Remove(CollisionMeshId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh removed: %s"), *CollisionMeshId);

    return true;
}

FAuracronCollisionMeshData UAuracronFoliageCollisionManager::GetCollisionMesh(const FString& CollisionMeshId) const
{
    FScopeLock Lock(&CollisionLock);

    if (const FAuracronCollisionMeshData* MeshData = CollisionMeshes.Find(CollisionMeshId))
    {
        return *MeshData;
    }

    return FAuracronCollisionMeshData();
}

TArray<FAuracronCollisionMeshData> UAuracronFoliageCollisionManager::GetAllCollisionMeshes() const
{
    FScopeLock Lock(&CollisionLock);

    TArray<FAuracronCollisionMeshData> AllMeshes;
    CollisionMeshes.GenerateValueArray(AllMeshes);
    return AllMeshes;
}

void UAuracronFoliageCollisionManager::ValidateConfiguration()
{
    // Validate collision settings
    Configuration.DefaultMass = FMath::Clamp(Configuration.DefaultMass, 0.1f, 1000.0f);
    Configuration.DefaultLinearDamping = FMath::Clamp(Configuration.DefaultLinearDamping, 0.0f, 1.0f);
    Configuration.DefaultAngularDamping = FMath::Clamp(Configuration.DefaultAngularDamping, 0.0f, 1.0f);
    Configuration.DefaultRestitution = FMath::Clamp(Configuration.DefaultRestitution, 0.0f, 1.0f);
    Configuration.DefaultFriction = FMath::Clamp(Configuration.DefaultFriction, 0.0f, 2.0f);

    // Validate destructible settings
    Configuration.DestructionThreshold = FMath::Max(1.0f, Configuration.DestructionThreshold);
    Configuration.DestructionImpulse = FMath::Max(1.0f, Configuration.DestructionImpulse);

    // Validate trampling settings
    Configuration.TramplingRadius = FMath::Clamp(Configuration.TramplingRadius, 10.0f, 1000.0f);
    Configuration.TramplingForce = FMath::Clamp(Configuration.TramplingForce, 1.0f, 1000.0f);
    Configuration.TramplingRecoveryTime = FMath::Max(0.1f, Configuration.TramplingRecoveryTime);

    // Validate performance settings
    Configuration.MaxCollisionUpdatesPerFrame = FMath::Max(1, Configuration.MaxCollisionUpdatesPerFrame);
    Configuration.CollisionUpdateInterval = FMath::Max(0.01f, Configuration.CollisionUpdateInterval);
    Configuration.MaxCollisionDistance = FMath::Max(100.0f, Configuration.MaxCollisionDistance);
    Configuration.CollisionDisableDistance = FMath::Max(100.0f, Configuration.CollisionDisableDistance);
}

FString UAuracronFoliageCollisionManager::GenerateCollisionMeshId() const
{
    return FString::Printf(TEXT("CollisionMesh_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageCollisionManager::GenerateDestructibleId() const
{
    return FString::Printf(TEXT("Destructible_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageCollisionManager::GenerateTramplingId() const
{
    return FString::Printf(TEXT("Trampling_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageCollisionManager::UpdateCollisionMeshInternal(FAuracronCollisionMeshData& MeshData, float DeltaTime)
{
    // Update collision mesh state based on distance and LOD
    if (Configuration.bDisableCollisionForDistantFoliage && ManagedWorld.IsValid())
    {
        // Get viewer location (simplified - in production would get from camera manager)
        FVector ViewerLocation = FVector::ZeroVector;
        if (APawn* ViewerPawn = ManagedWorld->GetFirstPlayerController()->GetPawn())
        {
            ViewerLocation = ViewerPawn->GetActorLocation();
        }

        // Calculate distance to collision mesh (simplified)
        float Distance = 1000.0f; // Placeholder - would calculate actual distance

        if (Distance > Configuration.CollisionDisableDistance)
        {
            // Disable collision for distant meshes
            // This would be implemented by modifying the actual collision components
        }
    }
}

void UAuracronFoliageCollisionManager::UpdateDestructibleFoliageInternal(FAuracronDestructibleFoliageData& DestructibleData, float DeltaTime)
{
    if (DestructibleData.bIsDestroyed && DestructibleData.bCanRegenerate)
    {
        // Handle regeneration
        float TimeSinceDestruction = (FDateTime::Now() - DestructibleData.DestructionTime).GetTotalSeconds();

        if (TimeSinceDestruction >= DestructibleData.RegenerationTime)
        {
            // Start regeneration process
            DestructibleData.Health += DestructibleData.RegenerationRate * DeltaTime;

            if (DestructibleData.Health >= DestructibleData.MaxHealth)
            {
                DestructibleData.Health = DestructibleData.MaxHealth;
                DestructibleData.bIsDestroyed = false;

                AURACRON_FOLIAGE_LOG_INFO(TEXT("Destructible foliage regenerated: %s"), *DestructibleData.DestructibleId);
            }
        }
    }
}

void UAuracronFoliageCollisionManager::UpdateTramplingEffectInternal(FAuracronTramplingEffectData& TramplingData, float DeltaTime)
{
    if (TramplingData.bIsActive && TramplingData.bAutoRecover)
    {
        // Handle recovery
        TramplingData.CurrentRecoveryProgress += TramplingData.RecoveryRate * DeltaTime;

        if (TramplingData.CurrentRecoveryProgress >= 1.0f)
        {
            // Recovery complete
            TramplingData.bIsActive = false;
            TramplingData.CurrentRecoveryProgress = 0.0f;

            AURACRON_FOLIAGE_LOG_INFO(TEXT("Trampling effect recovered: %s"), *TramplingData.TramplingId);
        }
    }
}

void UAuracronFoliageCollisionManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&CollisionLock);

    // Reset counters
    PerformanceData.TotalCollisionMeshes = CollisionMeshes.Num();
    PerformanceData.ActiveCollisionMeshes = 0;
    PerformanceData.DestructibleInstances = DestructibleFoliage.Num();
    PerformanceData.TramplingEffects = TramplingEffects.Num();

    // Count active collision meshes
    for (const auto& MeshPair : CollisionMeshes)
    {
        if (MeshPair.Value.bIsGenerated)
        {
            PerformanceData.ActiveCollisionMeshes++;
        }
    }

    // Calculate memory usage (simplified)
    PerformanceData.MemoryUsageMB = (CollisionMeshes.Num() * sizeof(FAuracronCollisionMeshData) +
                                    DestructibleFoliage.Num() * sizeof(FAuracronDestructibleFoliageData) +
                                    TramplingEffects.Num() * sizeof(FAuracronTramplingEffectData)) / (1024.0f * 1024.0f);
}

UStaticMesh* UAuracronFoliageCollisionManager::GenerateSimplifiedCollisionMesh(UStaticMesh* SourceMesh, float ComplexityLevel) const
{
    if (!SourceMesh)
    {
        return nullptr;
    }

    // In a production implementation, this would use UE5.6's mesh simplification tools
    // For now, we return the source mesh as a placeholder
    // Real implementation would use:
    // - FMeshDescription for mesh data access
    // - UE::Geometry::FDynamicMesh3 for mesh processing
    // - Mesh simplification algorithms from GeometryProcessing module

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Generating simplified collision mesh with complexity: %.2f"), ComplexityLevel);

    return SourceMesh; // Placeholder - would return simplified mesh in production
}

void UAuracronFoliageCollisionManager::ApplyPhysicsPropertiesInternal(UBodyInstance* BodyInstance, const FAuracronCollisionMeshData& CollisionData)
{
    if (!BodyInstance)
    {
        return;
    }

    // Apply physics properties
    BodyInstance->SetMassOverride(CollisionData.Mass, true);
    BodyInstance->LinearDamping = CollisionData.LinearDamping;
    BodyInstance->AngularDamping = CollisionData.AngularDamping;

    // Set collision response
    switch (CollisionData.CollisionType)
    {
        case EAuracronFoliageCollisionType::QueryOnly:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            break;

        case EAuracronFoliageCollisionType::PhysicsOnly:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::PhysicsOnly);
            break;

        case EAuracronFoliageCollisionType::CollisionEnabled:
        case EAuracronFoliageCollisionType::Destructible:
        case EAuracronFoliageCollisionType::Interactive:
        case EAuracronFoliageCollisionType::Trampling:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            break;

        default:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            break;
    }

    // Set collision channel
    BodyInstance->SetObjectType(Configuration.FoliageCollisionChannel);

    // Configure overlap events
    if (CollisionData.bGenerateOverlapEvents)
    {
        BodyInstance->bGenerateOverlapEvents = true;
    }
}

void UAuracronFoliageCollisionManager::CreateDestructionFragments(const FVector& Location, const FAuracronDestructibleFoliageData& DestructibleData)
{
    if (!ManagedWorld.IsValid())
    {
        return;
    }

    // Create destruction fragments
    for (int32 i = 0; i < DestructibleData.FragmentCount; ++i)
    {
        FVector FragmentLocation = Location + FMath::VRand() * DestructibleData.FragmentSpread;
        FVector FragmentVelocity = FMath::VRand() * DestructibleData.DestructionImpulse;

        // In production, this would spawn actual fragment actors/components
        // For now, we just log the creation
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Created destruction fragment %d at location: %s"), i, *FragmentLocation.ToString());
    }
}

void UAuracronFoliageCollisionManager::ApplyTramplingDeformation(UHierarchicalInstancedStaticMeshComponent* Component, int32 InstanceIndex, const FAuracronTramplingEffectData& TramplingData)
{
    if (!Component)
    {
        return;
    }

    // Get current instance transform
    FTransform InstanceTransform;
    if (Component->GetInstanceTransform(InstanceIndex, InstanceTransform, true))
    {
        // Apply trampling deformation based on effect type
        switch (TramplingData.TramplingEffect)
        {
            case EAuracronTramplingEffect::Bend:
                {
                    // Apply bending rotation
                    FRotator BendRotation(TramplingData.BendAngle, 0.0f, 0.0f);
                    InstanceTransform.SetRotation(InstanceTransform.GetRotation() * BendRotation.Quaternion());
                }
                break;

            case EAuracronTramplingEffect::Flatten:
                {
                    // Apply flattening scale
                    FVector Scale = InstanceTransform.GetScale3D();
                    Scale.Z *= TramplingData.FlattenAmount;
                    InstanceTransform.SetScale3D(Scale);
                }
                break;

            case EAuracronTramplingEffect::Crush:
                {
                    // Apply crushing scale
                    FVector Scale = InstanceTransform.GetScale3D() * 0.5f;
                    InstanceTransform.SetScale3D(Scale);
                }
                break;

            default:
                break;
        }

        // Update instance transform
        Component->UpdateInstanceTransform(InstanceIndex, InstanceTransform, true, true);
    }
}
