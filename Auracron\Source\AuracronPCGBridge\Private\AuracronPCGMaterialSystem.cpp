// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Material System Implementation
// Bridge 2.11: PCG Framework - Material Assignment

#include "AuracronPCGMaterialSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Async/ParallelFor.h"

// =============================================================================
// ADVANCED MATERIAL SELECTOR IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedMaterialSelectorSettings::UAuracronPCGAdvancedMaterialSelectorSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Material Selector");
    NodeMetadata.NodeDescription = TEXT("Selects materials based on various criteria and attributes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Material"));
    NodeMetadata.Tags.Add(TEXT("Selection"));
    NodeMetadata.Tags.Add(TEXT("Assignment"));
    NodeMetadata.Tags.Add(TEXT("Attributes"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.4f, 0.2f);
}

void UAuracronPCGAdvancedMaterialSelectorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseMultipleCriteria)
    {
        FPCGPinProperties& CriteriaPin = InputPins.Emplace_GetRef();
        CriteriaPin.Label = TEXT("Additional Criteria");
        CriteriaPin.AllowedTypes = EPCGDataType::Attribute;
        CriteriaPin.bAdvancedPin = true;
    }
}

void UAuracronPCGAdvancedMaterialSelectorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputSelectionInfo)
    {
        FPCGPinProperties& SelectionPin = OutputPins.Emplace_GetRef();
        SelectionPin.Label = TEXT("Selection Info");
        SelectionPin.AllowedTypes = EPCGDataType::Attribute;
        SelectionPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGAdvancedMaterialSelectorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                   FPCGDataCollection& OutputData, 
                                                                                   const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedMaterialSelectorSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedMaterialSelectorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Material Selector");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 MaterialsAssigned = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Select materials for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Select material based on criteria
                UMaterialInterface* SelectedMaterial = SelectMaterialForPoint(OutputPoint, Settings);
                
                if (SelectedMaterial)
                {
                    // Apply material to point (simplified - in production you'd use proper material assignment)
                    ApplyMaterialToPoint(OutputPoint, SelectedMaterial, Settings);
                    MaterialsAssigned++;
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Material Selector processed %d points, assigned %d materials"), 
                                  TotalProcessed, MaterialsAssigned);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Material Selector error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

UMaterialInterface* FAuracronPCGAdvancedMaterialSelectorElement::SelectMaterialForPoint(const FPCGPoint& Point, const UAuracronPCGAdvancedMaterialSelectorSettings* Settings) const
{
    // Use utility function for material selection
    UMaterialInterface* SelectedMaterial = UAuracronPCGMaterialSystemUtils::SelectMaterial(Point, Settings->SelectionDescriptor);
    
    // Apply material variation if enabled
    if (Settings->bEnableMaterialVariation && SelectedMaterial)
    {
        SelectedMaterial = ApplyMaterialVariation(SelectedMaterial, Point, Settings);
    }

    // Handle multiple criteria if enabled
    if (Settings->bUseMultipleCriteria && Settings->AdditionalCriteria.Num() > 0)
    {
        TArray<UMaterialInterface*> CandidateMaterials = UAuracronPCGMaterialSystemUtils::SelectMultipleMaterials(
            Point, Settings->AdditionalCriteria, Settings->CriteriaWeights);
        
        if (CandidateMaterials.Num() > 0)
        {
            // Select best candidate based on combined criteria
            SelectedMaterial = SelectBestMaterialCandidate(CandidateMaterials, Point, Settings);
        }
    }

    // Fallback to default material if no selection
    if (!SelectedMaterial && Settings->SelectionDescriptor.FallbackMaterial.IsValid())
    {
        SelectedMaterial = AuracronPCGMaterialSystemUtils::LoadMaterialSafe(Settings->SelectionDescriptor.FallbackMaterial);
    }

    return SelectedMaterial;
}

UMaterialInterface* FAuracronPCGAdvancedMaterialSelectorElement::ApplyMaterialVariation(UMaterialInterface* BaseMaterial, const FPCGPoint& Point, const UAuracronPCGAdvancedMaterialSelectorSettings* Settings) const
{
    if (!BaseMaterial)
    {
        return nullptr;
    }

    // Create dynamic material instance for variation
    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, nullptr);
    if (!DynamicMaterial)
    {
        return BaseMaterial;
    }

    // Apply variation based on point position and settings
    FVector Position = Point.Transform.GetLocation();
    FRandomStream VariationStream(Settings->VariationSeed + FMath::FloorToInt(Position.X + Position.Y + Position.Z));
    
    // Vary material parameters
    float VariationFactor = VariationStream.FRandRange(-Settings->VariationStrength, Settings->VariationStrength);
    
    // Apply variation to common material parameters (simplified)
    DynamicMaterial->SetScalarParameterValue(TEXT("Variation"), VariationFactor);
    DynamicMaterial->SetVectorParameterValue(TEXT("VariationColor"), 
        FLinearColor(1.0f + VariationFactor, 1.0f + VariationFactor, 1.0f + VariationFactor, 1.0f));

    return DynamicMaterial;
}

UMaterialInterface* FAuracronPCGAdvancedMaterialSelectorElement::SelectBestMaterialCandidate(const TArray<UMaterialInterface*>& Candidates, const FPCGPoint& Point, const UAuracronPCGAdvancedMaterialSelectorSettings* Settings) const
{
    if (Candidates.Num() == 0)
    {
        return nullptr;
    }

    if (Candidates.Num() == 1)
    {
        return Candidates[0];
    }

    // Score each candidate based on multiple criteria
    float BestScore = -1.0f;
    UMaterialInterface* BestMaterial = nullptr;

    for (int32 i = 0; i < Candidates.Num(); i++)
    {
        float Score = 0.0f;
        
        // Calculate score based on criteria weights
        for (int32 j = 0; j < Settings->AdditionalCriteria.Num() && j < Settings->CriteriaWeights.Num(); j++)
        {
            float CriteriaScore = AuracronPCGMaterialSystemUtils::EvaluateSelectionCriteria(Point, Settings->AdditionalCriteria[j]);
            Score += CriteriaScore * Settings->CriteriaWeights[j];
        }

        if (Score > BestScore)
        {
            BestScore = Score;
            BestMaterial = Candidates[i];
        }
    }

    return BestMaterial;
}

void FAuracronPCGAdvancedMaterialSelectorElement::ApplyMaterialToPoint(FPCGPoint& Point, UMaterialInterface* Material, const UAuracronPCGAdvancedMaterialSelectorSettings* Settings) const
{
    // Simplified material application - in production you'd use proper material assignment
    // Set material index as attribute
    if (Settings->SelectionDescriptor.MaterialOptions.Num() > 0)
    {
        for (int32 i = 0; i < Settings->SelectionDescriptor.MaterialOptions.Num(); i++)
        {
            if (Settings->SelectionDescriptor.MaterialOptions[i].Get() == Material)
            {
                // Set material index (simplified)
                Point.SetLocalBounds(FBox(FVector(-i), FVector(i)));
                break;
            }
        }
    }

    // Set material name as color for visualization
    if (Material)
    {
        FString MaterialName = Material->GetName();
        uint32 NameHash = GetTypeHash(MaterialName);
        float R = ((NameHash >> 16) & 0xFF) / 255.0f;
        float G = ((NameHash >> 8) & 0xFF) / 255.0f;
        float B = (NameHash & 0xFF) / 255.0f;
        Point.Color = FVector4(R, G, B, 1.0f);
    }
}

// =============================================================================
// MATERIAL BLENDER IMPLEMENTATION
// =============================================================================

UAuracronPCGMaterialBlenderSettings::UAuracronPCGMaterialBlenderSettings()
{
    NodeMetadata.NodeName = TEXT("Material Blender");
    NodeMetadata.NodeDescription = TEXT("Blends multiple materials using various blending modes and masks");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Material"));
    NodeMetadata.Tags.Add(TEXT("Blending"));
    NodeMetadata.Tags.Add(TEXT("Mixing"));
    NodeMetadata.Tags.Add(TEXT("Layering"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.8f, 0.2f);
}

void UAuracronPCGMaterialBlenderSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseAttributeAsMask)
    {
        FPCGPinProperties& MaskPin = InputPins.Emplace_GetRef();
        MaskPin.Label = TEXT("Blend Mask");
        MaskPin.AllowedTypes = EPCGDataType::Attribute;
        MaskPin.bAdvancedPin = true;
    }
}

void UAuracronPCGMaterialBlenderSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputBlendWeights)
    {
        FPCGPinProperties& WeightsPin = OutputPins.Emplace_GetRef();
        WeightsPin.Label = TEXT("Blend Weights");
        WeightsPin.AllowedTypes = EPCGDataType::Attribute;
        WeightsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGMaterialBlenderElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                          FPCGDataCollection& OutputData, 
                                                                          const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGMaterialBlenderSettings* Settings = GetTypedSettings<UAuracronPCGMaterialBlenderSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Material Blender");
            return Result;
        }

        if (Settings->SourceMaterials.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No source materials provided for blending");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 MaterialsBlended = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Blend materials for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Calculate blend weights for this point
                TArray<float> BlendWeights = CalculateBlendWeights(OutputPoint, Settings);
                
                // Blend materials
                UMaterialInstanceDynamic* BlendedMaterial = BlendMaterialsForPoint(OutputPoint, BlendWeights, Settings);
                
                if (BlendedMaterial)
                {
                    ApplyBlendedMaterialToPoint(OutputPoint, BlendedMaterial, BlendWeights, Settings);
                    MaterialsBlended++;
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Material Blender processed %d points, blended %d materials"), 
                                  TotalProcessed, MaterialsBlended);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Material Blender error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

TArray<float> FAuracronPCGMaterialBlenderElement::CalculateBlendWeights(const FPCGPoint& Point, const UAuracronPCGMaterialBlenderSettings* Settings) const
{
    TArray<float> BlendWeights;
    BlendWeights.Reserve(Settings->SourceMaterials.Num());

    // Start with material weights from settings
    for (int32 i = 0; i < Settings->SourceMaterials.Num(); i++)
    {
        float Weight = (i < Settings->MaterialWeights.Num()) ? Settings->MaterialWeights[i] : 1.0f;
        BlendWeights.Add(Weight);
    }

    // Apply blend mask if enabled
    if (Settings->bUseAttributeAsMask)
    {
        float MaskValue = UAuracronPCGMaterialSystemUtils::GetAttributeAsFloat(Point, Settings->MaskAttributeName, 1.0f);
        
        for (float& Weight : BlendWeights)
        {
            Weight *= MaskValue;
        }
    }

    // Apply procedural mask if enabled
    if (Settings->bUseProceduralMask)
    {
        FVector Position = Point.Transform.GetLocation();
        FRandomStream NoiseStream(Settings->MaskNoiseSeed);
        
        // Simple noise-based mask (in production you'd use proper noise functions)
        float NoiseValue = NoiseStream.FRandRange(0.0f, 1.0f);
        
        for (float& Weight : BlendWeights)
        {
            Weight *= NoiseValue;
        }
    }

    // Normalize weights if requested
    if (Settings->BlendingDescriptor.bNormalizeBlendWeights)
    {
        float TotalWeight = 0.0f;
        for (float Weight : BlendWeights)
        {
            TotalWeight += Weight;
        }
        
        if (TotalWeight > 0.0f)
        {
            for (float& Weight : BlendWeights)
            {
                Weight /= TotalWeight;
            }
        }
    }

    return BlendWeights;
}

UMaterialInstanceDynamic* FAuracronPCGMaterialBlenderElement::BlendMaterialsForPoint(const FPCGPoint& Point, const TArray<float>& BlendWeights, const UAuracronPCGMaterialBlenderSettings* Settings) const
{
    if (Settings->SourceMaterials.Num() == 0 || BlendWeights.Num() == 0)
    {
        return nullptr;
    }

    // Load source materials
    TArray<UMaterialInterface*> LoadedMaterials;
    for (const TSoftObjectPtr<UMaterialInterface>& MaterialPtr : Settings->SourceMaterials)
    {
        UMaterialInterface* Material = AuracronPCGMaterialSystemUtils::LoadMaterialSafe(MaterialPtr);
        LoadedMaterials.Add(Material);
    }

    // Use utility function for blending
    return UAuracronPCGMaterialSystemUtils::BlendMaterials(LoadedMaterials, BlendWeights, Settings->BlendingDescriptor);
}

void FAuracronPCGMaterialBlenderElement::ApplyBlendedMaterialToPoint(FPCGPoint& Point, UMaterialInstanceDynamic* BlendedMaterial, const TArray<float>& BlendWeights, const UAuracronPCGMaterialBlenderSettings* Settings) const
{
    // Simplified material application - in production you'd use proper material assignment
    if (BlendedMaterial)
    {
        // Set blend strength as density
        float TotalWeight = 0.0f;
        for (float Weight : BlendWeights)
        {
            TotalWeight += Weight;
        }
        Point.Density = FMath::Clamp(TotalWeight, 0.0f, 1.0f);
        
        // Set blend visualization as color
        if (BlendWeights.Num() >= 3)
        {
            Point.Color = FVector4(
                FMath::Clamp(BlendWeights[0], 0.0f, 1.0f),
                FMath::Clamp(BlendWeights[1], 0.0f, 1.0f),
                FMath::Clamp(BlendWeights[2], 0.0f, 1.0f),
                1.0f
            );
        }
    }
}

// =============================================================================
// UV COORDINATE GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGUVCoordinateGeneratorSettings::UAuracronPCGUVCoordinateGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("UV Coordinate Generator");
    NodeMetadata.NodeDescription = TEXT("Generates UV coordinates using various projection methods");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("UV"));
    NodeMetadata.Tags.Add(TEXT("Coordinates"));
    NodeMetadata.Tags.Add(TEXT("Projection"));
    NodeMetadata.Tags.Add(TEXT("Mapping"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.6f, 0.8f);
}

void UAuracronPCGUVCoordinateGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGUVCoordinateGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputUVAttributes)
    {
        FPCGPinProperties& UVPin = OutputPins.Emplace_GetRef();
        UVPin.Label = TEXT("UV Coordinates");
        UVPin.AllowedTypes = EPCGDataType::Attribute;
        UVPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGUVCoordinateGeneratorElement::ProcessData(const FPCGDataCollection& InputData,
                                                                                FPCGDataCollection& OutputData,
                                                                                const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGUVCoordinateGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGUVCoordinateGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for UV Coordinate Generator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 UVsGenerated = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Generate UV coordinates for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Generate primary UV coordinates
                FVector2D PrimaryUV = GenerateUVForPoint(OutputPoint, Settings->UVDescriptor);

                // Apply UV transformations
                PrimaryUV = ApplyUVTransformations(PrimaryUV, Settings->UVDescriptor);

                // Apply UV to point
                ApplyUVToPoint(OutputPoint, PrimaryUV, Settings);

                // Generate additional UV channels if requested
                if (Settings->bGenerateMultipleChannels)
                {
                    GenerateAdditionalUVChannels(OutputPoint, Settings);
                }

                OutputPoints[i] = OutputPoint;
                UVsGenerated++;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("UV Coordinate Generator processed %d points, generated %d UV coordinates"),
                                  TotalProcessed, UVsGenerated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("UV Coordinate Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

FVector2D FAuracronPCGUVCoordinateGeneratorElement::GenerateUVForPoint(const FPCGPoint& Point, const FAuracronPCGUVGenerationDescriptor& UVDescriptor) const
{
    FVector Position = Point.Transform.GetLocation();

    // Use utility function for UV generation
    return UAuracronPCGMaterialSystemUtils::GenerateUVCoordinates(Position, UVDescriptor);
}

FVector2D FAuracronPCGUVCoordinateGeneratorElement::ApplyUVTransformations(const FVector2D& UV, const FAuracronPCGUVGenerationDescriptor& UVDescriptor) const
{
    FVector2D TransformedUV = UV;

    // Apply transformations using utility functions
    TransformedUV = AuracronPCGMaterialSystemUtils::ApplyUVTransform(
        TransformedUV, UVDescriptor.UVScale, UVDescriptor.UVOffset, UVDescriptor.UVRotation);

    // Apply wrapping
    if (UVDescriptor.bWrapUVs)
    {
        TransformedUV = AuracronPCGMaterialSystemUtils::WrapUVCoordinates(TransformedUV, true, true);
    }

    // Apply flipping
    if (UVDescriptor.bFlipU || UVDescriptor.bFlipV)
    {
        TransformedUV = AuracronPCGMaterialSystemUtils::FlipUVCoordinates(TransformedUV, UVDescriptor.bFlipU, UVDescriptor.bFlipV);
    }

    return TransformedUV;
}

void FAuracronPCGUVCoordinateGeneratorElement::ApplyUVToPoint(FPCGPoint& Point, const FVector2D& UV, const UAuracronPCGUVCoordinateGeneratorSettings* Settings) const
{
    // Simplified UV application - in production you'd use proper UV channel assignment
    // Set UV as color for visualization
    Point.Color = FVector4(UV.X, UV.Y, 0.0f, 1.0f);

    // Set UV magnitude as density
    Point.Density = FMath::Clamp(UV.Size(), 0.0f, 1.0f);
}

void FAuracronPCGUVCoordinateGeneratorElement::GenerateAdditionalUVChannels(FPCGPoint& Point, const UAuracronPCGUVCoordinateGeneratorSettings* Settings) const
{
    // Generate additional UV channels
    for (const FAuracronPCGUVGenerationDescriptor& AdditionalDescriptor : Settings->AdditionalChannels)
    {
        FVector2D AdditionalUV = GenerateUVForPoint(Point, AdditionalDescriptor);
        AdditionalUV = ApplyUVTransformations(AdditionalUV, AdditionalDescriptor);

        // Store additional UV (simplified - in production you'd use proper multi-channel storage)
        // For now, just blend with existing color
        Point.Color.X = FMath::Lerp(Point.Color.X, AdditionalUV.X, 0.5f);
        Point.Color.Y = FMath::Lerp(Point.Color.Y, AdditionalUV.Y, 0.5f);
    }
}

// =============================================================================
// MATERIAL PARAMETER CONTROLLER IMPLEMENTATION
// =============================================================================

UAuracronPCGMaterialParameterControllerSettings::UAuracronPCGMaterialParameterControllerSettings()
{
    NodeMetadata.NodeName = TEXT("Material Parameter Controller");
    NodeMetadata.NodeDescription = TEXT("Controls dynamic material parameters based on point attributes");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Material"));
    NodeMetadata.Tags.Add(TEXT("Parameters"));
    NodeMetadata.Tags.Add(TEXT("Dynamic"));
    NodeMetadata.Tags.Add(TEXT("Control"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.6f);
}

void UAuracronPCGMaterialParameterControllerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (bUseParameterCurves)
    {
        FPCGPinProperties& CurvesPin = InputPins.Emplace_GetRef();
        CurvesPin.Label = TEXT("Parameter Curves");
        CurvesPin.AllowedTypes = EPCGDataType::Attribute;
        CurvesPin.bAdvancedPin = true;
    }
}

void UAuracronPCGMaterialParameterControllerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputParameterValues)
    {
        FPCGPinProperties& ParametersPin = OutputPins.Emplace_GetRef();
        ParametersPin.Label = TEXT("Parameter Values");
        ParametersPin.AllowedTypes = EPCGDataType::Attribute;
        ParametersPin.bAdvancedPin = true;
    }

    if (bOutputMaterialInstances)
    {
        FPCGPinProperties& MaterialsPin = OutputPins.Emplace_GetRef();
        MaterialsPin.Label = TEXT("Material Instances");
        MaterialsPin.AllowedTypes = EPCGDataType::Attribute;
        MaterialsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGMaterialParameterControllerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                                      FPCGDataCollection& OutputData,
                                                                                      const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGMaterialParameterControllerSettings* Settings = GetTypedSettings<UAuracronPCGMaterialParameterControllerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Material Parameter Controller");
            return Result;
        }

        if (Settings->ParameterDescriptors.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No parameter descriptors provided");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 ParametersControlled = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Control material parameters for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Create or get material instance for this point
                UMaterialInstanceDynamic* MaterialInstance = CreateMaterialInstanceForPoint(OutputPoint, Settings);

                if (MaterialInstance)
                {
                    // Apply parameter controls
                    ApplyParameterControls(MaterialInstance, OutputPoint, Settings);
                    ParametersControlled++;
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Material Parameter Controller processed %d points, controlled %d parameter sets"),
                                  TotalProcessed, ParametersControlled);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Material Parameter Controller error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

UMaterialInstanceDynamic* FAuracronPCGMaterialParameterControllerElement::CreateMaterialInstanceForPoint(const FPCGPoint& Point, const UAuracronPCGMaterialParameterControllerSettings* Settings) const
{
    if (!Settings->TargetMaterial.IsValid())
    {
        return nullptr;
    }

    UMaterialInterface* BaseMaterial = AuracronPCGMaterialSystemUtils::LoadMaterialSafe(Settings->TargetMaterial);
    if (!BaseMaterial)
    {
        return nullptr;
    }

    if (Settings->bCreateMaterialInstances)
    {
        // Use utility function to create dynamic material instance
        return UAuracronPCGMaterialSystemUtils::CreateDynamicMaterialInstance(BaseMaterial, Settings->ParameterDescriptors);
    }

    return nullptr;
}

void FAuracronPCGMaterialParameterControllerElement::ApplyParameterControls(UMaterialInstanceDynamic* MaterialInstance, const FPCGPoint& Point, const UAuracronPCGMaterialParameterControllerSettings* Settings) const
{
    if (!MaterialInstance)
    {
        return;
    }

    // Apply each parameter descriptor
    for (const FAuracronPCGMaterialParameterDescriptor& ParameterDescriptor : Settings->ParameterDescriptors)
    {
        UAuracronPCGMaterialSystemUtils::SetMaterialParameter(MaterialInstance, ParameterDescriptor, Point);
    }

    // Apply parameter animation if enabled
    if (Settings->bEnableParameterAnimation)
    {
        ApplyParameterAnimation(MaterialInstance, Point, Settings);
    }
}

void FAuracronPCGMaterialParameterControllerElement::ApplyParameterAnimation(UMaterialInstanceDynamic* MaterialInstance, const FPCGPoint& Point, const UAuracronPCGMaterialParameterControllerSettings* Settings) const
{
    if (!MaterialInstance)
    {
        return;
    }

    // Simple time-based animation (in production you'd use proper time management)
    float AnimationTime = FPlatformTime::Seconds() * Settings->AnimationSpeed;

    if (Settings->bLoopAnimation)
    {
        AnimationTime = FMath::Fmod(AnimationTime, 1.0f);
    }

    // Apply animation to scalar parameters
    MaterialInstance->SetScalarParameterValue(TEXT("AnimationTime"), AnimationTime);
    MaterialInstance->SetScalarParameterValue(TEXT("AnimationPhase"), FMath::Sin(AnimationTime * 2.0f * PI));
}
