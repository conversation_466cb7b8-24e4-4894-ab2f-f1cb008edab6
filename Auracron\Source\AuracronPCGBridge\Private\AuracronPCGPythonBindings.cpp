// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Python Bindings Implementation
// Bridge 2.15: PCG Framework - Python Integration

#include "AuracronPCGPythonBindings.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformFilemanager.h"

// =============================================================================
// PYTHON BINDING MANAGER IMPLEMENTATION
// =============================================================================

UAuracronPCGPythonBindingManager* UAuracronPCGPythonBindingManager::Instance = nullptr;

UAuracronPCGPythonBindingManager* UAuracronPCGPythonBindingManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGPythonBindingManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

bool UAuracronPCGPythonBindingManager::InitializePythonBindings(const FAuracronPCGPythonBindingDescriptor& Descriptor)
{
    if (bIsInitialized)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Python bindings already initialized"));
        return true;
    }

    CurrentDescriptor = Descriptor;

    if (!ValidateDescriptor(Descriptor))
    {
        AddError(TEXT("Invalid binding descriptor"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Initialize Python interpreter if not already done
        if (!Py_IsInitialized())
        {
            pybind11::initialize_interpreter();
        }

        // Create the main PCG module
        PCGModule = pybind11::module_::create_extension_module(
            TCHAR_TO_UTF8(*Descriptor.ModuleName),
            TCHAR_TO_UTF8(*Descriptor.ModuleDescription),
            new pybind11::module_::module_def
        );

        // Bind classes based on category
        switch (Descriptor.Category)
        {
            case EAuracronPCGPythonBindingCategory::Core:
                BindCoreClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Nodes:
                BindNodeClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Data:
                BindDataClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Graph:
                BindGraphClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Execution:
                BindExecutionClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Debug:
                BindDebugClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Custom:
                BindCustomClasses();
                break;
            case EAuracronPCGPythonBindingCategory::Utilities:
                BindUtilityClasses();
                break;
            case EAuracronPCGPythonBindingCategory::All:
                BindCoreClasses();
                BindNodeClasses();
                BindDataClasses();
                BindGraphClasses();
                BindExecutionClasses();
                BindDebugClasses();
                BindCustomClasses();
                BindUtilityClasses();
                break;
        }

        bIsInitialized = true;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python bindings initialized successfully for module: %s"), *Descriptor.ModuleName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to initialize Python bindings: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("%s"), *ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not compiled in"));
    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Python support not compiled in"));
    return false;
#endif
}

void UAuracronPCGPythonBindingManager::ShutdownPythonBindings()
{
    if (!bIsInitialized)
    {
        return;
    }

#ifdef WITH_PYTHON
    try
    {
        // Clean up modules
        RegisteredModules.Empty();
        
        // Note: We don't finalize the interpreter here as it might be used by other systems
        bIsInitialized = false;
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python bindings shutdown successfully"));
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Error during Python bindings shutdown: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("%s"), *ErrorMsg);
    }
#endif
}

bool UAuracronPCGPythonBindingManager::IsInitialized() const
{
    return bIsInitialized;
}

bool UAuracronPCGPythonBindingManager::RegisterModule(const FString& ModuleName, const FAuracronPCGPythonBindingDescriptor& Descriptor)
{
    if (RegisteredModules.Contains(ModuleName))
    {
        AddError(FString::Printf(TEXT("Module '%s' is already registered"), *ModuleName));
        return false;
    }

    if (!ValidateDescriptor(Descriptor))
    {
        AddError(FString::Printf(TEXT("Invalid descriptor for module '%s'"), *ModuleName));
        return false;
    }

    RegisteredModules.Add(ModuleName, Descriptor);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Module '%s' registered successfully"), *ModuleName);
    return true;
}

bool UAuracronPCGPythonBindingManager::UnregisterModule(const FString& ModuleName)
{
    if (!RegisteredModules.Contains(ModuleName))
    {
        AddError(FString::Printf(TEXT("Module '%s' is not registered"), *ModuleName));
        return false;
    }

    RegisteredModules.Remove(ModuleName);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Module '%s' unregistered successfully"), *ModuleName);
    return true;
}

TArray<FString> UAuracronPCGPythonBindingManager::GetRegisteredModules() const
{
    TArray<FString> ModuleNames;
    RegisteredModules.GetKeys(ModuleNames);
    return ModuleNames;
}

bool UAuracronPCGPythonBindingManager::IsModuleRegistered(const FString& ModuleName) const
{
    return RegisteredModules.Contains(ModuleName);
}

bool UAuracronPCGPythonBindingManager::BindClass(const FString& ClassName, UClass* Class)
{
    if (!Class)
    {
        AddError(FString::Printf(TEXT("Cannot bind null class '%s'"), *ClassName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Simplified class binding - in production you'd use full pybind11 class binding
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Class '%s' bound successfully"), *ClassName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind class '%s': %s"), *ClassName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::BindFunction(const FString& FunctionName, UFunction* Function)
{
    if (!Function)
    {
        AddError(FString::Printf(TEXT("Cannot bind null function '%s'"), *FunctionName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Simplified function binding - in production you'd use full pybind11 function binding
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Function '%s' bound successfully"), *FunctionName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind function '%s': %s"), *FunctionName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::BindProperty(const FString& PropertyName, FProperty* Property)
{
    if (!Property)
    {
        AddError(FString::Printf(TEXT("Cannot bind null property '%s'"), *PropertyName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Simplified property binding - in production you'd use full pybind11 property binding
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Property '%s' bound successfully"), *PropertyName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind property '%s': %s"), *PropertyName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::BindEnum(const FString& EnumName, UEnum* Enum)
{
    if (!Enum)
    {
        AddError(FString::Printf(TEXT("Cannot bind null enum '%s'"), *EnumName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Simplified enum binding - in production you'd use full pybind11 enum binding
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Enum '%s' bound successfully"), *EnumName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind enum '%s': %s"), *EnumName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::BindStruct(const FString& StructName, UScriptStruct* Struct)
{
    if (!Struct)
    {
        AddError(FString::Printf(TEXT("Cannot bind null struct '%s'"), *StructName));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        // Simplified struct binding - in production you'd use full pybind11 struct binding
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Struct '%s' bound successfully"), *StructName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to bind struct '%s': %s"), *StructName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

FAuracronPCGPythonExecutionResult UAuracronPCGPythonBindingManager::ExecutePythonCode(const FString& Code)
{
    FAuracronPCGPythonExecutionResult Result;
    
    if (!bIsInitialized)
    {
        Result.ErrorMessage = TEXT("Python bindings not initialized");
        return Result;
    }

#ifdef WITH_PYTHON
    try
    {
        double StartTime = FPlatformTime::Seconds();
        
        // Execute Python code
        pybind11::exec(TCHAR_TO_UTF8(*Code));
        
        double EndTime = FPlatformTime::Seconds();
        
        Result.bSuccess = true;
        Result.ExecutionTime = static_cast<float>(EndTime - StartTime);
        Result.LinesExecuted = Code.ParseIntoArray(Result.Output, TEXT("\n"), true);
        Result.Output = TEXT("Code executed successfully");
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python code executed successfully in %.3f seconds"), Result.ExecutionTime);
    }
    catch (const std::exception& e)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Python execution error: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(Result.ErrorMessage);
    }
#else
    Result.bSuccess = false;
    Result.ErrorMessage = TEXT("Python support not available");
#endif

    return Result;
}

FAuracronPCGPythonExecutionResult UAuracronPCGPythonBindingManager::ExecutePythonFile(const FString& FilePath)
{
    FAuracronPCGPythonExecutionResult Result;
    
    FString FileContent;
    if (!FFileHelper::LoadFileToString(FileContent, *FilePath))
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Failed to load Python file: %s"), *FilePath);
        return Result;
    }

    return ExecutePythonCode(FileContent);
}

FAuracronPCGPythonExecutionResult UAuracronPCGPythonBindingManager::ExecutePythonFunction(const FString& ModuleName, const FString& FunctionName, const TArray<FString>& Arguments)
{
    FAuracronPCGPythonExecutionResult Result;
    
    if (!bIsInitialized)
    {
        Result.ErrorMessage = TEXT("Python bindings not initialized");
        return Result;
    }

#ifdef WITH_PYTHON
    try
    {
        double StartTime = FPlatformTime::Seconds();
        
        // Import module and call function
        pybind11::module_ module = pybind11::module_::import(TCHAR_TO_UTF8(*ModuleName));
        pybind11::object function = module.attr(TCHAR_TO_UTF8(*FunctionName));
        
        // Convert arguments and call function
        pybind11::list args;
        for (const FString& Arg : Arguments)
        {
            args.append(TCHAR_TO_UTF8(*Arg));
        }
        
        pybind11::object result = function(*args);
        
        double EndTime = FPlatformTime::Seconds();
        
        Result.bSuccess = true;
        Result.ExecutionTime = static_cast<float>(EndTime - StartTime);
        Result.Output = TEXT("Function executed successfully");
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python function '%s.%s' executed successfully"), *ModuleName, *FunctionName);
    }
    catch (const std::exception& e)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = FString::Printf(TEXT("Python function execution error: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(Result.ErrorMessage);
    }
#else
    Result.bSuccess = false;
    Result.ErrorMessage = TEXT("Python support not available");
#endif

    return Result;
}

bool UAuracronPCGPythonBindingManager::ImportPythonModule(const FString& ModuleName)
{
    if (!bIsInitialized)
    {
        AddError(TEXT("Python bindings not initialized"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        pybind11::module_::import(TCHAR_TO_UTF8(*ModuleName));
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python module '%s' imported successfully"), *ModuleName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to import Python module '%s': %s"), *ModuleName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

bool UAuracronPCGPythonBindingManager::ReloadPythonModule(const FString& ModuleName)
{
    if (!bIsInitialized)
    {
        AddError(TEXT("Python bindings not initialized"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ importlib = pybind11::module_::import("importlib");
        pybind11::module_ module = pybind11::module_::import(TCHAR_TO_UTF8(*ModuleName));
        importlib.attr("reload")(module);
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Python module '%s' reloaded successfully"), *ModuleName);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to reload Python module '%s': %s"), *ModuleName, UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

TArray<FString> UAuracronPCGPythonBindingManager::GetPythonPath() const
{
    TArray<FString> PythonPath;
    
#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ sys = pybind11::module_::import("sys");
        pybind11::list path = sys.attr("path");
        
        for (const auto& item : path)
        {
            PythonPath.Add(UTF8_TO_TCHAR(item.cast<std::string>().c_str()));
        }
    }
    catch (const std::exception& e)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to get Python path: %s"), UTF8_TO_TCHAR(e.what()));
    }
#endif

    return PythonPath;
}

bool UAuracronPCGPythonBindingManager::AddToPythonPath(const FString& Path)
{
    if (!bIsInitialized)
    {
        AddError(TEXT("Python bindings not initialized"));
        return false;
    }

#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ sys = pybind11::module_::import("sys");
        pybind11::list path = sys.attr("path");
        path.append(TCHAR_TO_UTF8(*Path));
        
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Added '%s' to Python path"), *Path);
        return true;
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Failed to add to Python path: %s"), UTF8_TO_TCHAR(e.what()));
        AddError(ErrorMsg);
        return false;
    }
#else
    AddError(TEXT("Python support not available"));
    return false;
#endif
}

FString UAuracronPCGPythonBindingManager::GetPythonVersion() const
{
#ifdef WITH_PYTHON
    try
    {
        pybind11::module_ sys = pybind11::module_::import("sys");
        pybind11::str version = sys.attr("version");
        return UTF8_TO_TCHAR(version.cast<std::string>().c_str());
    }
    catch (const std::exception& e)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Failed to get Python version: %s"), UTF8_TO_TCHAR(e.what()));
        return TEXT("Unknown");
    }
#else
    return TEXT("Python support not available");
#endif
}

void UAuracronPCGPythonBindingManager::SetBindingDescriptor(const FAuracronPCGPythonBindingDescriptor& Descriptor)
{
    CurrentDescriptor = Descriptor;
}

FAuracronPCGPythonBindingDescriptor UAuracronPCGPythonBindingManager::GetBindingDescriptor() const
{
    return CurrentDescriptor;
}

TArray<FString> UAuracronPCGPythonBindingManager::GetLastErrors() const
{
    return LastErrors;
}

void UAuracronPCGPythonBindingManager::ClearErrors()
{
    LastErrors.Empty();
}

bool UAuracronPCGPythonBindingManager::HasErrors() const
{
    return LastErrors.Num() > 0;
}

// Private helper functions
void UAuracronPCGPythonBindingManager::AddError(const FString& Error)
{
    LastErrors.Add(Error);
    AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Python Binding Error: %s"), *Error);
}

bool UAuracronPCGPythonBindingManager::ValidateDescriptor(const FAuracronPCGPythonBindingDescriptor& Descriptor)
{
    if (Descriptor.ModuleName.IsEmpty())
    {
        AddError(TEXT("Module name cannot be empty"));
        return false;
    }

    if (Descriptor.ModuleVersion.IsEmpty())
    {
        AddError(TEXT("Module version cannot be empty"));
        return false;
    }

    return true;
}

// Binding implementation functions
void UAuracronPCGPythonBindingManager::BindCoreClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind core PCG classes
        pybind11::class_<UPCGSettings>(PCGModule, "PCGSettings")
            .def("get_class_name", [](const UPCGSettings& self) {
                return TCHAR_TO_UTF8(*self.GetClass()->GetName());
            });

        pybind11::class_<UPCGData>(PCGModule, "PCGData")
            .def("get_class_name", [](const UPCGData& self) {
                return TCHAR_TO_UTF8(*self.GetClass()->GetName());
            });

        pybind11::class_<UPCGPointData, UPCGData>(PCGModule, "PCGPointData")
            .def("get_points_count", [](const UPCGPointData& self) {
                return self.GetPoints().Num();
            });

        pybind11::class_<UPCGSpatialData, UPCGData>(PCGModule, "PCGSpatialData")
            .def("get_bounds", [](const UPCGSpatialData& self) {
                FBox Bounds = self.GetBounds();
                return std::make_tuple(
                    std::make_tuple(Bounds.Min.X, Bounds.Min.Y, Bounds.Min.Z),
                    std::make_tuple(Bounds.Max.X, Bounds.Max.Y, Bounds.Max.Z)
                );
            });

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Core classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind core classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

void UAuracronPCGPythonBindingManager::BindNodeClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind node-related classes
        pybind11::enum_<EAuracronPCGNodeCategory>(PCGModule, "PCGNodeCategory")
            .value("Generator", EAuracronPCGNodeCategory::Generator)
            .value("Modifier", EAuracronPCGNodeCategory::Modifier)
            .value("Filter", EAuracronPCGNodeCategory::Filter)
            .value("Sampler", EAuracronPCGNodeCategory::Sampler)
            .value("Debug", EAuracronPCGNodeCategory::Debug)
            .value("Utility", EAuracronPCGNodeCategory::Utility);

        // Bind node metadata structure
        pybind11::class_<FAuracronPCGNodeMetadata>(PCGModule, "PCGNodeMetadata")
            .def(pybind11::init<>())
            .def_readwrite("node_name", &FAuracronPCGNodeMetadata::NodeName)
            .def_readwrite("node_description", &FAuracronPCGNodeMetadata::NodeDescription)
            .def_readwrite("category", &FAuracronPCGNodeMetadata::Category);

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Node classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind node classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}

void UAuracronPCGPythonBindingManager::BindDataClasses()
{
#ifdef WITH_PYTHON
    try
    {
        // Bind data-related structures
        pybind11::class_<FPCGPoint>(PCGModule, "PCGPoint")
            .def(pybind11::init<>())
            .def_readwrite("density", &FPCGPoint::Density)
            .def_property("position",
                [](const FPCGPoint& self) {
                    FVector Pos = self.Transform.GetLocation();
                    return std::make_tuple(Pos.X, Pos.Y, Pos.Z);
                },
                [](FPCGPoint& self, const std::tuple<float, float, float>& pos) {
                    FVector NewPos(std::get<0>(pos), std::get<1>(pos), std::get<2>(pos));
                    self.Transform.SetLocation(NewPos);
                });

        pybind11::class_<FVector>(PCGModule, "Vector")
            .def(pybind11::init<float, float, float>())
            .def_readwrite("x", &FVector::X)
            .def_readwrite("y", &FVector::Y)
            .def_readwrite("z", &FVector::Z);

        pybind11::class_<FLinearColor>(PCGModule, "LinearColor")
            .def(pybind11::init<float, float, float, float>())
            .def_readwrite("r", &FLinearColor::R)
            .def_readwrite("g", &FLinearColor::G)
            .def_readwrite("b", &FLinearColor::B)
            .def_readwrite("a", &FLinearColor::A);

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Data classes bound successfully"));
    }
    catch (const std::exception& e)
    {
        AddError(FString::Printf(TEXT("Failed to bind data classes: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif
}
