// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Point Processing Nodes Implementation
// Bridge 2.4: PCG Framework - Point Processing Nodes

#include "AuracronPCGPointProcessing.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Async/ParallelFor.h"

// =============================================================================
// ADVANCED POINT FILTER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedPointFilterSettings::UAuracronPCGAdvancedPointFilterSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Point Filter");
    NodeMetadata.NodeDescription = TEXT("Enhanced point filtering with mathematical operations and complex criteria");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Filter;
    NodeMetadata.Tags.Add(TEXT("Filter"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("Math"));
    NodeMetadata.Tags.Add(TEXT("Processing"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.2f, 0.4f);
}

void UAuracronPCGAdvancedPointFilterSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAdvancedPointFilterSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& PassedPin = OutputPins.Emplace_GetRef();
    PassedPin.Label = TEXT("Passed");
    PassedPin.AllowedTypes = EPCGDataType::Point;

    FPCGPinProperties& FilteredPin = OutputPins.Emplace_GetRef();
    FilteredPin.Label = TEXT("Filtered");
    FilteredPin.AllowedTypes = EPCGDataType::Point;
    FilteredPin.bAdvancedPin = true;
}

FAuracronPCGElementResult FAuracronPCGAdvancedPointFilterElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                              FPCGDataCollection& OutputData, 
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedPointFilterSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedPointFilterSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Point Filter");
            return Result;
        }

        // Create output point data
        UPCGPointData* PassedData = NewObject<UPCGPointData>();
        PassedData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& PassedPoints = PassedData->GetMutablePoints();

        UPCGPointData* FilteredData = NewObject<UPCGPointData>();
        FilteredData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& FilteredPoints = FilteredData->GetMutablePoints();

        int32 TotalProcessed = 0;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            
            // Process points in batches for performance
            if (Settings->bUseMultithreading && InputPoints.Num() > Settings->BatchSize)
            {
                // Parallel processing
                TArray<bool> FilterResults;
                FilterResults.SetNum(InputPoints.Num());

                ParallelFor(InputPoints.Num(), [&](int32 Index)
                {
                    FilterResults[Index] = EvaluatePointFilter(InputPoints[Index], Settings, InputPointData->Metadata);
                });

                // Collect results
                for (int32 i = 0; i < InputPoints.Num(); i++)
                {
                    bool bPassed = FilterResults[i];
                    if (Settings->bInvertFilter)
                    {
                        bPassed = !bPassed;
                    }

                    if (bPassed)
                    {
                        PassedPoints.Add(InputPoints[i]);
                    }
                    else
                    {
                        FilteredPoints.Add(InputPoints[i]);
                    }
                }
            }
            else
            {
                // Sequential processing
                for (const FPCGPoint& Point : InputPoints)
                {
                    TotalProcessed++;
                    bool bPassed = EvaluatePointFilter(Point, Settings, InputPointData->Metadata);
                    
                    if (Settings->bInvertFilter)
                    {
                        bPassed = !bPassed;
                    }

                    if (bPassed)
                    {
                        PassedPoints.Add(Point);
                    }
                    else
                    {
                        FilteredPoints.Add(Point);
                    }
                }
            }

            // Copy metadata if available
            if (InputPointData->Metadata)
            {
                if (!PassedData->Metadata)
                {
                    PassedData->Metadata = InputPointData->Metadata->Copy();
                }
                if (!FilteredData->Metadata)
                {
                    FilteredData->Metadata = InputPointData->Metadata->Copy();
                }
            }
        }

        // Add passed points to output
        FPCGTaggedData& PassedTaggedData = OutputData.TaggedData.Emplace_GetRef();
        PassedTaggedData.Data = PassedData;
        PassedTaggedData.Pin = TEXT("Passed");

        // Add filtered points to output
        FPCGTaggedData& FilteredTaggedData = OutputData.TaggedData.Emplace_GetRef();
        FilteredTaggedData.Data = FilteredData;
        FilteredTaggedData.Pin = TEXT("Filtered");

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = 2;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Point Filter processed %d points (%d passed, %d filtered)"), 
                                  TotalProcessed, PassedPoints.Num(), FilteredPoints.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Point Filter error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

bool FAuracronPCGAdvancedPointFilterElement::EvaluatePointFilter(const FPCGPoint& Point, 
                                                                 const UAuracronPCGAdvancedPointFilterSettings* Settings,
                                                                 const UPCGMetadata* Metadata) const
{
    // Position filter
    if (Settings->bFilterByPosition)
    {
        FVector Position = Point.Transform.GetLocation();
        if (!Settings->PositionBounds.IsInside(Position))
        {
            return false;
        }
    }

    // Density filter
    if (Settings->bFilterByDensity)
    {
        if (Point.Density < Settings->DensityRange.X || Point.Density > Settings->DensityRange.Y)
        {
            return false;
        }
    }

    // Scale filter
    if (Settings->bFilterByScale)
    {
        FVector Scale = Point.Transform.GetScale3D();
        if (Scale.X < Settings->ScaleMin.X || Scale.X > Settings->ScaleMax.X ||
            Scale.Y < Settings->ScaleMin.Y || Scale.Y > Settings->ScaleMax.Y ||
            Scale.Z < Settings->ScaleMin.Z || Scale.Z > Settings->ScaleMax.Z)
        {
            return false;
        }
    }

    // Mathematical operation filter
    if (Settings->bUseMathOperation)
    {
        float AttributeValue = 0.0f;
        if (Settings->SourceAttribute == TEXT("Density"))
        {
            AttributeValue = Point.Density;
        }
        else if (Metadata)
        {
            // Get attribute from metadata (simplified implementation)
            AttributeValue = 0.5f; // Default value
        }

        float Result = UAuracronPCGPointProcessingUtils::ApplyMathOperation(
            Settings->MathOperation, AttributeValue, Settings->MathOperand);

        if (Result < Settings->ResultRange.X || Result > Settings->ResultRange.Y)
        {
            return false;
        }
    }

    // Custom expression filter
    if (Settings->bUseCustomExpression && !Settings->CustomExpression.IsEmpty())
    {
        TMap<FString, float> Variables;
        Variables.Add(TEXT("Density"), Point.Density);
        Variables.Add(TEXT("X"), Point.Transform.GetLocation().X);
        Variables.Add(TEXT("Y"), Point.Transform.GetLocation().Y);
        Variables.Add(TEXT("Z"), Point.Transform.GetLocation().Z);
        Variables.Add(TEXT("ScaleX"), Point.Transform.GetScale3D().X);
        Variables.Add(TEXT("ScaleY"), Point.Transform.GetScale3D().Y);
        Variables.Add(TEXT("ScaleZ"), Point.Transform.GetScale3D().Z);

        float ExpressionResult = AuracronPCGPointProcessingUtils::EvaluateCustomExpression(
            Settings->CustomExpression, Variables);

        return ExpressionResult > 0.0f;
    }

    return true;
}

// =============================================================================
// ADVANCED POINT TRANSFORMER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedPointTransformerSettings::UAuracronPCGAdvancedPointTransformerSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Point Transformer");
    NodeMetadata.NodeDescription = TEXT("Enhanced point transformation with mathematical operations and complex transformations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Transform;
    NodeMetadata.Tags.Add(TEXT("Transform"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("Math"));
    NodeMetadata.Tags.Add(TEXT("Processing"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.4f);
}

void UAuracronPCGAdvancedPointTransformerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAdvancedPointTransformerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGAdvancedPointTransformerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                                   FPCGDataCollection& OutputData, 
                                                                                   const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedPointTransformerSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedPointTransformerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Point Transformer");
            return Result;
        }

        // Create output point data
        UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
        OutputPointData->InitializeFromData(nullptr);
        TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

        int32 TotalProcessed = 0;

        // Process all input data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            
            for (const FPCGPoint& InputPoint : InputPoints)
            {
                TotalProcessed++;
                FPCGPoint OutputPoint = InputPoint;

                // Apply transformations
                ApplyAdvancedTransformation(OutputPoint, Settings);

                OutputPoints.Add(OutputPoint);
            }

            // Copy metadata if available
            if (InputPointData->Metadata && !OutputPointData->Metadata)
            {
                OutputPointData->Metadata = InputPointData->Metadata->Copy();
            }
        }

        // Add to output
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = OutputPointData;
        TaggedData.Pin = TEXT("Output");

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Point Transformer processed %d points"), TotalProcessed);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Point Transformer error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGAdvancedPointTransformerElement::ApplyAdvancedTransformation(FPCGPoint& Point,
                                                                              const UAuracronPCGAdvancedPointTransformerSettings* Settings) const
{
    FTransform& Transform = Point.Transform;

    // Mathematical transformations
    if (Settings->bUseMathTransform)
    {
        // Position transformation
        if (Settings->bTransformPosition)
        {
            FVector Position = Transform.GetLocation();
            FVector NewPosition = UAuracronPCGPointProcessingUtils::ApplyMathOperationVector(
                Settings->PositionMathOp, Position, Settings->PositionOperand);
            Transform.SetLocation(NewPosition);
        }

        // Scale transformation
        if (Settings->bTransformScale)
        {
            FVector Scale = Transform.GetScale3D();
            FVector NewScale = UAuracronPCGPointProcessingUtils::ApplyMathOperationVector(
                Settings->ScaleMathOp, Scale, Settings->ScaleOperand);
            Transform.SetScale3D(NewScale);
        }
    }

    // Matrix transformation
    if (Settings->bUseMatrixTransform)
    {
        FTransform MatrixTransform(Settings->CustomTransformMatrix);
        Transform = Transform * MatrixTransform;
    }

    // Noise transformation
    if (Settings->bUseNoiseTransform)
    {
        FVector Position = Transform.GetLocation();
        float NoiseValue = AuracronPCGElementUtils::GenerateNoise(
            Settings->NoiseType, Position, Settings->NoiseScale, 1);

        FVector NoiseOffset = FVector(NoiseValue) * Settings->NoiseIntensity * 100.0f;
        Transform.SetLocation(Position + NoiseOffset);
    }
}

// =============================================================================
// POINT PROCESSING UTILITIES IMPLEMENTATION
// =============================================================================

float UAuracronPCGPointProcessingUtils::ApplyMathOperation(EAuracronPCGMathOperation Operation, float ValueA, float ValueB)
{
    switch (Operation)
    {
        case EAuracronPCGMathOperation::Add:
            return ValueA + ValueB;
        case EAuracronPCGMathOperation::Subtract:
            return ValueA - ValueB;
        case EAuracronPCGMathOperation::Multiply:
            return ValueA * ValueB;
        case EAuracronPCGMathOperation::Divide:
            return ValueB != 0.0f ? ValueA / ValueB : 0.0f;
        case EAuracronPCGMathOperation::Power:
            return FMath::Pow(ValueA, ValueB);
        case EAuracronPCGMathOperation::Modulo:
            return ValueB != 0.0f ? FMath::Fmod(ValueA, ValueB) : 0.0f;
        case EAuracronPCGMathOperation::Min:
            return FMath::Min(ValueA, ValueB);
        case EAuracronPCGMathOperation::Max:
            return FMath::Max(ValueA, ValueB);
        case EAuracronPCGMathOperation::Average:
            return (ValueA + ValueB) * 0.5f;
        case EAuracronPCGMathOperation::Lerp:
            return FMath::Lerp(ValueA, ValueB, 0.5f);
        case EAuracronPCGMathOperation::Clamp:
            return FMath::Clamp(ValueA, 0.0f, ValueB);
        case EAuracronPCGMathOperation::Normalize:
            return ValueB != 0.0f ? ValueA / ValueB : 0.0f;
        case EAuracronPCGMathOperation::Abs:
            return FMath::Abs(ValueA);
        case EAuracronPCGMathOperation::Sign:
            return FMath::Sign(ValueA);
        case EAuracronPCGMathOperation::Floor:
            return FMath::Floor(ValueA);
        case EAuracronPCGMathOperation::Ceil:
            return FMath::Ceil(ValueA);
        case EAuracronPCGMathOperation::Round:
            return FMath::Round(ValueA);
        case EAuracronPCGMathOperation::Frac:
            return FMath::Frac(ValueA);
        case EAuracronPCGMathOperation::Sqrt:
            return FMath::Sqrt(FMath::Max(0.0f, ValueA));
        case EAuracronPCGMathOperation::Log:
            return ValueA > 0.0f ? FMath::Loge(ValueA) : 0.0f;
        case EAuracronPCGMathOperation::Exp:
            return FMath::Exp(ValueA);
        case EAuracronPCGMathOperation::Sin:
            return FMath::Sin(ValueA);
        case EAuracronPCGMathOperation::Cos:
            return FMath::Cos(ValueA);
        case EAuracronPCGMathOperation::Tan:
            return FMath::Tan(ValueA);
        default:
            return ValueA;
    }
}

FVector UAuracronPCGPointProcessingUtils::ApplyMathOperationVector(EAuracronPCGMathOperation Operation, const FVector& ValueA, const FVector& ValueB)
{
    return FVector(
        ApplyMathOperation(Operation, ValueA.X, ValueB.X),
        ApplyMathOperation(Operation, ValueA.Y, ValueB.Y),
        ApplyMathOperation(Operation, ValueA.Z, ValueB.Z)
    );
}

bool UAuracronPCGPointProcessingUtils::ComparePoints(const FPCGPoint& PointA, const FPCGPoint& PointB,
                                                     EAuracronPCGSortCriteria Criteria, const FVector& ReferencePoint)
{
    switch (Criteria)
    {
        case EAuracronPCGSortCriteria::Position:
            return PointA.Transform.GetLocation().X < PointB.Transform.GetLocation().X;
        case EAuracronPCGSortCriteria::Distance:
            return FVector::Dist(PointA.Transform.GetLocation(), ReferencePoint) <
                   FVector::Dist(PointB.Transform.GetLocation(), ReferencePoint);
        case EAuracronPCGSortCriteria::Density:
            return PointA.Density < PointB.Density;
        case EAuracronPCGSortCriteria::Scale:
            return PointA.Transform.GetScale3D().Size() < PointB.Transform.GetScale3D().Size();
        default:
            return false;
    }
}

float UAuracronPCGPointProcessingUtils::CalculatePointDistance(const FPCGPoint& PointA, const FPCGPoint& PointB, bool bUse2D)
{
    if (bUse2D)
    {
        return FVector::Dist2D(PointA.Transform.GetLocation(), PointB.Transform.GetLocation());
    }
    else
    {
        return FVector::Dist(PointA.Transform.GetLocation(), PointB.Transform.GetLocation());
    }
}

bool UAuracronPCGPointProcessingUtils::IsPointInRegion(const FPCGPoint& Point, const FBox& Region)
{
    return Region.IsInside(Point.Transform.GetLocation());
}

int32 UAuracronPCGPointProcessingUtils::GetOptimalBatchSize(int32 TotalPoints, int32 ThreadCount)
{
    if (ThreadCount <= 0)
    {
        ThreadCount = FPlatformMisc::NumberOfCoresIncludingHyperthreads();
    }

    return FMath::Max(100, TotalPoints / (ThreadCount * 4));
}

bool UAuracronPCGPointProcessingUtils::ShouldUseParallelProcessing(int32 PointCount, int32 Threshold)
{
    return PointCount >= Threshold;
}

// =============================================================================
// ADVANCED POINT SPLITTER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedPointSplitterSettings::UAuracronPCGAdvancedPointSplitterSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Point Splitter");
    NodeMetadata.NodeDescription = TEXT("Enhanced point splitting with multiple criteria and distribution strategies");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Utility;
    NodeMetadata.Tags.Add(TEXT("Split"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("Processing"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.8f, 0.2f);
}

void UAuracronPCGAdvancedPointSplitterSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAdvancedPointSplitterSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    for (int32 i = 0; i < OutputCount; i++)
    {
        FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
        OutputPin.Label = FText::FromString(FString::Printf(TEXT("Output%d"), i + 1));
        OutputPin.AllowedTypes = EPCGDataType::Point;
    }
}

FAuracronPCGElementResult FAuracronPCGAdvancedPointSplitterElement::ProcessData(const FPCGDataCollection& InputData,
                                                                                FPCGDataCollection& OutputData,
                                                                                const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedPointSplitterSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedPointSplitterSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Point Splitter");
            return Result;
        }

        // Collect all input points
        TArray<FPCGPoint> AllPoints;
        const UPCGMetadata* InputMetadata = nullptr;

        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            AllPoints.Append(InputPointData->GetPoints());
            if (!InputMetadata)
            {
                InputMetadata = InputPointData->Metadata;
            }
        }

        if (AllPoints.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No input points to split");
            return Result;
        }

        // Split points into groups
        TArray<TArray<FPCGPoint>> PointGroups = AuracronPCGPointProcessingUtils::SplitPointsIntoGroups(
            AllPoints, Settings->OutputCount, Settings->SplitCriteria);

        // Create output data for each group
        for (int32 GroupIndex = 0; GroupIndex < PointGroups.Num(); GroupIndex++)
        {
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(nullptr);
            OutputPointData->GetMutablePoints() = PointGroups[GroupIndex];

            // Copy metadata if available
            if (InputMetadata)
            {
                OutputPointData->Metadata = InputMetadata->Copy();
            }

            // Add to output
            FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
            TaggedData.Data = OutputPointData;
            TaggedData.Pin = FText::FromString(FString::Printf(TEXT("Output%d"), GroupIndex + 1));
        }

        Result.bSuccess = true;
        Result.PointsProcessed = AllPoints.Num();
        Result.OutputDataCount = PointGroups.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Point Splitter split %d points into %d groups"),
                                  AllPoints.Num(), PointGroups.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Point Splitter error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

// =============================================================================
// ADVANCED POINT SORTER IMPLEMENTATION
// =============================================================================

UAuracronPCGAdvancedPointSorterSettings::UAuracronPCGAdvancedPointSorterSettings()
{
    NodeMetadata.NodeName = TEXT("Advanced Point Sorter");
    NodeMetadata.NodeDescription = TEXT("Enhanced point sorting with multiple criteria and custom comparisons");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Utility;
    NodeMetadata.Tags.Add(TEXT("Sort"));
    NodeMetadata.Tags.Add(TEXT("Advanced"));
    NodeMetadata.Tags.Add(TEXT("Processing"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.6f, 0.4f);
}

void UAuracronPCGAdvancedPointSorterSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGAdvancedPointSorterSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;
}

FAuracronPCGElementResult FAuracronPCGAdvancedPointSorterElement::ProcessData(const FPCGDataCollection& InputData,
                                                                              FPCGDataCollection& OutputData,
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGAdvancedPointSorterSettings* Settings = GetTypedSettings<UAuracronPCGAdvancedPointSorterSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Advanced Point Sorter");
            return Result;
        }

        // Collect all input points
        TArray<FPCGPoint> AllPoints;
        const UPCGMetadata* InputMetadata = nullptr;

        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            AllPoints.Append(InputPointData->GetPoints());
            if (!InputMetadata)
            {
                InputMetadata = InputPointData->Metadata;
            }
        }

        if (AllPoints.Num() == 0)
        {
            Result.ErrorMessage = TEXT("No input points to sort");
            return Result;
        }

        // Create comparator function
        auto Comparator = [Settings](const FPCGPoint& PointA, const FPCGPoint& PointB) -> bool
        {
            bool PrimaryResult = UAuracronPCGPointProcessingUtils::ComparePoints(
                PointA, PointB, Settings->PrimarySortCriteria, Settings->ReferencePoint);

            if (Settings->bUseSecondaryCriteria)
            {
                bool SecondaryResult = UAuracronPCGPointProcessingUtils::ComparePoints(
                    PointA, PointB, Settings->SecondarySortCriteria, Settings->ReferencePoint);

                // Use secondary criteria as tiebreaker
                if (PrimaryResult == UAuracronPCGPointProcessingUtils::ComparePoints(
                    PointB, PointA, Settings->PrimarySortCriteria, Settings->ReferencePoint))
                {
                    PrimaryResult = SecondaryResult;
                }
            }

            return Settings->bDescendingOrder ? !PrimaryResult : PrimaryResult;
        };

        // Sort points
        if (Settings->bUseParallelSort && AllPoints.Num() >= Settings->ParallelSortThreshold)
        {
            AuracronPCGPointProcessingUtils::SortPointsParallel(AllPoints, Comparator);
        }
        else
        {
            if (Settings->bUseStableSort)
            {
                AllPoints.StableSort(Comparator);
            }
            else
            {
                AllPoints.Sort(Comparator);
            }
        }

        // Create output data
        UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
        OutputPointData->InitializeFromData(nullptr);
        OutputPointData->GetMutablePoints() = AllPoints;

        // Copy metadata if available
        if (InputMetadata)
        {
            OutputPointData->Metadata = InputMetadata->Copy();
        }

        // Add to output
        FPCGTaggedData& TaggedData = OutputData.TaggedData.Emplace_GetRef();
        TaggedData.Data = OutputPointData;
        TaggedData.Pin = TEXT("Output");

        Result.bSuccess = true;
        Result.PointsProcessed = AllPoints.Num();
        Result.OutputDataCount = 1;

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Advanced Point Sorter sorted %d points"), AllPoints.Num());
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Advanced Point Sorter error: %s"), *Result.ErrorMessage);
    }

    return Result;
}
