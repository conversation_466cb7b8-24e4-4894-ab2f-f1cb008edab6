// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Mesh Generation Utilities Implementation
// Bridge 2.6: PCG Framework - Mesh Generation Nodes

#include "AuracronPCGMeshGeneration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "ProceduralMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

// =============================================================================
// MESH GENERATION UTILITIES IMPLEMENTATION
// =============================================================================

int32 UAuracronPCGMeshGenerationUtils::SelectMeshIndex(const TArray<FAuracronPCGMeshEntry>& MeshEntries, 
                                                       const FPCGPoint& Point, 
                                                       const UPCGMetadata* Metadata, 
                                                       EAuracronPCGMeshSelectionMode SelectionMode, 
                                                       const FString& SelectionAttribute)
{
    if (MeshEntries.Num() == 0)
    {
        return -1;
    }

    switch (SelectionMode)
    {
        case EAuracronPCGMeshSelectionMode::Random:
        {
            // Use point seed for consistent randomness
            FRandomStream RandomStream(Point.Seed);
            return RandomStream.RandRange(0, MeshEntries.Num() - 1);
        }
        case EAuracronPCGMeshSelectionMode::ByAttribute:
        {
            if (Metadata && !SelectionAttribute.IsEmpty())
            {
                // Get attribute value and map to mesh index
                // Simplified implementation - in production you'd handle all attribute types
                float AttributeValue = 0.5f; // Default value
                int32 Index = FMath::FloorToInt(AttributeValue * MeshEntries.Num());
                return FMath::Clamp(Index, 0, MeshEntries.Num() - 1);
            }
            return 0;
        }
        case EAuracronPCGMeshSelectionMode::ByDensity:
        {
            float Density = Point.Density;
            int32 Index = FMath::FloorToInt(Density * MeshEntries.Num());
            return FMath::Clamp(Index, 0, MeshEntries.Num() - 1);
        }
        case EAuracronPCGMeshSelectionMode::Sequential:
        {
            // Use point index for sequential selection
            return Point.Seed % MeshEntries.Num();
        }
        case EAuracronPCGMeshSelectionMode::Weighted:
        {
            // Calculate total weight
            float TotalWeight = 0.0f;
            for (const FAuracronPCGMeshEntry& Entry : MeshEntries)
            {
                TotalWeight += Entry.Weight;
            }

            if (TotalWeight <= 0.0f)
            {
                return 0;
            }

            // Generate random value based on point seed
            FRandomStream RandomStream(Point.Seed);
            float RandomValue = RandomStream.FRand() * TotalWeight;

            // Find weighted selection
            float CurrentWeight = 0.0f;
            for (int32 i = 0; i < MeshEntries.Num(); i++)
            {
                CurrentWeight += MeshEntries[i].Weight;
                if (RandomValue <= CurrentWeight)
                {
                    return i;
                }
            }

            return MeshEntries.Num() - 1;
        }
        default:
            return 0;
    }
}

bool UAuracronPCGMeshGenerationUtils::ValidateMeshEntry(const FAuracronPCGMeshEntry& MeshEntry, FString& ValidationError)
{
    ValidationError.Empty();

    // Check if mesh is valid
    if (!MeshEntry.Mesh.IsValid())
    {
        ValidationError = TEXT("Mesh reference is invalid");
        return false;
    }

    // Check weight
    if (MeshEntry.Weight < 0.0f)
    {
        ValidationError = TEXT("Weight cannot be negative");
        return false;
    }

    // Check scale
    if (MeshEntry.LocalScale.X <= 0.0f || MeshEntry.LocalScale.Y <= 0.0f || MeshEntry.LocalScale.Z <= 0.0f)
    {
        ValidationError = TEXT("Scale components must be positive");
        return false;
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::GenerateBoxMesh(const FVector& Size, FAuracronPCGProceduralMeshDescriptor& OutMeshDescriptor)
{
    OutMeshDescriptor.Vertices.Empty();
    OutMeshDescriptor.Triangles.Empty();
    OutMeshDescriptor.Normals.Empty();
    OutMeshDescriptor.UVs.Empty();

    FVector HalfSize = Size * 0.5f;

    // Generate vertices (8 corners of a box)
    TArray<FVector> BoxVertices = {
        FVector(-HalfSize.X, -HalfSize.Y, -HalfSize.Z), // 0
        FVector( HalfSize.X, -HalfSize.Y, -HalfSize.Z), // 1
        FVector( HalfSize.X,  HalfSize.Y, -HalfSize.Z), // 2
        FVector(-HalfSize.X,  HalfSize.Y, -HalfSize.Z), // 3
        FVector(-HalfSize.X, -HalfSize.Y,  HalfSize.Z), // 4
        FVector( HalfSize.X, -HalfSize.Y,  HalfSize.Z), // 5
        FVector( HalfSize.X,  HalfSize.Y,  HalfSize.Z), // 6
        FVector(-HalfSize.X,  HalfSize.Y,  HalfSize.Z)  // 7
    };

    // Generate faces (6 faces, 4 vertices each, 2 triangles per face)
    TArray<int32> FaceIndices = {
        // Bottom face
        0, 1, 2, 0, 2, 3,
        // Top face
        4, 7, 6, 4, 6, 5,
        // Front face
        0, 4, 5, 0, 5, 1,
        // Back face
        2, 6, 7, 2, 7, 3,
        // Left face
        0, 3, 7, 0, 7, 4,
        // Right face
        1, 5, 6, 1, 6, 2
    };

    // Generate normals for each face
    TArray<FVector> FaceNormals = {
        FVector(0, 0, -1), FVector(0, 0, -1), // Bottom
        FVector(0, 0,  1), FVector(0, 0,  1), // Top
        FVector(0, -1, 0), FVector(0, -1, 0), // Front
        FVector(0,  1, 0), FVector(0,  1, 0), // Back
        FVector(-1, 0, 0), FVector(-1, 0, 0), // Left
        FVector( 1, 0, 0), FVector( 1, 0, 0)  // Right
    };

    // Build final vertex and triangle arrays
    for (int32 FaceIndex = 0; FaceIndex < 6; FaceIndex++)
    {
        for (int32 VertexIndex = 0; VertexIndex < 6; VertexIndex++) // 6 vertices per face (2 triangles)
        {
            int32 BoxVertexIndex = FaceIndices[FaceIndex * 6 + VertexIndex];
            OutMeshDescriptor.Vertices.Add(BoxVertices[BoxVertexIndex]);
            OutMeshDescriptor.Normals.Add(FaceNormals[FaceIndex * 2 + (VertexIndex / 3)]);
            OutMeshDescriptor.Triangles.Add(OutMeshDescriptor.Vertices.Num() - 1);
            
            // Generate UVs
            FVector2D UV = FVector2D(0.0f, 0.0f);
            if (VertexIndex % 3 == 1) UV.X = 1.0f;
            if (VertexIndex % 3 == 2) UV.Y = 1.0f;
            if (VertexIndex == 3) { UV.X = 0.0f; UV.Y = 1.0f; }
            if (VertexIndex == 4) { UV.X = 1.0f; UV.Y = 1.0f; }
            if (VertexIndex == 5) { UV.X = 1.0f; UV.Y = 0.0f; }
            OutMeshDescriptor.UVs.Add(UV);
        }
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::GenerateSphereMesh(float Radius, int32 Segments, FAuracronPCGProceduralMeshDescriptor& OutMeshDescriptor)
{
    OutMeshDescriptor.Vertices.Empty();
    OutMeshDescriptor.Triangles.Empty();
    OutMeshDescriptor.Normals.Empty();
    OutMeshDescriptor.UVs.Empty();

    if (Segments < 3)
    {
        Segments = 3;
    }

    // Generate sphere vertices using spherical coordinates
    for (int32 LatIndex = 0; LatIndex <= Segments; LatIndex++)
    {
        float Lat = PI * LatIndex / Segments;
        float SinLat = FMath::Sin(Lat);
        float CosLat = FMath::Cos(Lat);

        for (int32 LonIndex = 0; LonIndex <= Segments; LonIndex++)
        {
            float Lon = 2.0f * PI * LonIndex / Segments;
            float SinLon = FMath::Sin(Lon);
            float CosLon = FMath::Cos(Lon);

            FVector Vertex = FVector(
                Radius * SinLat * CosLon,
                Radius * SinLat * SinLon,
                Radius * CosLat
            );

            OutMeshDescriptor.Vertices.Add(Vertex);
            OutMeshDescriptor.Normals.Add(Vertex.GetSafeNormal());
            OutMeshDescriptor.UVs.Add(FVector2D(
                static_cast<float>(LonIndex) / Segments,
                static_cast<float>(LatIndex) / Segments
            ));
        }
    }

    // Generate triangles
    for (int32 LatIndex = 0; LatIndex < Segments; LatIndex++)
    {
        for (int32 LonIndex = 0; LonIndex < Segments; LonIndex++)
        {
            int32 Current = LatIndex * (Segments + 1) + LonIndex;
            int32 Next = Current + Segments + 1;

            // First triangle
            OutMeshDescriptor.Triangles.Add(Current);
            OutMeshDescriptor.Triangles.Add(Next);
            OutMeshDescriptor.Triangles.Add(Current + 1);

            // Second triangle
            OutMeshDescriptor.Triangles.Add(Current + 1);
            OutMeshDescriptor.Triangles.Add(Next);
            OutMeshDescriptor.Triangles.Add(Next + 1);
        }
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::OptimizeMesh(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, 
                                                   bool bWeldVertices, 
                                                   float WeldThreshold, 
                                                   bool bRemoveDegenerateTriangles)
{
    if (bWeldVertices)
    {
        // Simplified vertex welding - in production you'd use more sophisticated algorithms
        TMap<int32, int32> VertexRemap;
        TArray<FVector> WeldedVertices;
        TArray<FVector> WeldedNormals;
        TArray<FVector2D> WeldedUVs;

        for (int32 i = 0; i < MeshDescriptor.Vertices.Num(); i++)
        {
            bool bFoundMatch = false;
            for (int32 j = 0; j < WeldedVertices.Num(); j++)
            {
                if (FVector::Dist(MeshDescriptor.Vertices[i], WeldedVertices[j]) < WeldThreshold)
                {
                    VertexRemap.Add(i, j);
                    bFoundMatch = true;
                    break;
                }
            }

            if (!bFoundMatch)
            {
                VertexRemap.Add(i, WeldedVertices.Num());
                WeldedVertices.Add(MeshDescriptor.Vertices[i]);
                if (i < MeshDescriptor.Normals.Num())
                {
                    WeldedNormals.Add(MeshDescriptor.Normals[i]);
                }
                if (i < MeshDescriptor.UVs.Num())
                {
                    WeldedUVs.Add(MeshDescriptor.UVs[i]);
                }
            }
        }

        // Update triangles with new vertex indices
        for (int32& TriangleIndex : MeshDescriptor.Triangles)
        {
            if (int32* RemappedIndex = VertexRemap.Find(TriangleIndex))
            {
                TriangleIndex = *RemappedIndex;
            }
        }

        MeshDescriptor.Vertices = WeldedVertices;
        MeshDescriptor.Normals = WeldedNormals;
        MeshDescriptor.UVs = WeldedUVs;
    }

    if (bRemoveDegenerateTriangles)
    {
        // Remove degenerate triangles
        TArray<int32> ValidTriangles;
        for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
        {
            if (i + 2 < MeshDescriptor.Triangles.Num())
            {
                int32 V0 = MeshDescriptor.Triangles[i];
                int32 V1 = MeshDescriptor.Triangles[i + 1];
                int32 V2 = MeshDescriptor.Triangles[i + 2];

                // Check if triangle is degenerate
                if (V0 != V1 && V1 != V2 && V0 != V2)
                {
                    if (V0 < MeshDescriptor.Vertices.Num() && 
                        V1 < MeshDescriptor.Vertices.Num() && 
                        V2 < MeshDescriptor.Vertices.Num())
                    {
                        FVector Edge1 = MeshDescriptor.Vertices[V1] - MeshDescriptor.Vertices[V0];
                        FVector Edge2 = MeshDescriptor.Vertices[V2] - MeshDescriptor.Vertices[V0];
                        FVector Normal = FVector::CrossProduct(Edge1, Edge2);

                        if (Normal.SizeSquared() > SMALL_NUMBER)
                        {
                            ValidTriangles.Add(V0);
                            ValidTriangles.Add(V1);
                            ValidTriangles.Add(V2);
                        }
                    }
                }
            }
        }
        MeshDescriptor.Triangles = ValidTriangles;
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::GenerateNormals(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, bool bSmoothNormals)
{
    MeshDescriptor.Normals.Empty();
    MeshDescriptor.Normals.SetNum(MeshDescriptor.Vertices.Num());

    // Initialize normals to zero
    for (FVector& Normal : MeshDescriptor.Normals)
    {
        Normal = FVector::ZeroVector;
    }

    // Calculate face normals and accumulate to vertex normals
    for (int32 i = 0; i < MeshDescriptor.Triangles.Num(); i += 3)
    {
        if (i + 2 < MeshDescriptor.Triangles.Num())
        {
            int32 V0 = MeshDescriptor.Triangles[i];
            int32 V1 = MeshDescriptor.Triangles[i + 1];
            int32 V2 = MeshDescriptor.Triangles[i + 2];

            if (V0 < MeshDescriptor.Vertices.Num() && 
                V1 < MeshDescriptor.Vertices.Num() && 
                V2 < MeshDescriptor.Vertices.Num())
            {
                FVector Edge1 = MeshDescriptor.Vertices[V1] - MeshDescriptor.Vertices[V0];
                FVector Edge2 = MeshDescriptor.Vertices[V2] - MeshDescriptor.Vertices[V0];
                FVector FaceNormal = FVector::CrossProduct(Edge1, Edge2).GetSafeNormal();

                MeshDescriptor.Normals[V0] += FaceNormal;
                MeshDescriptor.Normals[V1] += FaceNormal;
                MeshDescriptor.Normals[V2] += FaceNormal;
            }
        }
    }

    // Normalize vertex normals
    for (FVector& Normal : MeshDescriptor.Normals)
    {
        Normal = Normal.GetSafeNormal();
    }

    return true;
}

bool UAuracronPCGMeshGenerationUtils::GenerateUVs(FAuracronPCGProceduralMeshDescriptor& MeshDescriptor, float UVScale, bool bWorldSpace)
{
    MeshDescriptor.UVs.Empty();
    MeshDescriptor.UVs.SetNum(MeshDescriptor.Vertices.Num());

    if (bWorldSpace)
    {
        // Generate UVs based on world space coordinates
        for (int32 i = 0; i < MeshDescriptor.Vertices.Num(); i++)
        {
            const FVector& Vertex = MeshDescriptor.Vertices[i];
            MeshDescriptor.UVs[i] = FVector2D(Vertex.X * UVScale, Vertex.Y * UVScale);
        }
    }
    else
    {
        // Generate planar UVs
        FBox BoundingBox(MeshDescriptor.Vertices);
        FVector Size = BoundingBox.GetSize();
        FVector Min = BoundingBox.Min;

        for (int32 i = 0; i < MeshDescriptor.Vertices.Num(); i++)
        {
            const FVector& Vertex = MeshDescriptor.Vertices[i];
            FVector RelativePos = (Vertex - Min) / Size;
            MeshDescriptor.UVs[i] = FVector2D(RelativePos.X * UVScale, RelativePos.Y * UVScale);
        }
    }

    return true;
}

int32 UAuracronPCGMeshGenerationUtils::GetOptimalInstanceCount(int32 TotalInstances, int32 MaxInstancesPerComponent)
{
    return FMath::Min(TotalInstances, MaxInstancesPerComponent);
}

bool UAuracronPCGMeshGenerationUtils::ShouldUseHierarchicalInstancing(int32 InstanceCount, int32 Threshold)
{
    return InstanceCount >= Threshold;
}
