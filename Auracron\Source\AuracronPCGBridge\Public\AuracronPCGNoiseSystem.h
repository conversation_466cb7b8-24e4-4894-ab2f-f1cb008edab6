// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Noise System Header
// Bridge 2.9: PCG Framework - Noise e Randomization

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGMetadata.h"

// Engine includes
#include "Engine/World.h"
#include "Math/UnrealMathUtility.h"
#include "Curves/CurveFloat.h"
#include "Curves/CurveVector.h"

#include "AuracronPCGNoiseSystem.generated.h"

// Noise types
UENUM(BlueprintType)
enum class EAuracronPCGNoiseType : uint8
{
    Perlin              UMETA(DisplayName = "Perlin Noise"),
    Simplex             UMETA(DisplayName = "Simplex Noise"),
    Worley              UMETA(DisplayName = "Worley Noise"),
    Ridge               UMETA(DisplayName = "Ridge Noise"),
    Billow              UMETA(DisplayName = "Billow Noise"),
    Voronoi             UMETA(DisplayName = "Voronoi Noise"),
    Cellular            UMETA(DisplayName = "Cellular Noise"),
    Value               UMETA(DisplayName = "Value Noise"),
    Gradient            UMETA(DisplayName = "Gradient Noise"),
    Curl                UMETA(DisplayName = "Curl Noise"),
    Fractal             UMETA(DisplayName = "Fractal Noise"),
    Custom              UMETA(DisplayName = "Custom Noise")
};

// Noise dimensions
UENUM(BlueprintType)
enum class EAuracronPCGNoiseDimension : uint8
{
    OneDimensional      UMETA(DisplayName = "1D"),
    TwoDimensional      UMETA(DisplayName = "2D"),
    ThreeDimensional    UMETA(DisplayName = "3D"),
    FourDimensional     UMETA(DisplayName = "4D")
};

// Fractal types
UENUM(BlueprintType)
enum class EAuracronPCGFractalType : uint8
{
    None                UMETA(DisplayName = "None"),
    FBM                 UMETA(DisplayName = "Fractional Brownian Motion"),
    Turbulence          UMETA(DisplayName = "Turbulence"),
    RidgedMulti         UMETA(DisplayName = "Ridged Multifractal"),
    Billow              UMETA(DisplayName = "Billow"),
    PingPong            UMETA(DisplayName = "Ping Pong"),
    DomainWarp          UMETA(DisplayName = "Domain Warp"),
    Custom              UMETA(DisplayName = "Custom")
};

// Interpolation types
UENUM(BlueprintType)
enum class EAuracronPCGNoiseInterpolation : uint8
{
    Linear              UMETA(DisplayName = "Linear"),
    Hermite             UMETA(DisplayName = "Hermite"),
    Quintic             UMETA(DisplayName = "Quintic"),
    Cosine              UMETA(DisplayName = "Cosine"),
    Cubic               UMETA(DisplayName = "Cubic"),
    Smoothstep          UMETA(DisplayName = "Smoothstep"),
    Smootherstep        UMETA(DisplayName = "Smootherstep")
};

// Distance functions for Worley noise
UENUM(BlueprintType)
enum class EAuracronPCGWorleyDistanceFunction : uint8
{
    Euclidean           UMETA(DisplayName = "Euclidean"),
    Manhattan           UMETA(DisplayName = "Manhattan"),
    Chebyshev           UMETA(DisplayName = "Chebyshev"),
    Minkowski           UMETA(DisplayName = "Minkowski"),
    Natural             UMETA(DisplayName = "Natural"),
    Hybrid              UMETA(DisplayName = "Hybrid")
};

// Randomization modes
UENUM(BlueprintType)
enum class EAuracronPCGRandomizationMode : uint8
{
    Deterministic       UMETA(DisplayName = "Deterministic"),
    SeedBased           UMETA(DisplayName = "Seed Based"),
    TimeBased           UMETA(DisplayName = "Time Based"),
    PositionBased       UMETA(DisplayName = "Position Based"),
    AttributeBased      UMETA(DisplayName = "Attribute Based"),
    Hybrid              UMETA(DisplayName = "Hybrid"),
    Custom              UMETA(DisplayName = "Custom")
};

// =============================================================================
// NOISE DESCRIPTOR
// =============================================================================

/**
 * Noise Descriptor
 * Describes parameters for noise generation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGNoiseDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    EAuracronPCGNoiseType NoiseType = EAuracronPCGNoiseType::Perlin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    EAuracronPCGNoiseDimension Dimension = EAuracronPCGNoiseDimension::TwoDimensional;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Parameters")
    float Frequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Parameters")
    float Amplitude = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Parameters")
    int32 Seed = 12345;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Parameters")
    FVector Offset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fractal")
    EAuracronPCGFractalType FractalType = EAuracronPCGFractalType::FBM;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fractal")
    int32 Octaves = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fractal")
    float Lacunarity = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fractal")
    float Persistence = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fractal")
    float Gain = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interpolation")
    EAuracronPCGNoiseInterpolation InterpolationType = EAuracronPCGNoiseInterpolation::Quintic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Worley", meta = (EditCondition = "NoiseType == EAuracronPCGNoiseType::Worley"))
    EAuracronPCGWorleyDistanceFunction DistanceFunction = EAuracronPCGWorleyDistanceFunction::Euclidean;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Worley", meta = (EditCondition = "NoiseType == EAuracronPCGNoiseType::Worley"))
    float Jitter = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    float OutputMin = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    float OutputMax = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bInvert = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bAbsolute = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bTiling = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced", meta = (EditCondition = "bTiling"))
    FVector TileSize = FVector(256.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bNormalize = true;

    FAuracronPCGNoiseDescriptor()
    {
        NoiseType = EAuracronPCGNoiseType::Perlin;
        Dimension = EAuracronPCGNoiseDimension::TwoDimensional;
        Frequency = 1.0f;
        Amplitude = 1.0f;
        Seed = 12345;
        Offset = FVector::ZeroVector;
        FractalType = EAuracronPCGFractalType::FBM;
        Octaves = 4;
        Lacunarity = 2.0f;
        Persistence = 0.5f;
        Gain = 0.5f;
        InterpolationType = EAuracronPCGNoiseInterpolation::Quintic;
        DistanceFunction = EAuracronPCGWorleyDistanceFunction::Euclidean;
        Jitter = 1.0f;
        OutputMin = 0.0f;
        OutputMax = 1.0f;
        bInvert = false;
        bAbsolute = false;
        bTiling = false;
        TileSize = FVector(256.0f);
        bNormalize = true;
    }
};

// =============================================================================
// RANDOMIZATION DESCRIPTOR
// =============================================================================

/**
 * Randomization Descriptor
 * Describes parameters for randomization operations
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGRandomizationDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Randomization")
    EAuracronPCGRandomizationMode RandomizationMode = EAuracronPCGRandomizationMode::Deterministic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Randomization")
    int32 BaseSeed = 12345;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Randomization")
    bool bUseGlobalSeed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Randomization")
    bool bPerPointSeed = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distribution")
    bool bUniformDistribution = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distribution")
    bool bGaussianDistribution = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distribution", meta = (EditCondition = "bGaussianDistribution"))
    float GaussianMean = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distribution", meta = (EditCondition = "bGaussianDistribution"))
    float GaussianStdDev = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Range")
    FVector2D RandomRange = FVector2D(0.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Range")
    bool bClampOutput = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bDeterministicOrder = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    FString SeedAttribute = TEXT("Seed");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseSeedAttribute = false;

    FAuracronPCGRandomizationDescriptor()
    {
        RandomizationMode = EAuracronPCGRandomizationMode::Deterministic;
        BaseSeed = 12345;
        bUseGlobalSeed = false;
        bPerPointSeed = true;
        bUniformDistribution = true;
        bGaussianDistribution = false;
        GaussianMean = 0.0f;
        GaussianStdDev = 1.0f;
        RandomRange = FVector2D(0.0f, 1.0f);
        bClampOutput = true;
        bDeterministicOrder = true;
        SeedAttribute = TEXT("Seed");
        bUseSeedAttribute = false;
    }
};

// =============================================================================
// ADVANCED NOISE GENERATOR
// =============================================================================

/**
 * Advanced Noise Generator
 * Generates various types of noise with advanced parameters
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedNoiseGeneratorSettings, FAuracronPCGAdvancedNoiseGeneratorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedNoiseGeneratorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedNoiseGeneratorSettings();

    // Noise configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    FAuracronPCGNoiseDescriptor NoiseDescriptor;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString OutputAttribute = TEXT("Noise");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputToPosition = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputToScale = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputToRotation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputToColor = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputToDensity = false;

    // Multi-channel output
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Channel")
    bool bGenerateVectorNoise = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Channel", meta = (EditCondition = "bGenerateVectorNoise"))
    FAuracronPCGNoiseDescriptor NoiseDescriptorY;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Channel", meta = (EditCondition = "bGenerateVectorNoise"))
    FAuracronPCGNoiseDescriptor NoiseDescriptorZ;

    // Domain warping
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Domain Warp")
    bool bUseDomainWarp = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Domain Warp", meta = (EditCondition = "bUseDomainWarp"))
    FAuracronPCGNoiseDescriptor DomainWarpDescriptor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Domain Warp", meta = (EditCondition = "bUseDomainWarp"))
    float DomainWarpStrength = 1.0f;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseGPUAcceleration = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bCacheResults = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CacheSize = 1000;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedNoiseGeneratorElement, UAuracronPCGAdvancedNoiseGeneratorSettings)

// =============================================================================
// ADVANCED RANDOMIZER
// =============================================================================

/**
 * Advanced Randomizer
 * Provides advanced randomization capabilities with deterministic control
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedRandomizerSettings, FAuracronPCGAdvancedRandomizerElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedRandomizerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedRandomizerSettings();

    // Randomization configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Randomization")
    FAuracronPCGRandomizationDescriptor RandomizationDescriptor;

    // Target attributes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes")
    TArray<FString> TargetAttributes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes")
    bool bRandomizePosition = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes", meta = (EditCondition = "bRandomizePosition"))
    FVector PositionRange = FVector(100.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes")
    bool bRandomizeRotation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes", meta = (EditCondition = "bRandomizeRotation"))
    FVector RotationRange = FVector(360.0f, 360.0f, 360.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes")
    bool bRandomizeScale = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes", meta = (EditCondition = "bRandomizeScale"))
    FVector2D ScaleRange = FVector2D(0.5f, 2.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes")
    bool bRandomizeColor = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes")
    bool bRandomizeDensity = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Attributes", meta = (EditCondition = "bRandomizeDensity"))
    FVector2D DensityRange = FVector2D(0.0f, 1.0f);

    // Advanced randomization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseWeightedRandomization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced", meta = (EditCondition = "bUseWeightedRandomization"))
    FString WeightAttribute = TEXT("Weight");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseConditionalRandomization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced", meta = (EditCondition = "bUseConditionalRandomization"))
    FString ConditionAttribute = TEXT("Condition");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced", meta = (EditCondition = "bUseConditionalRandomization"))
    FVector2D ConditionRange = FVector2D(0.0f, 1.0f);

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputRandomSeed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputStatistics = false;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedRandomizerElement, UAuracronPCGAdvancedRandomizerSettings)

// =============================================================================
// NOISE COMBINER
// =============================================================================

/**
 * Noise Combiner
 * Combines multiple noise sources using various blending modes
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGNoiseCombinerSettings, FAuracronPCGNoiseCombinerElement)

UCLASS(BlueprintType, Blueprintable)
enum class EAuracronPCGNoiseCombineMode : uint8
{
    Add                 UMETA(DisplayName = "Add"),
    Subtract            UMETA(DisplayName = "Subtract"),
    Multiply            UMETA(DisplayName = "Multiply"),
    Divide              UMETA(DisplayName = "Divide"),
    Min                 UMETA(DisplayName = "Minimum"),
    Max                 UMETA(DisplayName = "Maximum"),
    Average             UMETA(DisplayName = "Average"),
    Screen              UMETA(DisplayName = "Screen"),
    Overlay             UMETA(DisplayName = "Overlay"),
    SoftLight           UMETA(DisplayName = "Soft Light"),
    HardLight           UMETA(DisplayName = "Hard Light"),
    ColorDodge          UMETA(DisplayName = "Color Dodge"),
    ColorBurn           UMETA(DisplayName = "Color Burn"),
    Difference          UMETA(DisplayName = "Difference"),
    Exclusion           UMETA(DisplayName = "Exclusion"),
    Custom              UMETA(DisplayName = "Custom")
};

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGNoiseCombinerSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGNoiseCombinerSettings();

    // Noise sources
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Sources")
    TArray<FAuracronPCGNoiseDescriptor> NoiseDescriptors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Sources")
    TArray<float> NoiseWeights;

    // Combination settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combination")
    EAuracronPCGNoiseCombineMode CombineMode = EAuracronPCGNoiseCombineMode::Add;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combination")
    bool bNormalizeResult = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combination")
    float CombinationStrength = 1.0f;

    // Masking
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Masking")
    bool bUseMask = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Masking", meta = (EditCondition = "bUseMask"))
    FAuracronPCGNoiseDescriptor MaskDescriptor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Masking", meta = (EditCondition = "bUseMask"))
    bool bInvertMask = false;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString OutputAttribute = TEXT("CombinedNoise");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputIndividualNoises = false;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGNoiseCombinerElement, UAuracronPCGNoiseCombinerSettings)

// =============================================================================
// NOISE FIELD GENERATOR
// =============================================================================

/**
 * Noise Field Generator
 * Generates 2D/3D noise fields for various applications
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGNoiseFieldGeneratorSettings, FAuracronPCGNoiseFieldGeneratorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGNoiseFieldGeneratorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGNoiseFieldGeneratorSettings();

    // Field settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field")
    FVector FieldSize = FVector(1000.0f, 1000.0f, 1000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field")
    FIntVector Resolution = FIntVector(64, 64, 64);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Field")
    FVector FieldCenter = FVector::ZeroVector;

    // Noise configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    FAuracronPCGNoiseDescriptor NoiseDescriptor;

    // Output format
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputAsTexture = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputAsVolumeTexture = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputAsPointCloud = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputAsHeightmap = false;

    // Filtering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterByThreshold = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByThreshold"))
    float Threshold = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByThreshold"))
    bool bInvertThreshold = false;

    // Performance
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseMultithreading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "16"))
    int32 ThreadCount = 4;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGNoiseFieldGeneratorElement, UAuracronPCGNoiseFieldGeneratorSettings)

// =============================================================================
// NOISE SYSTEM UTILITIES
// =============================================================================

/**
 * Noise System Utilities
 * Utility functions for advanced noise system operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGNoiseSystemUtils : public UObject
{
    GENERATED_BODY()

public:
    // Noise generation utilities
    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float GenerateNoise(const FVector& Position, const FAuracronPCGNoiseDescriptor& NoiseDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static FVector GenerateVectorNoise(const FVector& Position, const FAuracronPCGNoiseDescriptor& NoiseDescriptorX, const FAuracronPCGNoiseDescriptor& NoiseDescriptorY, const FAuracronPCGNoiseDescriptor& NoiseDescriptorZ);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float GeneratePerlinNoise(const FVector& Position, float Frequency, int32 Octaves, float Persistence, int32 Seed);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float GenerateSimplexNoise(const FVector& Position, float Frequency, int32 Octaves, float Persistence, int32 Seed);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float GenerateWorleyNoise(const FVector& Position, float Frequency, EAuracronPCGWorleyDistanceFunction DistanceFunction, float Jitter, int32 Seed);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float GenerateRidgeNoise(const FVector& Position, float Frequency, int32 Octaves, float Persistence, int32 Seed);

    // Randomization utilities
    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float GenerateRandomFloat(int32 Seed, float Min = 0.0f, float Max = 1.0f);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static FVector GenerateRandomVector(int32 Seed, const FVector& Min = FVector::ZeroVector, const FVector& Max = FVector::OneVector);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static int32 GenerateRandomInt(int32 Seed, int32 Min = 0, int32 Max = 100);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float GenerateGaussianRandom(int32 Seed, float Mean = 0.0f, float StdDev = 1.0f);

    // Noise combination utilities
    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float CombineNoiseValues(const TArray<float>& NoiseValues, const TArray<float>& Weights, EAuracronPCGNoiseCombineMode CombineMode);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float ApplyNoiseMask(float NoiseValue, float MaskValue, bool bInvertMask = false);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float RemapNoiseValue(float NoiseValue, float InputMin, float InputMax, float OutputMin, float OutputMax);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static float ApplyNoiseInterpolation(float Value, EAuracronPCGNoiseInterpolation InterpolationType);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static bool ValidateNoiseDescriptor(const FAuracronPCGNoiseDescriptor& NoiseDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Noise System Utils")
    static FAuracronPCGNoiseDescriptor CreateDefaultNoiseDescriptor(EAuracronPCGNoiseType NoiseType);
};

// Namespace for noise system utility functions
namespace AuracronPCGNoiseSystemUtils
{
    AURACRONPCGFRAMEWORK_API float PerlinNoise1D(float x, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float PerlinNoise2D(float x, float y, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float PerlinNoise3D(float x, float y, float z, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float PerlinNoise4D(float x, float y, float z, float w, int32 seed = 0);

    AURACRONPCGFRAMEWORK_API float SimplexNoise1D(float x, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float SimplexNoise2D(float x, float y, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float SimplexNoise3D(float x, float y, float z, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float SimplexNoise4D(float x, float y, float z, float w, int32 seed = 0);

    AURACRONPCGFRAMEWORK_API float WorleyNoise2D(float x, float y, EAuracronPCGWorleyDistanceFunction DistanceFunction, float Jitter, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float WorleyNoise3D(float x, float y, float z, EAuracronPCGWorleyDistanceFunction DistanceFunction, float Jitter, int32 seed = 0);

    AURACRONPCGFRAMEWORK_API float ValueNoise1D(float x, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float ValueNoise2D(float x, float y, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float ValueNoise3D(float x, float y, float z, int32 seed = 0);

    AURACRONPCGFRAMEWORK_API float GradientNoise2D(float x, float y, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API float GradientNoise3D(float x, float y, float z, int32 seed = 0);

    AURACRONPCGFRAMEWORK_API float FractalNoise(float x, float y, float z, const FAuracronPCGNoiseDescriptor& Descriptor);
    AURACRONPCGFRAMEWORK_API float DomainWarp(float x, float y, float z, const FAuracronPCGNoiseDescriptor& WarpDescriptor, float Strength);

    AURACRONPCGFRAMEWORK_API float InterpolateLinear(float a, float b, float t);
    AURACRONPCGFRAMEWORK_API float InterpolateHermite(float a, float b, float t);
    AURACRONPCGFRAMEWORK_API float InterpolateQuintic(float a, float b, float t);
    AURACRONPCGFRAMEWORK_API float InterpolateCosine(float a, float b, float t);

    AURACRONPCGFRAMEWORK_API uint32 Hash1D(uint32 x, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API uint32 Hash2D(uint32 x, uint32 y, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API uint32 Hash3D(uint32 x, uint32 y, uint32 z, int32 seed = 0);
    AURACRONPCGFRAMEWORK_API uint32 Hash4D(uint32 x, uint32 y, uint32 z, uint32 w, int32 seed = 0);

    AURACRONPCGFRAMEWORK_API float DistanceEuclidean(const FVector2D& a, const FVector2D& b);
    AURACRONPCGFRAMEWORK_API float DistanceManhattan(const FVector2D& a, const FVector2D& b);
    AURACRONPCGFRAMEWORK_API float DistanceChebyshev(const FVector2D& a, const FVector2D& b);
    AURACRONPCGFRAMEWORK_API float DistanceMinkowski(const FVector2D& a, const FVector2D& b, float p);

    AURACRONPCGFRAMEWORK_API void GenerateGradientTable(TArray<FVector>& GradientTable, int32 Size, int32 Seed);
    AURACRONPCGFRAMEWORK_API void GeneratePermutationTable(TArray<int32>& PermutationTable, int32 Size, int32 Seed);

    AURACRONPCGFRAMEWORK_API float ApplyFractal(float BaseNoise, EAuracronPCGFractalType FractalType, const FAuracronPCGNoiseDescriptor& Descriptor);
    AURACRONPCGFRAMEWORK_API float NormalizeNoise(float NoiseValue, float Min, float Max);
}
