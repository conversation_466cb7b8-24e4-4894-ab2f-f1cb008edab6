// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Landscape Integration Advanced Utilities Implementation
// Bridge 2.7: PCG Framework - Landscape Integration

#include "AuracronPCGLandscapeIntegration.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Landscape includes
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeDataAccess.h"
#include "LandscapeEdit.h"

// Engine includes
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"

namespace AuracronPCGLandscapeIntegrationUtils
{
    // =============================================================================
    // LANDSCAPE COMPONENT ACCESS
    // =============================================================================

    ULandscapeComponent* FindLandscapeComponentAtLocation(ALandscape* Landscape, const FVector& WorldLocation)
    {
        if (!Landscape)
        {
            return nullptr;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return nullptr;
        }

        // Convert world location to landscape coordinates
        FIntPoint LandscapeCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, WorldLocation);
        
        // Find the component that contains this coordinate
        return LandscapeInfo->XYtoComponentMap.FindRef(LandscapeCoord);
    }

    // =============================================================================
    // HEIGHT DATA ACCESS
    // =============================================================================

    bool GetLandscapeHeightData(ALandscape* Landscape, const FBox& Area, TArray<float>& OutHeightData, FIntPoint& OutDataSize)
    {
        if (!Landscape)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Convert area to landscape coordinates
        FIntPoint MinCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Min);
        FIntPoint MaxCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Max);

        // Calculate data size
        OutDataSize.X = MaxCoord.X - MinCoord.X + 1;
        OutDataSize.Y = MaxCoord.Y - MinCoord.Y + 1;

        // Allocate output array
        OutHeightData.SetNum(OutDataSize.X * OutDataSize.Y);

        // Access landscape data
        FLandscapeDataAccess DataAccess(LandscapeInfo);

        // Read height data
        for (int32 Y = 0; Y < OutDataSize.Y; Y++)
        {
            for (int32 X = 0; X < OutDataSize.X; X++)
            {
                int32 Index = Y * OutDataSize.X + X;
                int32 LandscapeX = MinCoord.X + X;
                int32 LandscapeY = MinCoord.Y + Y;

                uint16 HeightValue = 0;
                if (DataAccess.GetHeightData(LandscapeX, LandscapeY, HeightValue))
                {
                    OutHeightData[Index] = LandscapeDataAccess::GetLocalHeight(HeightValue);
                }
                else
                {
                    OutHeightData[Index] = 0.0f;
                }
            }
        }

        return true;
    }

    bool SetLandscapeHeightData(ALandscape* Landscape, const FBox& Area, const TArray<float>& HeightData, const FIntPoint& DataSize)
    {
        if (!Landscape || HeightData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Convert area to landscape coordinates
        FIntPoint MinCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Min);

        // Prepare height data for writing
        TArray<uint16> LandscapeHeightData;
        LandscapeHeightData.SetNum(HeightData.Num());

        for (int32 i = 0; i < HeightData.Num(); i++)
        {
            LandscapeHeightData[i] = LandscapeDataAccess::GetTexHeight(HeightData[i]);
        }

        // Write height data to landscape
        FLandscapeEditDataInterface LandscapeEdit(LandscapeInfo);
        LandscapeEdit.SetHeightData(MinCoord.X, MinCoord.Y, MinCoord.X + DataSize.X - 1, MinCoord.Y + DataSize.Y - 1, 
                                   LandscapeHeightData.GetData(), 0, true);

        return true;
    }

    // =============================================================================
    // LAYER DATA ACCESS
    // =============================================================================

    bool GetLandscapeLayerData(ALandscape* Landscape, const FString& LayerName, const FBox& Area, TArray<float>& OutLayerData, FIntPoint& OutDataSize)
    {
        if (!Landscape)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Find layer info object
        ULandscapeLayerInfoObject* LayerInfo = nullptr;
        for (const FLandscapeInfoLayerSettings& LayerSettings : LandscapeInfo->Layers)
        {
            if (LayerSettings.LayerInfoObj && LayerSettings.LayerName.ToString() == LayerName)
            {
                LayerInfo = LayerSettings.LayerInfoObj;
                break;
            }
        }

        if (!LayerInfo)
        {
            return false;
        }

        // Convert area to landscape coordinates
        FIntPoint MinCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Min);
        FIntPoint MaxCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Max);

        // Calculate data size
        OutDataSize.X = MaxCoord.X - MinCoord.X + 1;
        OutDataSize.Y = MaxCoord.Y - MinCoord.Y + 1;

        // Allocate output array
        OutLayerData.SetNum(OutDataSize.X * OutDataSize.Y);

        // Access landscape data
        FLandscapeDataAccess DataAccess(LandscapeInfo);

        // Read layer data
        for (int32 Y = 0; Y < OutDataSize.Y; Y++)
        {
            for (int32 X = 0; X < OutDataSize.X; X++)
            {
                int32 Index = Y * OutDataSize.X + X;
                int32 LandscapeX = MinCoord.X + X;
                int32 LandscapeY = MinCoord.Y + Y;

                uint8 WeightValue = 0;
                if (DataAccess.GetWeightData(LayerInfo, LandscapeX, LandscapeY, WeightValue))
                {
                    OutLayerData[Index] = static_cast<float>(WeightValue) / 255.0f;
                }
                else
                {
                    OutLayerData[Index] = 0.0f;
                }
            }
        }

        return true;
    }

    bool SetLandscapeLayerData(ALandscape* Landscape, const FString& LayerName, const FBox& Area, const TArray<float>& LayerData, const FIntPoint& DataSize)
    {
        if (!Landscape || LayerData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Find layer info object
        ULandscapeLayerInfoObject* LayerInfo = nullptr;
        for (const FLandscapeInfoLayerSettings& LayerSettings : LandscapeInfo->Layers)
        {
            if (LayerSettings.LayerInfoObj && LayerSettings.LayerName.ToString() == LayerName)
            {
                LayerInfo = LayerSettings.LayerInfoObj;
                break;
            }
        }

        if (!LayerInfo)
        {
            return false;
        }

        // Convert area to landscape coordinates
        FIntPoint MinCoord = UAuracronPCGLandscapeIntegrationUtils::WorldLocationToLandscapeCoordinate(Landscape, Area.Min);

        // Prepare layer data for writing
        TArray<uint8> LandscapeLayerData;
        LandscapeLayerData.SetNum(LayerData.Num());

        for (int32 i = 0; i < LayerData.Num(); i++)
        {
            LandscapeLayerData[i] = static_cast<uint8>(FMath::Clamp(LayerData[i] * 255.0f, 0.0f, 255.0f));
        }

        // Write layer data to landscape
        FLandscapeEditDataInterface LandscapeEdit(LandscapeInfo);
        LandscapeEdit.SetAlphaData(LayerInfo, MinCoord.X, MinCoord.Y, MinCoord.X + DataSize.X - 1, MinCoord.Y + DataSize.Y - 1, 
                                  LandscapeLayerData.GetData(), 0);

        return true;
    }

    // =============================================================================
    // ADVANCED CALCULATIONS
    // =============================================================================

    float CalculateLandscapeCurvature(ALandscape* Landscape, const FVector& WorldLocation, float SampleRadius)
    {
        if (!Landscape)
        {
            return 0.0f;
        }

        // Sample heights in a cross pattern
        float CenterHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation);
        float NorthHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, SampleRadius, 0.0f));
        float SouthHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, -SampleRadius, 0.0f));
        float EastHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(SampleRadius, 0.0f, 0.0f));
        float WestHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(-SampleRadius, 0.0f, 0.0f));

        // Calculate second derivatives (curvature)
        float CurvatureX = (EastHeight - 2.0f * CenterHeight + WestHeight) / (SampleRadius * SampleRadius);
        float CurvatureY = (NorthHeight - 2.0f * CenterHeight + SouthHeight) / (SampleRadius * SampleRadius);

        // Return mean curvature
        return (CurvatureX + CurvatureY) * 0.5f;
    }

    FVector CalculateLandscapeGradient(ALandscape* Landscape, const FVector& WorldLocation, float SampleRadius)
    {
        if (!Landscape)
        {
            return FVector::ZeroVector;
        }

        // Sample heights in cardinal directions
        float CenterHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation);
        float NorthHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, SampleRadius, 0.0f));
        float SouthHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(0.0f, -SampleRadius, 0.0f));
        float EastHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(SampleRadius, 0.0f, 0.0f));
        float WestHeight = UAuracronPCGLandscapeIntegrationUtils::SampleLandscapeHeight(Landscape, WorldLocation + FVector(-SampleRadius, 0.0f, 0.0f));

        // Calculate gradient
        float GradientX = (EastHeight - WestHeight) / (2.0f * SampleRadius);
        float GradientY = (NorthHeight - SouthHeight) / (2.0f * SampleRadius);

        return FVector(GradientX, GradientY, 0.0f);
    }

    // =============================================================================
    // VALIDATION AND OPTIMIZATION
    // =============================================================================

    bool ValidateLandscapeForModification(ALandscape* Landscape)
    {
        if (!Landscape)
        {
            return false;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return false;
        }

        // Check if landscape is valid for editing
        if (LandscapeInfo->LandscapeComponents.Num() == 0)
        {
            return false;
        }

        // Check if landscape is not read-only
        // Additional validation can be added here

        return true;
    }

    void OptimizeLandscapeAfterModification(ALandscape* Landscape, const FBox& ModifiedArea)
    {
        if (!Landscape)
        {
            return;
        }

        ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
        if (!LandscapeInfo)
        {
            return;
        }

        // Update collision for modified area
        // Find components that intersect with the modified area
        for (ULandscapeComponent* Component : LandscapeInfo->LandscapeComponents)
        {
            if (Component)
            {
                FBox ComponentBounds = Component->Bounds.GetBox();
                if (ComponentBounds.Intersect(ModifiedArea))
                {
                    // Update collision for this component
                    Component->UpdateCollisionData();
                }
            }
        }

        // Update navigation mesh if needed
        // This would typically be handled by the navigation system automatically
    }

    // =============================================================================
    // EROSION ALGORITHMS
    // =============================================================================

    bool PerformThermalErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor)
    {
        if (HeightData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }

        TArray<float> NewHeightData = HeightData;
        const float TalusAngleRadians = FMath::DegreesToRadians(ErosionDescriptor.TalusAngle);
        const float MaxSlope = FMath::Tan(TalusAngleRadians);

        for (int32 Iteration = 0; Iteration < ErosionDescriptor.Iterations; Iteration++)
        {
            for (int32 Y = 1; Y < DataSize.Y - 1; Y++)
            {
                for (int32 X = 1; X < DataSize.X - 1; X++)
                {
                    int32 Index = Y * DataSize.X + X;
                    float CenterHeight = HeightData[Index];

                    // Check all 8 neighbors
                    for (int32 DY = -1; DY <= 1; DY++)
                    {
                        for (int32 DX = -1; DX <= 1; DX++)
                        {
                            if (DX == 0 && DY == 0) continue;

                            int32 NeighborIndex = (Y + DY) * DataSize.X + (X + DX);
                            float NeighborHeight = HeightData[NeighborIndex];
                            float HeightDiff = CenterHeight - NeighborHeight;
                            float Distance = FMath::Sqrt(static_cast<float>(DX * DX + DY * DY));

                            if (HeightDiff > MaxSlope * Distance)
                            {
                                float Excess = HeightDiff - MaxSlope * Distance;
                                float Transfer = Excess * ErosionDescriptor.ThermalStrength * 0.5f;

                                NewHeightData[Index] -= Transfer;
                                NewHeightData[NeighborIndex] += Transfer;
                            }
                        }
                    }
                }
            }

            HeightData = NewHeightData;
        }

        return true;
    }

    bool PerformHydraulicErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor)
    {
        if (HeightData.Num() != DataSize.X * DataSize.Y)
        {
            return false;
        }

        // Initialize water and sediment maps
        TArray<float> WaterMap;
        TArray<float> SedimentMap;
        WaterMap.SetNumZeroed(HeightData.Num());
        SedimentMap.SetNumZeroed(HeightData.Num());

        for (int32 Iteration = 0; Iteration < ErosionDescriptor.Iterations; Iteration++)
        {
            // Add rain
            for (int32 i = 0; i < HeightData.Num(); i++)
            {
                WaterMap[i] += ErosionDescriptor.RainAmount;
            }

            // Flow simulation and erosion
            for (int32 Y = 1; Y < DataSize.Y - 1; Y++)
            {
                for (int32 X = 1; X < DataSize.X - 1; X++)
                {
                    int32 Index = Y * DataSize.X + X;
                    float TotalHeight = HeightData[Index] + WaterMap[Index];

                    // Find steepest descent
                    float MaxHeightDiff = 0.0f;
                    int32 FlowTarget = -1;

                    for (int32 DY = -1; DY <= 1; DY++)
                    {
                        for (int32 DX = -1; DX <= 1; DX++)
                        {
                            if (DX == 0 && DY == 0) continue;

                            int32 NeighborIndex = (Y + DY) * DataSize.X + (X + DX);
                            float NeighborTotalHeight = HeightData[NeighborIndex] + WaterMap[NeighborIndex];
                            float HeightDiff = TotalHeight - NeighborTotalHeight;

                            if (HeightDiff > MaxHeightDiff)
                            {
                                MaxHeightDiff = HeightDiff;
                                FlowTarget = NeighborIndex;
                            }
                        }
                    }

                    // Perform erosion and deposition
                    if (FlowTarget >= 0 && MaxHeightDiff > 0.0f)
                    {
                        float Velocity = FMath::Sqrt(MaxHeightDiff);
                        float Capacity = ErosionDescriptor.SedimentCapacity * Velocity;
                        float CurrentSediment = SedimentMap[Index];

                        if (CurrentSediment > Capacity)
                        {
                            // Deposition
                            float Deposition = (CurrentSediment - Capacity) * ErosionDescriptor.Strength;
                            HeightData[Index] += Deposition;
                            SedimentMap[Index] -= Deposition;
                        }
                        else
                        {
                            // Erosion
                            float Erosion = FMath::Min((Capacity - CurrentSediment) * ErosionDescriptor.Strength, HeightData[Index]);
                            HeightData[Index] -= Erosion;
                            SedimentMap[Index] += Erosion;
                        }

                        // Flow water and sediment
                        float WaterFlow = FMath::Min(WaterMap[Index], MaxHeightDiff);
                        WaterMap[Index] -= WaterFlow;
                        WaterMap[FlowTarget] += WaterFlow;

                        float SedimentFlow = SedimentMap[Index] * (WaterFlow / (WaterMap[Index] + WaterFlow));
                        SedimentMap[Index] -= SedimentFlow;
                        SedimentMap[FlowTarget] += SedimentFlow;
                    }
                }
            }

            // Evaporation
            for (int32 i = 0; i < WaterMap.Num(); i++)
            {
                WaterMap[i] *= (1.0f - ErosionDescriptor.Evaporation);
            }
        }

        return true;
    }

    bool PerformWindErosion(TArray<float>& HeightData, const FIntPoint& DataSize, const FAuracronPCGErosionDescriptor& ErosionDescriptor)
    {
        // Simplified wind erosion implementation
        // In production, this would be much more sophisticated
        
        FVector2D WindDir2D(ErosionDescriptor.WindDirection.X, ErosionDescriptor.WindDirection.Y);
        WindDir2D.Normalize();

        for (int32 Iteration = 0; Iteration < ErosionDescriptor.Iterations; Iteration++)
        {
            for (int32 Y = 1; Y < DataSize.Y - 1; Y++)
            {
                for (int32 X = 1; X < DataSize.X - 1; X++)
                {
                    int32 Index = Y * DataSize.X + X;
                    
                    // Calculate exposure to wind
                    float Exposure = 1.0f; // Simplified - would calculate based on surrounding heights
                    
                    // Apply wind erosion
                    float Erosion = ErosionDescriptor.WindStrength * Exposure * ErosionDescriptor.Strength;
                    HeightData[Index] -= Erosion;
                }
            }
        }

        return true;
    }

    // =============================================================================
    // BLENDING FUNCTIONS
    // =============================================================================

    float ApplyBlendMode(float BaseValue, float BlendValue, EAuracronPCGLandscapeBlendMode BlendMode, float BlendStrength)
    {
        float Result = BaseValue;

        switch (BlendMode)
        {
            case EAuracronPCGLandscapeBlendMode::Replace:
                Result = BlendValue;
                break;
            case EAuracronPCGLandscapeBlendMode::Add:
                Result = BaseValue + BlendValue;
                break;
            case EAuracronPCGLandscapeBlendMode::Subtract:
                Result = BaseValue - BlendValue;
                break;
            case EAuracronPCGLandscapeBlendMode::Multiply:
                Result = BaseValue * BlendValue;
                break;
            case EAuracronPCGLandscapeBlendMode::Screen:
                Result = 1.0f - (1.0f - BaseValue) * (1.0f - BlendValue);
                break;
            case EAuracronPCGLandscapeBlendMode::Overlay:
                if (BaseValue < 0.5f)
                    Result = 2.0f * BaseValue * BlendValue;
                else
                    Result = 1.0f - 2.0f * (1.0f - BaseValue) * (1.0f - BlendValue);
                break;
            default:
                Result = BlendValue;
                break;
        }

        return FMath::Lerp(BaseValue, Result, BlendStrength);
    }
}
