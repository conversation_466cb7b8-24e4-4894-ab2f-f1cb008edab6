// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Debug System Implementation
// Bridge 2.14: PCG Framework - Debugging Tools

#include "AuracronPCGDebugSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "PCGGraph.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Canvas.h"
#include "Engine/Engine.h"
#include "DrawDebugHelpers.h"
#include "HAL/IConsoleManager.h"
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// =============================================================================
// VISUAL DEBUGGER IMPLEMENTATION
// =============================================================================

EAuracronPCGDebugVisualizationMode UAuracronPCGVisualDebugger::CurrentVisualizationMode = EAuracronPCGDebugVisualizationMode::None;
bool UAuracronPCGVisualDebugger::bVisualizationEnabled = false;

void UAuracronPCGVisualDebugger::DrawPointData(UWorld* World, const UPCGPointData* PointData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !PointData || !bVisualizationEnabled)
    {
        return;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    
    for (int32 i = 0; i < Points.Num(); i++)
    {
        const FPCGPoint& Point = Points[i];
        FVector Location = Point.Transform.GetLocation();
        
        if (Descriptor.bShowPoints)
        {
            // Draw point as sphere
            DrawDebugSphere(World, Location, Descriptor.PointSize, 8, Descriptor.PointColor.ToFColor(true), 
                          Descriptor.bPersistentDisplay, Descriptor.DisplayDuration);
            
            // Draw point index if enabled
            if (Descriptor.bShowPointIndices)
            {
                FString IndexText = FString::FromInt(i);
                DrawDebugString(World, Location + FVector(0, 0, 20), IndexText, nullptr, 
                              Descriptor.PointColor.ToFColor(true), Descriptor.DisplayDuration, 
                              Descriptor.bWorldSpaceText);
            }
        }
    }
}

void UAuracronPCGVisualDebugger::DrawGraphConnections(UWorld* World, const UPCGGraph* Graph, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !Graph || !bVisualizationEnabled || !Descriptor.bShowConnections)
    {
        return;
    }

    // Simplified connection drawing - in production you'd iterate through actual graph connections
    // This is a placeholder implementation
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Drawing graph connections for debug visualization"));
}

void UAuracronPCGVisualDebugger::DrawBoundingBoxes(UWorld* World, const UPCGSpatialData* SpatialData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !SpatialData || !bVisualizationEnabled || !Descriptor.bShowBoundingBoxes)
    {
        return;
    }

    FBox Bounds = SpatialData->GetBounds();
    DrawDebugBox(World, Bounds.GetCenter(), Bounds.GetExtent(), Descriptor.BoundingBoxColor.ToFColor(true), 
                Descriptor.bPersistentDisplay, Descriptor.DisplayDuration, 0, Descriptor.ConnectionThickness);
}

void UAuracronPCGVisualDebugger::DrawAttributes(UWorld* World, const UPCGPointData* PointData, const TArray<FString>& AttributeNames, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !PointData || !bVisualizationEnabled || !Descriptor.bShowAttributes)
    {
        return;
    }

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    const UPCGMetadata* Metadata = PointData->Metadata;
    
    if (!Metadata)
    {
        return;
    }

    for (int32 i = 0; i < Points.Num(); i++)
    {
        const FPCGPoint& Point = Points[i];
        FVector Location = Point.Transform.GetLocation();
        
        FString AttributeText;
        for (const FString& AttributeName : AttributeNames)
        {
            // Simplified attribute display - in production you'd get actual attribute values
            AttributeText += FString::Printf(TEXT("%s: %.2f\n"), *AttributeName, Point.Density);
        }
        
        if (!AttributeText.IsEmpty())
        {
            DrawDebugString(World, Location + FVector(0, 0, 30), AttributeText, nullptr, 
                          FColor::White, Descriptor.DisplayDuration, Descriptor.bWorldSpaceText);
        }
    }
}

void UAuracronPCGVisualDebugger::DrawPerformanceInfo(UWorld* World, const FVector& Location, const TMap<FString, float>& PerformanceData, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !bVisualizationEnabled || !Descriptor.bShowPerformanceInfo)
    {
        return;
    }

    FString PerformanceText;
    for (const auto& DataPair : PerformanceData)
    {
        PerformanceText += FString::Printf(TEXT("%s: %.3f\n"), *DataPair.Key, DataPair.Value);
    }
    
    if (!PerformanceText.IsEmpty())
    {
        DrawDebugString(World, Location, PerformanceText, nullptr, FColor::Yellow, 
                      Descriptor.DisplayDuration, Descriptor.bWorldSpaceText);
    }
}

void UAuracronPCGVisualDebugger::DrawDataFlow(UWorld* World, const UPCGGraph* Graph, float AnimationTime, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !Graph || !bVisualizationEnabled || !Descriptor.bShowDataFlow)
    {
        return;
    }

    // Simplified data flow visualization - in production you'd animate data flow between nodes
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Drawing data flow animation at time %.3f"), AnimationTime);
}

void UAuracronPCGVisualDebugger::DrawErrors(UWorld* World, const TArray<FString>& ErrorMessages, const TArray<FVector>& ErrorLocations, const FAuracronPCGDebugVisualizationDescriptor& Descriptor)
{
    if (!World || !bVisualizationEnabled || !Descriptor.bShowErrors)
    {
        return;
    }

    for (int32 i = 0; i < FMath::Min(ErrorMessages.Num(), ErrorLocations.Num()); i++)
    {
        const FString& ErrorMessage = ErrorMessages[i];
        const FVector& ErrorLocation = ErrorLocations[i];
        
        // Draw error marker
        DrawDebugSphere(World, ErrorLocation, 20.0f, 8, Descriptor.ErrorColor.ToFColor(true), 
                      Descriptor.bPersistentDisplay, Descriptor.DisplayDuration);
        
        // Draw error message
        DrawDebugString(World, ErrorLocation + FVector(0, 0, 40), ErrorMessage, nullptr, 
                      Descriptor.ErrorColor.ToFColor(true), Descriptor.DisplayDuration, 
                      Descriptor.bWorldSpaceText);
    }
}

void UAuracronPCGVisualDebugger::ClearDebugDisplay(UWorld* World)
{
    if (World)
    {
        FlushDebugStrings(World);
        FlushPersistentDebugLines(World);
    }
}

void UAuracronPCGVisualDebugger::SetVisualizationMode(EAuracronPCGDebugVisualizationMode Mode)
{
    CurrentVisualizationMode = Mode;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug visualization mode set to: %d"), static_cast<int32>(Mode));
}

EAuracronPCGDebugVisualizationMode UAuracronPCGVisualDebugger::GetVisualizationMode()
{
    return CurrentVisualizationMode;
}

void UAuracronPCGVisualDebugger::ToggleVisualization(bool bEnabled)
{
    bVisualizationEnabled = bEnabled;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Debug visualization %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronPCGVisualDebugger::IsVisualizationEnabled()
{
    return bVisualizationEnabled;
}

// =============================================================================
// PERFORMANCE PROFILER IMPLEMENTATION
// =============================================================================

bool UAuracronPCGPerformanceProfiler::bIsProfilingActive = false;
FAuracronPCGDebugProfilingDescriptor UAuracronPCGPerformanceProfiler::CurrentDescriptor;
TMap<FString, TArray<float>> UAuracronPCGPerformanceProfiler::ExecutionTimeHistory;
TMap<FString, TArray<float>> UAuracronPCGPerformanceProfiler::MemoryUsageHistory;
TMap<FString, float> UAuracronPCGPerformanceProfiler::CustomMetrics;

void UAuracronPCGPerformanceProfiler::StartProfiling(const FAuracronPCGDebugProfilingDescriptor& Descriptor)
{
    CurrentDescriptor = Descriptor;
    bIsProfilingActive = true;
    
    // Clear previous data if not keeping history
    if (!Descriptor.bKeepProfilingHistory)
    {
        ClearProfilingData();
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling started"));
}

void UAuracronPCGPerformanceProfiler::StopProfiling()
{
    bIsProfilingActive = false;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling stopped"));
    
    // Auto-save if enabled
    if (CurrentDescriptor.bAutoSaveProfilingData)
    {
        FString FilePath = CurrentDescriptor.ExportDirectory / TEXT("AutoSave_") + FDateTime::Now().ToString() + TEXT(".csv");
        ExportProfilingData(FilePath, true);
    }
}

void UAuracronPCGPerformanceProfiler::PauseProfiling()
{
    if (bIsProfilingActive)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling paused"));
    }
}

void UAuracronPCGPerformanceProfiler::ResumeProfiling()
{
    if (bIsProfilingActive)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling resumed"));
    }
}

bool UAuracronPCGPerformanceProfiler::IsProfilingActive()
{
    return bIsProfilingActive;
}

void UAuracronPCGPerformanceProfiler::RecordExecutionTime(const FString& NodeName, float ExecutionTime)
{
    if (!bIsProfilingActive)
    {
        return;
    }

    if (!ExecutionTimeHistory.Contains(NodeName))
    {
        ExecutionTimeHistory.Add(NodeName, TArray<float>());
    }
    
    TArray<float>& TimeHistory = ExecutionTimeHistory[NodeName];
    TimeHistory.Add(ExecutionTime);
    
    // Limit history size
    if (TimeHistory.Num() > CurrentDescriptor.MaxHistoryEntries)
    {
        TimeHistory.RemoveAt(0);
    }
    
    // Check threshold
    if (CurrentDescriptor.bAlertOnThresholdExceeded && ExecutionTime > CurrentDescriptor.SlowExecutionThreshold)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Node '%s' exceeded execution time threshold: %.3fms"), *NodeName, ExecutionTime);
    }
}

void UAuracronPCGPerformanceProfiler::RecordMemoryUsage(const FString& NodeName, float MemoryUsage)
{
    if (!bIsProfilingActive)
    {
        return;
    }

    if (!MemoryUsageHistory.Contains(NodeName))
    {
        MemoryUsageHistory.Add(NodeName, TArray<float>());
    }
    
    TArray<float>& MemoryHistory = MemoryUsageHistory[NodeName];
    MemoryHistory.Add(MemoryUsage);
    
    // Limit history size
    if (MemoryHistory.Num() > CurrentDescriptor.MaxHistoryEntries)
    {
        MemoryHistory.RemoveAt(0);
    }
    
    // Check threshold
    if (CurrentDescriptor.bAlertOnThresholdExceeded && MemoryUsage > CurrentDescriptor.HighMemoryThreshold)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Node '%s' exceeded memory usage threshold: %.3fMB"), *NodeName, MemoryUsage);
    }
}

void UAuracronPCGPerformanceProfiler::RecordDataTransfer(const FString& FromNode, const FString& ToNode, int32 DataSize)
{
    if (!bIsProfilingActive)
    {
        return;
    }

    FString TransferKey = FString::Printf(TEXT("%s->%s"), *FromNode, *ToNode);
    CustomMetrics.Add(TransferKey, static_cast<float>(DataSize));
}

void UAuracronPCGPerformanceProfiler::RecordCustomMetric(const FString& MetricName, float Value)
{
    if (!bIsProfilingActive)
    {
        return;
    }

    CustomMetrics.Add(MetricName, Value);
}

TMap<FString, float> UAuracronPCGPerformanceProfiler::GetExecutionTimes()
{
    TMap<FString, float> AverageTimes;
    
    for (const auto& HistoryPair : ExecutionTimeHistory)
    {
        const TArray<float>& Times = HistoryPair.Value;
        if (Times.Num() > 0)
        {
            float Average = 0.0f;
            for (float Time : Times)
            {
                Average += Time;
            }
            Average /= Times.Num();
            AverageTimes.Add(HistoryPair.Key, Average);
        }
    }
    
    return AverageTimes;
}

TMap<FString, float> UAuracronPCGPerformanceProfiler::GetMemoryUsage()
{
    TMap<FString, float> AverageMemory;
    
    for (const auto& HistoryPair : MemoryUsageHistory)
    {
        const TArray<float>& Memory = HistoryPair.Value;
        if (Memory.Num() > 0)
        {
            float Average = 0.0f;
            for (float Mem : Memory)
            {
                Average += Mem;
            }
            Average /= Memory.Num();
            AverageMemory.Add(HistoryPair.Key, Average);
        }
    }
    
    return AverageMemory;
}

TMap<FString, float> UAuracronPCGPerformanceProfiler::GetCustomMetrics()
{
    return CustomMetrics;
}

float UAuracronPCGPerformanceProfiler::GetTotalExecutionTime()
{
    float TotalTime = 0.0f;
    TMap<FString, float> AverageTimes = GetExecutionTimes();
    
    for (const auto& TimePair : AverageTimes)
    {
        TotalTime += TimePair.Value;
    }
    
    return TotalTime;
}

float UAuracronPCGPerformanceProfiler::GetPeakMemoryUsage()
{
    float PeakMemory = 0.0f;
    
    for (const auto& HistoryPair : MemoryUsageHistory)
    {
        const TArray<float>& Memory = HistoryPair.Value;
        for (float Mem : Memory)
        {
            PeakMemory = FMath::Max(PeakMemory, Mem);
        }
    }
    
    return PeakMemory;
}

TArray<FString> UAuracronPCGPerformanceProfiler::GetBottleneckNodes(float Threshold)
{
    TArray<FString> BottleneckNodes;
    TMap<FString, float> AverageTimes = GetExecutionTimes();
    
    for (const auto& TimePair : AverageTimes)
    {
        if (TimePair.Value > Threshold)
        {
            BottleneckNodes.Add(TimePair.Key);
        }
    }
    
    return BottleneckNodes;
}

TArray<FString> UAuracronPCGPerformanceProfiler::GetHighMemoryNodes(float Threshold)
{
    TArray<FString> HighMemoryNodes;
    TMap<FString, float> AverageMemory = GetMemoryUsage();
    
    for (const auto& MemoryPair : AverageMemory)
    {
        if (MemoryPair.Value > Threshold)
        {
            HighMemoryNodes.Add(MemoryPair.Key);
        }
    }
    
    return HighMemoryNodes;
}

FString UAuracronPCGPerformanceProfiler::GeneratePerformanceReport()
{
    FString Report = TEXT("=== PCG Performance Report ===\n\n");
    
    // Execution times
    Report += TEXT("Execution Times (Average):\n");
    TMap<FString, float> AverageTimes = GetExecutionTimes();
    for (const auto& TimePair : AverageTimes)
    {
        Report += FString::Printf(TEXT("  %s: %.3fms\n"), *TimePair.Key, TimePair.Value);
    }
    
    // Memory usage
    Report += TEXT("\nMemory Usage (Average):\n");
    TMap<FString, float> AverageMemory = GetMemoryUsage();
    for (const auto& MemoryPair : AverageMemory)
    {
        Report += FString::Printf(TEXT("  %s: %.3fMB\n"), *MemoryPair.Key, MemoryPair.Value);
    }
    
    // Summary
    Report += FString::Printf(TEXT("\nSummary:\n"));
    Report += FString::Printf(TEXT("  Total Execution Time: %.3fms\n"), GetTotalExecutionTime());
    Report += FString::Printf(TEXT("  Peak Memory Usage: %.3fMB\n"), GetPeakMemoryUsage());
    
    // Bottlenecks
    TArray<FString> Bottlenecks = GetBottleneckNodes(CurrentDescriptor.SlowExecutionThreshold);
    if (Bottlenecks.Num() > 0)
    {
        Report += TEXT("\nBottleneck Nodes:\n");
        for (const FString& Node : Bottlenecks)
        {
            Report += FString::Printf(TEXT("  %s\n"), *Node);
        }
    }
    
    return Report;
}

bool UAuracronPCGPerformanceProfiler::ExportProfilingData(const FString& FilePath, bool bCSVFormat)
{
    FString ExportData;
    
    if (bCSVFormat)
    {
        // CSV format
        ExportData = TEXT("Node,Average Execution Time (ms),Average Memory Usage (MB)\n");
        
        TMap<FString, float> AverageTimes = GetExecutionTimes();
        TMap<FString, float> AverageMemory = GetMemoryUsage();
        
        TSet<FString> AllNodes;
        AverageTimes.GetKeys(AllNodes);
        AverageMemory.GetKeys(AllNodes);
        
        for (const FString& Node : AllNodes)
        {
            float ExecTime = AverageTimes.Contains(Node) ? AverageTimes[Node] : 0.0f;
            float MemUsage = AverageMemory.Contains(Node) ? AverageMemory[Node] : 0.0f;
            ExportData += FString::Printf(TEXT("%s,%.3f,%.3f\n"), *Node, ExecTime, MemUsage);
        }
    }
    else
    {
        // JSON format
        ExportData = GeneratePerformanceReport();
    }
    
    return FFileHelper::SaveStringToFile(ExportData, *FilePath);
}

void UAuracronPCGPerformanceProfiler::ClearProfilingData()
{
    ExecutionTimeHistory.Empty();
    MemoryUsageHistory.Empty();
    CustomMetrics.Empty();
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("PCG Performance profiling data cleared"));
}

// =============================================================================
// DATA INSPECTOR IMPLEMENTATION
// =============================================================================

FString UAuracronPCGDataInspector::InspectPointData(const UPCGPointData* PointData, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!PointData)
    {
        return TEXT("Invalid point data");
    }

    FString Report = TEXT("=== Point Data Inspection ===\n\n");

    const TArray<FPCGPoint>& Points = PointData->GetPoints();
    Report += FString::Printf(TEXT("Point Count: %d\n"), Points.Num());

    if (Points.Num() > 0)
    {
        // Analyze point distribution
        FVector MinLocation = Points[0].Transform.GetLocation();
        FVector MaxLocation = MinLocation;
        float MinDensity = Points[0].Density;
        float MaxDensity = MinDensity;

        for (const FPCGPoint& Point : Points)
        {
            FVector Location = Point.Transform.GetLocation();
            MinLocation = FVector::Min(MinLocation, Location);
            MaxLocation = FVector::Max(MaxLocation, Location);
            MinDensity = FMath::Min(MinDensity, Point.Density);
            MaxDensity = FMath::Max(MaxDensity, Point.Density);
        }

        Report += FString::Printf(TEXT("Bounds: Min(%.2f, %.2f, %.2f) Max(%.2f, %.2f, %.2f)\n"),
                                MinLocation.X, MinLocation.Y, MinLocation.Z,
                                MaxLocation.X, MaxLocation.Y, MaxLocation.Z);
        Report += FString::Printf(TEXT("Density Range: %.3f - %.3f\n"), MinDensity, MaxDensity);
    }

    // Inspect metadata if available
    if (PointData->Metadata && Descriptor.bInspectMetadata)
    {
        Report += TEXT("\nMetadata:\n");
        Report += InspectMetadata(PointData->Metadata, Descriptor);
    }

    return Report;
}

FString UAuracronPCGDataInspector::InspectSpatialData(const UPCGSpatialData* SpatialData, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!SpatialData)
    {
        return TEXT("Invalid spatial data");
    }

    FString Report = TEXT("=== Spatial Data Inspection ===\n\n");

    FBox Bounds = SpatialData->GetBounds();
    Report += FString::Printf(TEXT("Bounds: Center(%.2f, %.2f, %.2f) Extent(%.2f, %.2f, %.2f)\n"),
                            Bounds.GetCenter().X, Bounds.GetCenter().Y, Bounds.GetCenter().Z,
                            Bounds.GetExtent().X, Bounds.GetExtent().Y, Bounds.GetExtent().Z);

    // Calculate volume
    float Volume = Bounds.GetVolume();
    Report += FString::Printf(TEXT("Volume: %.2f cubic units\n"), Volume);

    return Report;
}

FString UAuracronPCGDataInspector::InspectMetadata(const UPCGMetadata* Metadata, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!Metadata)
    {
        return TEXT("No metadata available");
    }

    FString Report = TEXT("Metadata Attributes:\n");

    // Simplified metadata inspection - in production you'd iterate through actual attributes
    Report += TEXT("  (Metadata inspection not fully implemented in this example)\n");

    return Report;
}

FString UAuracronPCGDataInspector::InspectGraph(const UPCGGraph* Graph, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!Graph)
    {
        return TEXT("Invalid graph");
    }

    FString Report = TEXT("=== Graph Inspection ===\n\n");

    // Simplified graph inspection - in production you'd analyze actual graph structure
    Report += TEXT("Graph structure inspection not fully implemented in this example\n");

    return Report;
}

FString UAuracronPCGDataInspector::InspectNode(const UPCGSettings* NodeSettings, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    if (!NodeSettings)
    {
        return TEXT("Invalid node settings");
    }

    FString Report = TEXT("=== Node Inspection ===\n\n");

    Report += FString::Printf(TEXT("Node Class: %s\n"), *NodeSettings->GetClass()->GetName());

    // Simplified node inspection - in production you'd analyze actual node properties
    Report += TEXT("Node property inspection not fully implemented in this example\n");

    return Report;
}

TArray<FString> UAuracronPCGDataInspector::ValidateDataIntegrity(const UPCGData* Data)
{
    TArray<FString> ValidationErrors;

    if (!Data)
    {
        ValidationErrors.Add(TEXT("Data is null"));
        return ValidationErrors;
    }

    // Simplified validation - in production you'd perform comprehensive data integrity checks
    if (const UPCGPointData* PointData = Cast<UPCGPointData>(Data))
    {
        const TArray<FPCGPoint>& Points = PointData->GetPoints();

        for (int32 i = 0; i < Points.Num(); i++)
        {
            const FPCGPoint& Point = Points[i];

            // Check for invalid density values
            if (Point.Density < 0.0f || Point.Density > 1.0f)
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid density: %.3f"), i, Point.Density));
            }

            // Check for invalid transforms
            if (!Point.Transform.IsValid())
            {
                ValidationErrors.Add(FString::Printf(TEXT("Point %d has invalid transform"), i));
            }
        }
    }

    return ValidationErrors;
}

TArray<FString> UAuracronPCGDataInspector::ValidateGraphConnections(const UPCGGraph* Graph)
{
    TArray<FString> ValidationErrors;

    if (!Graph)
    {
        ValidationErrors.Add(TEXT("Graph is null"));
        return ValidationErrors;
    }

    // Simplified validation - in production you'd validate actual graph connections
    ValidationErrors.Add(TEXT("Graph connection validation not fully implemented in this example"));

    return ValidationErrors;
}

TArray<FString> UAuracronPCGDataInspector::CheckForMemoryLeaks(const UPCGGraph* Graph)
{
    TArray<FString> MemoryIssues;

    if (!Graph)
    {
        MemoryIssues.Add(TEXT("Graph is null"));
        return MemoryIssues;
    }

    // Simplified memory leak detection - in production you'd perform actual memory analysis
    MemoryIssues.Add(TEXT("Memory leak detection not fully implemented in this example"));

    return MemoryIssues;
}

TMap<FString, int32> UAuracronPCGDataInspector::AnalyzeDataTypes(const UPCGData* Data)
{
    TMap<FString, int32> DataTypeCount;

    if (!Data)
    {
        return DataTypeCount;
    }

    FString DataType = Data->GetClass()->GetName();
    DataTypeCount.Add(DataType, 1);

    return DataTypeCount;
}

TMap<FString, float> UAuracronPCGDataInspector::AnalyzeAttributeDistribution(const UPCGPointData* PointData, const FString& AttributeName)
{
    TMap<FString, float> Distribution;

    if (!PointData)
    {
        return Distribution;
    }

    // Simplified attribute analysis - in production you'd analyze actual attribute values
    Distribution.Add(TEXT("Min"), 0.0f);
    Distribution.Add(TEXT("Max"), 1.0f);
    Distribution.Add(TEXT("Average"), 0.5f);
    Distribution.Add(TEXT("StdDev"), 0.25f);

    return Distribution;
}

FString UAuracronPCGDataInspector::GenerateDataReport(const UPCGData* Data, const FAuracronPCGDebugInspectionDescriptor& Descriptor)
{
    FString Report = TEXT("=== Data Report ===\n\n");

    if (const UPCGPointData* PointData = Cast<UPCGPointData>(Data))
    {
        Report += InspectPointData(PointData, Descriptor);
    }
    else if (const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(Data))
    {
        Report += InspectSpatialData(SpatialData, Descriptor);
    }
    else
    {
        Report += TEXT("Unknown data type\n");
    }

    // Add validation results
    TArray<FString> ValidationErrors = ValidateDataIntegrity(Data);
    if (ValidationErrors.Num() > 0)
    {
        Report += TEXT("\nValidation Errors:\n");
        for (const FString& Error : ValidationErrors)
        {
            Report += FString::Printf(TEXT("  %s\n"), *Error);
        }
    }
    else
    {
        Report += TEXT("\nNo validation errors found\n");
    }

    return Report;
}

int32 UAuracronPCGDataInspector::CountPoints(const UPCGPointData* PointData)
{
    if (!PointData)
    {
        return 0;
    }

    return PointData->GetPoints().Num();
}

TArray<FString> UAuracronPCGDataInspector::GetAttributeNames(const UPCGPointData* PointData)
{
    TArray<FString> AttributeNames;

    if (!PointData || !PointData->Metadata)
    {
        return AttributeNames;
    }

    // Simplified attribute name extraction - in production you'd get actual attribute names
    AttributeNames.Add(TEXT("Density"));
    AttributeNames.Add(TEXT("Color"));
    AttributeNames.Add(TEXT("Position"));

    return AttributeNames;
}

FBox UAuracronPCGDataInspector::GetDataBounds(const UPCGSpatialData* SpatialData)
{
    if (!SpatialData)
    {
        return FBox(ForceInit);
    }

    return SpatialData->GetBounds();
}

float UAuracronPCGDataInspector::CalculateDataSize(const UPCGData* Data)
{
    if (!Data)
    {
        return 0.0f;
    }

    // Simplified size calculation - in production you'd calculate actual memory footprint
    if (const UPCGPointData* PointData = Cast<UPCGPointData>(Data))
    {
        int32 PointCount = PointData->GetPoints().Num();
        return static_cast<float>(PointCount * sizeof(FPCGPoint)) / 1024.0f; // KB
    }

    return 0.0f;
}
