// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - LOD System Implementation
// Bridge 2.12: PCG Framework - LOD e Optimization

#include "AuracronPCGLODSystem.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"
#include "AuracronPCGElementLibrary.h"

// PCG includes
#include "PCGContext.h"
#include "PCGComponent.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGMetadata.h"
#include "Helpers/PCGHelpers.h"
#include "Helpers/PCGAsync.h"

// Engine includes
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Engine/LODActor.h"
#include "Async/ParallelFor.h"
#include "RenderingThread.h"
#include "Stats/Stats.h"

// =============================================================================
// LOD GENERATOR IMPLEMENTATION
// =============================================================================

UAuracronPCGLODGeneratorSettings::UAuracronPCGLODGeneratorSettings()
{
    NodeMetadata.NodeName = TEXT("LOD Generator");
    NodeMetadata.NodeDescription = TEXT("Generates Level of Detail meshes and configurations");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("LOD"));
    NodeMetadata.Tags.Add(TEXT("Optimization"));
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.Tags.Add(TEXT("Mesh"));
    NodeMetadata.NodeColor = FLinearColor(0.2f, 0.8f, 0.6f);
}

void UAuracronPCGLODGeneratorSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (!bUseSourceMeshFromInput)
    {
        FPCGPinProperties& MeshPin = InputPins.Emplace_GetRef();
        MeshPin.Label = TEXT("Source Mesh");
        MeshPin.AllowedTypes = EPCGDataType::Attribute;
        MeshPin.bAdvancedPin = true;
    }
}

void UAuracronPCGLODGeneratorSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputLODMeshes)
    {
        FPCGPinProperties& LODPin = OutputPins.Emplace_GetRef();
        LODPin.Label = TEXT("LOD Meshes");
        LODPin.AllowedTypes = EPCGDataType::Attribute;
        LODPin.bAdvancedPin = true;
    }

    if (bOutputLODStatistics)
    {
        FPCGPinProperties& StatsPin = OutputPins.Emplace_GetRef();
        StatsPin.Label = TEXT("LOD Statistics");
        StatsPin.AllowedTypes = EPCGDataType::Attribute;
        StatsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGLODGeneratorElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                       FPCGDataCollection& OutputData, 
                                                                       const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGLODGeneratorSettings* Settings = GetTypedSettings<UAuracronPCGLODGeneratorSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for LOD Generator");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 LODsGenerated = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Generate LODs for each point
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];
                
                // Get source mesh for this point
                UStaticMesh* SourceMesh = GetSourceMeshForPoint(OutputPoint, Settings);
                
                if (SourceMesh)
                {
                    // Generate LOD chain
                    TArray<UStaticMesh*> LODMeshes = GenerateLODChainForPoint(SourceMesh, Settings);
                    
                    if (LODMeshes.Num() > 0)
                    {
                        ApplyLODToPoint(OutputPoint, LODMeshes, Settings);
                        LODsGenerated++;
                    }
                }

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("LOD Generator processed %d points, generated %d LOD chains"), 
                                  TotalProcessed, LODsGenerated);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("LOD Generator error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

UStaticMesh* FAuracronPCGLODGeneratorElement::GetSourceMeshForPoint(const FPCGPoint& Point, const UAuracronPCGLODGeneratorSettings* Settings) const
{
    if (Settings->bUseSourceMeshFromInput)
    {
        // In production, you'd extract mesh from point attributes
        // For now, return a default mesh if available
        return AuracronPCGLODSystemUtils::LoadMeshSafe(Settings->SourceMesh);
    }
    else
    {
        return AuracronPCGLODSystemUtils::LoadMeshSafe(Settings->SourceMesh);
    }
}

TArray<UStaticMesh*> FAuracronPCGLODGeneratorElement::GenerateLODChainForPoint(UStaticMesh* SourceMesh, const UAuracronPCGLODGeneratorSettings* Settings) const
{
    if (!SourceMesh)
    {
        return TArray<UStaticMesh*>();
    }

    // Use utility function for LOD generation
    return UAuracronPCGLODSystemUtils::GenerateLODChain(SourceMesh, Settings->LODDescriptor);
}

void FAuracronPCGLODGeneratorElement::ApplyLODToPoint(FPCGPoint& Point, const TArray<UStaticMesh*>& LODMeshes, const UAuracronPCGLODGeneratorSettings* Settings) const
{
    // Simplified LOD application - in production you'd use proper LOD assignment
    // Set LOD level as density
    Point.Density = FMath::Clamp(static_cast<float>(LODMeshes.Num()) / Settings->LODDescriptor.MaxLODLevels, 0.0f, 1.0f);
    
    // Set LOD visualization as color
    if (LODMeshes.Num() > 0)
    {
        float LODRatio = static_cast<float>(LODMeshes.Num()) / Settings->LODDescriptor.MaxLODLevels;
        Point.Color = FVector4(LODRatio, 1.0f - LODRatio, 0.5f, 1.0f);
    }
}

// =============================================================================
// DISTANCE BASED CULLER IMPLEMENTATION
// =============================================================================

UAuracronPCGDistanceBasedCullerSettings::UAuracronPCGDistanceBasedCullerSettings()
{
    NodeMetadata.NodeName = TEXT("Distance Based Culler");
    NodeMetadata.NodeDescription = TEXT("Culls instances based on distance and other criteria");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Culling"));
    NodeMetadata.Tags.Add(TEXT("Distance"));
    NodeMetadata.Tags.Add(TEXT("Optimization"));
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.6f, 0.2f);
}

void UAuracronPCGDistanceBasedCullerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;

    if (!bUseViewerLocation)
    {
        FPCGPinProperties& ReferencePin = InputPins.Emplace_GetRef();
        ReferencePin.Label = TEXT("Reference Location");
        ReferencePin.AllowedTypes = EPCGDataType::Attribute;
        ReferencePin.bAdvancedPin = true;
    }
}

void UAuracronPCGDistanceBasedCullerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputCullingInfo)
    {
        FPCGPinProperties& CullingPin = OutputPins.Emplace_GetRef();
        CullingPin.Label = TEXT("Culling Info");
        CullingPin.AllowedTypes = EPCGDataType::Attribute;
        CullingPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGDistanceBasedCullerElement::ProcessData(const FPCGDataCollection& InputData, 
                                                                              FPCGDataCollection& OutputData, 
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGDistanceBasedCullerSettings* Settings = GetTypedSettings<UAuracronPCGDistanceBasedCullerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Distance Based Culler");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 InstancesCulled = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Get reference location
        FVector ReferenceLocation = GetReferenceLocation(Settings);

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint> OutputPoints;
            OutputPoints.Reserve(InputPoints.Num());

            // Perform culling for each point
            for (const FPCGPoint& InputPoint : InputPoints)
            {
                bool bShouldCull = ShouldCullPoint(InputPoint, ReferenceLocation, Settings);
                
                if (!bShouldCull)
                {
                    FPCGPoint OutputPoint = InputPoint;
                    ApplyCullingInfoToPoint(OutputPoint, ReferenceLocation, Settings);
                    OutputPoints.Add(OutputPoint);
                }
                else
                {
                    InstancesCulled++;
                }
                
                TotalProcessed++;
            }

            OutputPointData->GetMutablePoints() = OutputPoints;
            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Distance Based Culler processed %d points, culled %d instances"), 
                                  TotalProcessed, InstancesCulled);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Distance Based Culler error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

FVector FAuracronPCGDistanceBasedCullerElement::GetReferenceLocation(const UAuracronPCGDistanceBasedCullerSettings* Settings) const
{
    if (Settings->bUseViewerLocation)
    {
        // In production, you'd get actual viewer/camera location
        // For now, return a default location
        return FVector::ZeroVector;
    }
    else
    {
        return Settings->ReferenceLocation;
    }
}

bool FAuracronPCGDistanceBasedCullerElement::ShouldCullPoint(const FPCGPoint& Point, const FVector& ReferenceLocation, const UAuracronPCGDistanceBasedCullerSettings* Settings) const
{
    FVector PointLocation = Point.Transform.GetLocation();
    
    // Use utility function for culling decision
    return UAuracronPCGLODSystemUtils::ShouldCullInstance(PointLocation, ReferenceLocation, Settings->CullingDescriptor);
}

void FAuracronPCGDistanceBasedCullerElement::ApplyCullingInfoToPoint(FPCGPoint& Point, const FVector& ReferenceLocation, const UAuracronPCGDistanceBasedCullerSettings* Settings) const
{
    if (Settings->bOutputCullingInfo)
    {
        FVector PointLocation = Point.Transform.GetLocation();
        float Distance = FVector::Dist(PointLocation, ReferenceLocation);
        
        // Set distance as density
        float NormalizedDistance = Distance / Settings->CullingDescriptor.MaxDrawDistance;
        Point.Density = FMath::Clamp(1.0f - NormalizedDistance, 0.0f, 1.0f);
        
        // Set culling visualization as color
        Point.Color = FVector4(NormalizedDistance, 1.0f - NormalizedDistance, 0.0f, 1.0f);
    }
}

// =============================================================================
// INSTANCING OPTIMIZER IMPLEMENTATION
// =============================================================================

UAuracronPCGInstancingOptimizerSettings::UAuracronPCGInstancingOptimizerSettings()
{
    NodeMetadata.NodeName = TEXT("Instancing Optimizer");
    NodeMetadata.NodeDescription = TEXT("Optimizes mesh instances for better performance");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Modifier;
    NodeMetadata.Tags.Add(TEXT("Instancing"));
    NodeMetadata.Tags.Add(TEXT("Optimization"));
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.Tags.Add(TEXT("Batching"));
    NodeMetadata.NodeColor = FLinearColor(0.6f, 0.2f, 0.8f);
}

void UAuracronPCGInstancingOptimizerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGInstancingOptimizerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputInstanceComponents)
    {
        FPCGPinProperties& ComponentsPin = OutputPins.Emplace_GetRef();
        ComponentsPin.Label = TEXT("Instance Components");
        ComponentsPin.AllowedTypes = EPCGDataType::Attribute;
        ComponentsPin.bAdvancedPin = true;
    }

    if (bOutputOptimizationStats)
    {
        FPCGPinProperties& StatsPin = OutputPins.Emplace_GetRef();
        StatsPin.Label = TEXT("Optimization Stats");
        StatsPin.AllowedTypes = EPCGDataType::Attribute;
        StatsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGInstancingOptimizerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                              FPCGDataCollection& OutputData,
                                                                              const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGInstancingOptimizerSettings* Settings = GetTypedSettings<UAuracronPCGInstancingOptimizerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Instancing Optimizer");
            return Result;
        }

        int32 TotalProcessed = 0;
        int32 InstancesOptimized = 0;
        TArray<UPCGPointData*> ProcessedPointData;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Group instances for optimization
            TMap<FString, TArray<FPCGPoint>> InstanceGroups = GroupInstancesForOptimization(InputPoints, Settings);

            // Optimize each group
            for (auto& GroupPair : InstanceGroups)
            {
                TArray<FPCGPoint> OptimizedInstances = OptimizeInstanceGroup(GroupPair.Value, Settings);

                for (FPCGPoint& OptimizedInstance : OptimizedInstances)
                {
                    ApplyOptimizationToPoint(OptimizedInstance, Settings);
                    OutputPoints.Add(OptimizedInstance);
                    InstancesOptimized++;
                }

                TotalProcessed += GroupPair.Value.Num();
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Instancing Optimizer processed %d points, optimized %d instances"),
                                  TotalProcessed, InstancesOptimized);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Instancing Optimizer error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

TMap<FString, TArray<FPCGPoint>> FAuracronPCGInstancingOptimizerElement::GroupInstancesForOptimization(const TArray<FPCGPoint>& Points, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    TMap<FString, TArray<FPCGPoint>> Groups;

    for (const FPCGPoint& Point : Points)
    {
        FString GroupKey = GenerateGroupKey(Point, Settings);

        if (!Groups.Contains(GroupKey))
        {
            Groups.Add(GroupKey, TArray<FPCGPoint>());
        }

        Groups[GroupKey].Add(Point);
    }

    return Groups;
}

FString FAuracronPCGInstancingOptimizerElement::GenerateGroupKey(const FPCGPoint& Point, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    FString GroupKey = TEXT("Default");

    // Group by mesh (simplified - in production you'd use actual mesh references)
    if (Settings->bGroupByMesh)
    {
        GroupKey += TEXT("_Mesh");
    }

    // Group by material (simplified - in production you'd use actual material references)
    if (Settings->bGroupByMaterial)
    {
        GroupKey += TEXT("_Material");
    }

    // Group by LOD level
    if (Settings->bGroupByLODLevel)
    {
        int32 LODLevel = FMath::FloorToInt(Point.Density * 4.0f); // Simplified LOD extraction
        GroupKey += FString::Printf(TEXT("_LOD%d"), LODLevel);
    }

    return GroupKey;
}

TArray<FPCGPoint> FAuracronPCGInstancingOptimizerElement::OptimizeInstanceGroup(const TArray<FPCGPoint>& GroupPoints, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    TArray<FPCGPoint> OptimizedPoints = GroupPoints;

    // Remove duplicate instances
    if (Settings->bRemoveDuplicateInstances)
    {
        OptimizedPoints = RemoveDuplicateInstances(OptimizedPoints, Settings->DuplicateThreshold);
    }

    // Optimize instance transforms
    if (Settings->bOptimizeInstanceTransforms)
    {
        OptimizeInstanceTransforms(OptimizedPoints, Settings);
    }

    // Sort instances by distance
    if (Settings->bSortInstancesByDistance)
    {
        SortInstancesByDistance(OptimizedPoints);
    }

    return OptimizedPoints;
}

TArray<FPCGPoint> FAuracronPCGInstancingOptimizerElement::RemoveDuplicateInstances(const TArray<FPCGPoint>& Points, float Threshold) const
{
    TArray<FPCGPoint> UniquePoints;

    for (const FPCGPoint& Point : Points)
    {
        bool bIsDuplicate = false;

        for (const FPCGPoint& UniquePoint : UniquePoints)
        {
            float Distance = FVector::Dist(Point.Transform.GetLocation(), UniquePoint.Transform.GetLocation());
            if (Distance < Threshold)
            {
                bIsDuplicate = true;
                break;
            }
        }

        if (!bIsDuplicate)
        {
            UniquePoints.Add(Point);
        }
    }

    return UniquePoints;
}

void FAuracronPCGInstancingOptimizerElement::OptimizeInstanceTransforms(TArray<FPCGPoint>& Points, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    // Extract transforms
    TArray<FTransform> Transforms;
    for (const FPCGPoint& Point : Points)
    {
        Transforms.Add(Point.Transform);
    }

    // Use utility function for optimization
    UAuracronPCGLODSystemUtils::OptimizeInstanceTransforms(Transforms, Settings->InstancingDescriptor.InstanceMergingThreshold);

    // Apply optimized transforms back to points
    for (int32 i = 0; i < Points.Num() && i < Transforms.Num(); i++)
    {
        Points[i].Transform = Transforms[i];
    }
}

void FAuracronPCGInstancingOptimizerElement::SortInstancesByDistance(TArray<FPCGPoint>& Points) const
{
    // Sort by distance from origin (simplified)
    Points.Sort([](const FPCGPoint& A, const FPCGPoint& B) -> bool
    {
        float DistanceA = A.Transform.GetLocation().Size();
        float DistanceB = B.Transform.GetLocation().Size();
        return DistanceA < DistanceB;
    });
}

void FAuracronPCGInstancingOptimizerElement::ApplyOptimizationToPoint(FPCGPoint& Point, const UAuracronPCGInstancingOptimizerSettings* Settings) const
{
    // Simplified optimization application - in production you'd create actual instanced components
    // Set optimization level as density
    Point.Density = FMath::Clamp(Point.Density * 1.1f, 0.0f, 1.0f); // Slight boost to indicate optimization

    // Set optimization visualization as color
    Point.Color = FVector4(0.0f, 1.0f, 0.0f, 1.0f); // Green for optimized
}

// =============================================================================
// PERFORMANCE PROFILER IMPLEMENTATION
// =============================================================================

UAuracronPCGPerformanceProfilerSettings::UAuracronPCGPerformanceProfilerSettings()
{
    NodeMetadata.NodeName = TEXT("Performance Profiler");
    NodeMetadata.NodeDescription = TEXT("Profiles and monitors performance metrics");
    NodeMetadata.Category = EAuracronPCGNodeCategory::Debug;
    NodeMetadata.Tags.Add(TEXT("Performance"));
    NodeMetadata.Tags.Add(TEXT("Profiling"));
    NodeMetadata.Tags.Add(TEXT("Monitoring"));
    NodeMetadata.Tags.Add(TEXT("Debug"));
    NodeMetadata.NodeColor = FLinearColor(0.8f, 0.8f, 0.2f);
}

void UAuracronPCGPerformanceProfilerSettings::ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const
{
    FPCGPinProperties& InputPin = InputPins.Emplace_GetRef();
    InputPin.Label = TEXT("Input");
    InputPin.AllowedTypes = EPCGDataType::Point;
    InputPin.bAllowMultipleConnections = true;
}

void UAuracronPCGPerformanceProfilerSettings::ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const
{
    FPCGPinProperties& OutputPin = OutputPins.Emplace_GetRef();
    OutputPin.Label = TEXT("Output");
    OutputPin.AllowedTypes = EPCGDataType::Point;

    if (bOutputPerformanceReport)
    {
        FPCGPinProperties& ReportPin = OutputPins.Emplace_GetRef();
        ReportPin.Label = TEXT("Performance Report");
        ReportPin.AllowedTypes = EPCGDataType::Attribute;
        ReportPin.bAdvancedPin = true;
    }

    if (bOutputRawMetrics)
    {
        FPCGPinProperties& MetricsPin = OutputPins.Emplace_GetRef();
        MetricsPin.Label = TEXT("Raw Metrics");
        MetricsPin.AllowedTypes = EPCGDataType::Attribute;
        MetricsPin.bAdvancedPin = true;
    }
}

FAuracronPCGElementResult FAuracronPCGPerformanceProfilerElement::ProcessData(const FPCGDataCollection& InputData,
                                                                               FPCGDataCollection& OutputData,
                                                                               const FAuracronPCGElementParams& Parameters) const
{
    AURACRON_PCG_SCOPE_CYCLE_COUNTER(STAT_AuracronPCG_ElementExecution);

    FAuracronPCGElementResult Result;
    Result.bSuccess = false;

    try
    {
        // Get settings
        const UAuracronPCGPerformanceProfilerSettings* Settings = GetTypedSettings<UAuracronPCGPerformanceProfilerSettings>(nullptr);
        if (!Settings)
        {
            Result.ErrorMessage = TEXT("Invalid settings for Performance Profiler");
            return Result;
        }

        // Start performance measurement
        double StartTime = FPlatformTime::Seconds();

        int32 TotalProcessed = 0;
        TArray<UPCGPointData*> ProcessedPointData;
        TMap<FString, float> PerformanceMetrics;

        // Process all input point data
        for (const FPCGTaggedData& TaggedData : InputData.GetInputs())
        {
            const UPCGPointData* InputPointData = Cast<UPCGPointData>(TaggedData.Data);
            if (!InputPointData)
            {
                continue;
            }

            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(InputPointData);

            const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();

            // Profile each point processing
            for (int32 i = 0; i < InputPoints.Num(); i++)
            {
                FPCGPoint OutputPoint = InputPoints[i];

                // Collect performance metrics for this point
                CollectPerformanceMetrics(OutputPoint, Settings, PerformanceMetrics);

                // Apply performance info to point
                ApplyPerformanceInfoToPoint(OutputPoint, PerformanceMetrics, Settings);

                OutputPoints[i] = OutputPoint;
                TotalProcessed++;
            }

            ProcessedPointData.Add(OutputPointData);
        }

        // End performance measurement
        double EndTime = FPlatformTime::Seconds();
        double ExecutionTime = EndTime - StartTime;

        // Add execution time to metrics
        PerformanceMetrics.Add(TEXT("ExecutionTime"), static_cast<float>(ExecutionTime));
        PerformanceMetrics.Add(TEXT("PointsPerSecond"), TotalProcessed / static_cast<float>(ExecutionTime));

        // Create output data
        for (UPCGPointData* PointData : ProcessedPointData)
        {
            if (PointData)
            {
                FPCGTaggedData& PointTaggedData = OutputData.TaggedData.Emplace_GetRef();
                PointTaggedData.Data = PointData;
                PointTaggedData.Pin = TEXT("Output");
            }
        }

        Result.bSuccess = true;
        Result.PointsProcessed = TotalProcessed;
        Result.OutputDataCount = OutputData.TaggedData.Num();

        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Performance Profiler processed %d points in %.3fs"),
                                  TotalProcessed, ExecutionTime);
    }
    catch (const std::exception& e)
    {
        Result.ErrorMessage = FString(UTF8_TO_TCHAR(e.what()));
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Performance Profiler error: %s"), *Result.ErrorMessage);
    }

    return Result;
}

void FAuracronPCGPerformanceProfilerElement::CollectPerformanceMetrics(const FPCGPoint& Point, const UAuracronPCGPerformanceProfilerSettings* Settings, TMap<FString, float>& OutMetrics) const
{
    // Collect various performance metrics
    for (EAuracronPCGPerformanceMetric Metric : Settings->ProfilingDescriptor.MetricsToTrack)
    {
        float MetricValue = 0.0f;

        switch (Metric)
        {
            case EAuracronPCGPerformanceMetric::RenderTime:
                MetricValue = MeasureRenderTime(Point);
                break;
            case EAuracronPCGPerformanceMetric::DrawCalls:
                MetricValue = static_cast<float>(CountDrawCalls(Point));
                break;
            case EAuracronPCGPerformanceMetric::TriangleCount:
                MetricValue = static_cast<float>(CountTriangles(Point));
                break;
            case EAuracronPCGPerformanceMetric::MemoryUsage:
                MetricValue = MeasureMemoryUsage(Point);
                break;
            case EAuracronPCGPerformanceMetric::InstanceCount:
                MetricValue = 1.0f; // Each point represents one instance
                break;
            case EAuracronPCGPerformanceMetric::ScreenSize:
                MetricValue = CalculateScreenSize(Point);
                break;
            default:
                MetricValue = 1.0f;
                break;
        }

        FString MetricName = UEnum::GetValueAsString(Metric);
        OutMetrics.Add(MetricName, MetricValue);
    }
}

float FAuracronPCGPerformanceProfilerElement::MeasureRenderTime(const FPCGPoint& Point) const
{
    // Simplified render time measurement - in production you'd use actual GPU timing
    return FMath::RandRange(0.1f, 2.0f);
}

int32 FAuracronPCGPerformanceProfilerElement::CountDrawCalls(const FPCGPoint& Point) const
{
    // Simplified draw call counting - in production you'd use actual render stats
    return FMath::RandRange(1, 5);
}

int32 FAuracronPCGPerformanceProfilerElement::CountTriangles(const FPCGPoint& Point) const
{
    // Simplified triangle counting - in production you'd use actual mesh data
    return FMath::RandRange(100, 1000);
}

float FAuracronPCGPerformanceProfilerElement::MeasureMemoryUsage(const FPCGPoint& Point) const
{
    // Simplified memory usage measurement - in production you'd use actual memory stats
    return FMath::RandRange(1.0f, 10.0f);
}

float FAuracronPCGPerformanceProfilerElement::CalculateScreenSize(const FPCGPoint& Point) const
{
    // Simplified screen size calculation - in production you'd use actual projection
    FVector Location = Point.Transform.GetLocation();
    float Distance = Location.Size();
    return FMath::Clamp(1000.0f / Distance, 0.01f, 1.0f);
}

void FAuracronPCGPerformanceProfilerElement::ApplyPerformanceInfoToPoint(FPCGPoint& Point, const TMap<FString, float>& PerformanceMetrics, const UAuracronPCGPerformanceProfilerSettings* Settings) const
{
    // Calculate overall performance score
    float PerformanceScore = CalculatePerformanceScore(PerformanceMetrics, Settings);

    // Set performance score as density
    Point.Density = FMath::Clamp(PerformanceScore, 0.0f, 1.0f);

    // Set performance visualization as color (green = good, red = bad)
    Point.Color = FVector4(1.0f - PerformanceScore, PerformanceScore, 0.0f, 1.0f);
}

float FAuracronPCGPerformanceProfilerElement::CalculatePerformanceScore(const TMap<FString, float>& PerformanceMetrics, const UAuracronPCGPerformanceProfilerSettings* Settings) const
{
    float Score = 1.0f;

    // Check against thresholds
    if (PerformanceMetrics.Contains(TEXT("RenderTime")))
    {
        float RenderTime = PerformanceMetrics[TEXT("RenderTime")];
        if (RenderTime > Settings->ProfilingDescriptor.MaxAcceptableRenderTime)
        {
            Score *= 0.5f;
        }
    }

    if (PerformanceMetrics.Contains(TEXT("DrawCalls")))
    {
        float DrawCalls = PerformanceMetrics[TEXT("DrawCalls")];
        if (DrawCalls > Settings->ProfilingDescriptor.MaxAcceptableDrawCalls)
        {
            Score *= 0.7f;
        }
    }

    if (PerformanceMetrics.Contains(TEXT("TriangleCount")))
    {
        float TriangleCount = PerformanceMetrics[TEXT("TriangleCount")];
        if (TriangleCount > Settings->ProfilingDescriptor.MaxAcceptableTriangles)
        {
            Score *= 0.8f;
        }
    }

    return FMath::Clamp(Score, 0.0f, 1.0f);
}
