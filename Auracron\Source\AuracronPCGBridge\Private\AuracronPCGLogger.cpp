// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Specialized Logger Implementation
// Bridge 2.1: PCG Framework - Core Infrastructure

#include "AuracronPCGLogger.h"
#include "AuracronPCGBridge.h"

// Engine includes
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "TimerManager.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// Global logger instance
static FAuracronPCGLogger* GGlobalPCGLogger = nullptr;

FAuracronPCGLogger::FAuracronPCGLogger()
{
    bIsInitialized = false;
    LogEntries.Reserve(1000); // Pre-allocate for performance
}

FAuracronPCGLogger::~FAuracronPCGLogger()
{
    Shutdown();
}

bool FAuracronPCGLogger::Initialize(const FAuracronPCGLogConfig& Config)
{
    if (bIsInitialized)
    {
        UE_LOG(LogAuracronPCGCore, Warning, TEXT("PCG Logger already initialized"));
        return true;
    }

    Configuration = Config;

    // Create log directory
    if (!CreateLogDirectory())
    {
        UE_LOG(LogAuracronPCGCore, Error, TEXT("Failed to create log directory"));
        return false;
    }

    // Initialize file logging if enabled
    if (Configuration.bEnableFileLogging)
    {
        CurrentLogFilePath = GenerateLogFileName();
        
        // Create log file
        LogFileArchive = TUniquePtr<FArchive>(IFileManager::Get().CreateFileWriter(*CurrentLogFilePath, FILEWRITE_Append));
        if (!LogFileArchive.IsValid())
        {
            UE_LOG(LogAuracronPCGCore, Error, TEXT("Failed to create log file: %s"), *CurrentLogFilePath);
            return false;
        }
    }

    // Clear existing data
    {
        FScopeLock Lock(&LogEntriesLock);
        LogEntries.Empty();
    }

    {
        FScopeLock Lock(&StatisticsLock);
        LogCountByLevel.Empty();
        LogCountByCategory.Empty();
    }

    // Start auto-flush timer if enabled
    if (Configuration.bAutoFlush)
    {
        StartAutoFlushTimer();
    }

    // Set global instance
    GGlobalPCGLogger = this;

    bIsInitialized = true;
    
    // Log initialization success
    LogInfo(TEXT("Core"), TEXT("AURACRON PCG Logger initialized successfully"), 
            FString::Printf(TEXT("File: %s"), *CurrentLogFilePath));

    return true;
}

void FAuracronPCGLogger::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    LogInfo(TEXT("Core"), TEXT("Shutting down AURACRON PCG Logger"));

    // Stop auto-flush timer
    StopAutoFlushTimer();

    // Flush any remaining logs
    FlushLogs();

    // Close log file
    {
        FScopeLock Lock(&FileOperationLock);
        if (LogFileArchive.IsValid())
        {
            LogFileArchive->Close();
            LogFileArchive.Reset();
        }
    }

    // Clear data
    {
        FScopeLock Lock(&LogEntriesLock);
        LogEntries.Empty();
    }

    {
        FScopeLock Lock(&StatisticsLock);
        LogCountByLevel.Empty();
        LogCountByCategory.Empty();
    }

    // Clear global instance
    if (GGlobalPCGLogger == this)
    {
        GGlobalPCGLogger = nullptr;
    }

    bIsInitialized = false;
}

void FAuracronPCGLogger::SetConfiguration(const FAuracronPCGLogConfig& NewConfig)
{
    Configuration = NewConfig;
    
    // Restart auto-flush timer if settings changed
    if (Configuration.bAutoFlush)
    {
        StopAutoFlushTimer();
        StartAutoFlushTimer();
    }
    else
    {
        StopAutoFlushTimer();
    }

    LogInfo(TEXT("Core"), TEXT("PCG Logger configuration updated"));
}

void FAuracronPCGLogger::Log(const FString& LogLevel, const FString& Category, const FString& Message, const FString& Context)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Create log entry
    FAuracronPCGLogEntry Entry(LogLevel, Category, SanitizeLogMessage(Message), Context);

    // Add to memory
    AddToMemory(Entry);

    // Write to file if enabled
    if (Configuration.bEnableFileLogging)
    {
        WriteToFile(Entry);
    }

    // Write to console if enabled
    if (Configuration.bEnableConsoleLogging)
    {
        WriteToConsole(Entry);
    }

    // Update statistics
    UpdateStatistics(Entry);

    // Check if log rotation is needed
    if (ShouldRotateLogFile())
    {
        PerformLogRotation();
    }
}

void FAuracronPCGLogger::LogError(const FAuracronPCGErrorInfo& ErrorInfo)
{
    FString Context = FString::Printf(TEXT("ErrorCode: %s, Source: %s, Line: %d"), 
                                     *UEnum::GetValueAsString(ErrorInfo.ErrorCode),
                                     *ErrorInfo.SourceElement,
                                     ErrorInfo.LineNumber);
    
    Log(TEXT("Error"), TEXT("PCGError"), ErrorInfo.ErrorMessage, Context);
}

void FAuracronPCGLogger::LogPerformance(const FAuracronPCGPerformanceMetrics& Metrics)
{
    if (!Configuration.bEnablePerformanceLogging)
    {
        return;
    }

    FString Message = FString::Printf(
        TEXT("Execution: %.3fs, Points: %d, Elements: %d, Memory: %.2fMB, CPU: %.1f%%, GPU: %.1f%%, Threads: %d"),
        Metrics.ExecutionTimeSeconds,
        Metrics.PointsGenerated,
        Metrics.ElementsExecuted,
        Metrics.MemoryUsageMB,
        Metrics.CPUUsagePercent,
        Metrics.GPUUsagePercent,
        Metrics.ThreadsUsed
    );

    Log(TEXT("Performance"), TEXT("Metrics"), Message);
}

void FAuracronPCGLogger::LogInfo(const FString& Category, const FString& Message, const FString& Context)
{
    Log(TEXT("Info"), Category, Message, Context);
}

void FAuracronPCGLogger::LogWarning(const FString& Category, const FString& Message, const FString& Context)
{
    Log(TEXT("Warning"), Category, Message, Context);
}

void FAuracronPCGLogger::LogError(const FString& Category, const FString& Message, const FString& Context)
{
    Log(TEXT("Error"), Category, Message, Context);
}

void FAuracronPCGLogger::LogVerbose(const FString& Category, const FString& Message, const FString& Context)
{
    if (Configuration.bEnableDetailedLogging)
    {
        Log(TEXT("Verbose"), Category, Message, Context);
    }
}

TArray<FAuracronPCGLogEntry> FAuracronPCGLogger::GetRecentLogs(int32 MaxEntries) const
{
    FScopeLock Lock(&LogEntriesLock);
    
    TArray<FAuracronPCGLogEntry> Result;
    int32 StartIndex = FMath::Max(0, LogEntries.Num() - MaxEntries);
    
    for (int32 i = StartIndex; i < LogEntries.Num(); ++i)
    {
        Result.Add(LogEntries[i]);
    }
    
    return Result;
}

TArray<FAuracronPCGLogEntry> FAuracronPCGLogger::GetLogsByCategory(const FString& Category, int32 MaxEntries) const
{
    FScopeLock Lock(&LogEntriesLock);
    
    TArray<FAuracronPCGLogEntry> Result;
    
    for (int32 i = LogEntries.Num() - 1; i >= 0 && Result.Num() < MaxEntries; --i)
    {
        if (LogEntries[i].Category == Category)
        {
            Result.Insert(LogEntries[i], 0);
        }
    }
    
    return Result;
}

TArray<FAuracronPCGLogEntry> FAuracronPCGLogger::GetLogsByLevel(const FString& LogLevel, int32 MaxEntries) const
{
    FScopeLock Lock(&LogEntriesLock);
    
    TArray<FAuracronPCGLogEntry> Result;
    
    for (int32 i = LogEntries.Num() - 1; i >= 0 && Result.Num() < MaxEntries; --i)
    {
        if (LogEntries[i].LogLevel == LogLevel)
        {
            Result.Insert(LogEntries[i], 0);
        }
    }
    
    return Result;
}

TArray<FAuracronPCGLogEntry> FAuracronPCGLogger::GetLogsInTimeRange(const FDateTime& StartTime, const FDateTime& EndTime) const
{
    FScopeLock Lock(&LogEntriesLock);
    
    TArray<FAuracronPCGLogEntry> Result;
    
    for (const FAuracronPCGLogEntry& Entry : LogEntries)
    {
        if (Entry.Timestamp >= StartTime && Entry.Timestamp <= EndTime)
        {
            Result.Add(Entry);
        }
    }
    
    return Result;
}

void FAuracronPCGLogger::ClearLogs()
{
    {
        FScopeLock Lock(&LogEntriesLock);
        LogEntries.Empty();
    }

    {
        FScopeLock Lock(&StatisticsLock);
        LogCountByLevel.Empty();
        LogCountByCategory.Empty();
    }

    LogInfo(TEXT("Core"), TEXT("Log entries cleared"));
}

void FAuracronPCGLogger::FlushLogs()
{
    FScopeLock Lock(&FileOperationLock);
    
    if (LogFileArchive.IsValid())
    {
        LogFileArchive->Flush();
    }
}

void FAuracronPCGLogger::RotateLogFiles()
{
    if (!Configuration.bEnableFileLogging)
    {
        return;
    }

    LogInfo(TEXT("Core"), TEXT("Rotating log files"));
    
    // Close current file
    {
        FScopeLock Lock(&FileOperationLock);
        if (LogFileArchive.IsValid())
        {
            LogFileArchive->Close();
            LogFileArchive.Reset();
        }
    }

    // Rename current file with timestamp
    FString TimestampedFileName = FPaths::GetBaseFilename(CurrentLogFilePath) + 
                                 TEXT("_") + FDateTime::Now().ToString(TEXT("%Y%m%d_%H%M%S")) + 
                                 FPaths::GetExtension(CurrentLogFilePath, true);
    
    FString TimestampedFilePath = FPaths::GetPath(CurrentLogFilePath) / TimestampedFileName;
    IFileManager::Get().Move(*TimestampedFilePath, *CurrentLogFilePath);

    // Create new log file
    CurrentLogFilePath = GenerateLogFileName();
    LogFileArchive = TUniquePtr<FArchive>(IFileManager::Get().CreateFileWriter(*CurrentLogFilePath));

    // Clean up old log files
    TArray<FString> LogFiles;
    IFileManager::Get().FindFiles(LogFiles, *(FPaths::GetPath(CurrentLogFilePath) / TEXT("*.log")), true, false);
    
    if (LogFiles.Num() > Configuration.MaxLogFiles)
    {
        // Sort by modification time and remove oldest
        LogFiles.Sort([&](const FString& A, const FString& B)
        {
            FDateTime TimeA = IFileManager::Get().GetTimeStamp(*(FPaths::GetPath(CurrentLogFilePath) / A));
            FDateTime TimeB = IFileManager::Get().GetTimeStamp(*(FPaths::GetPath(CurrentLogFilePath) / B));
            return TimeA < TimeB;
        });

        int32 FilesToRemove = LogFiles.Num() - Configuration.MaxLogFiles;
        for (int32 i = 0; i < FilesToRemove; ++i)
        {
            FString FileToRemove = FPaths::GetPath(CurrentLogFilePath) / LogFiles[i];
            IFileManager::Get().Delete(*FileToRemove);
        }
    }

    LogInfo(TEXT("Core"), TEXT("Log file rotation completed"), FString::Printf(TEXT("New file: %s"), *CurrentLogFilePath));
}

int32 FAuracronPCGLogger::GetTotalLogCount() const
{
    FScopeLock Lock(&LogEntriesLock);
    return LogEntries.Num();
}

int32 FAuracronPCGLogger::GetLogCountByLevel(const FString& LogLevel) const
{
    FScopeLock Lock(&StatisticsLock);
    if (const int32* Count = LogCountByLevel.Find(LogLevel))
    {
        return *Count;
    }
    return 0;
}

int32 FAuracronPCGLogger::GetLogCountByCategory(const FString& Category) const
{
    FScopeLock Lock(&StatisticsLock);
    if (const int32* Count = LogCountByCategory.Find(Category))
    {
        return *Count;
    }
    return 0;
}

TMap<FString, int32> FAuracronPCGLogger::GetLogStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    TMap<FString, int32> Statistics;

    // Add level statistics
    for (const auto& Pair : LogCountByLevel)
    {
        Statistics.Add(FString::Printf(TEXT("Level_%s"), *Pair.Key), Pair.Value);
    }

    // Add category statistics
    for (const auto& Pair : LogCountByCategory)
    {
        Statistics.Add(FString::Printf(TEXT("Category_%s"), *Pair.Key), Pair.Value);
    }

    // Add total count
    {
        FScopeLock LogLock(&LogEntriesLock);
        Statistics.Add(TEXT("Total"), LogEntries.Num());
    }

    return Statistics;
}

bool FAuracronPCGLogger::SaveLogsToFile(const FString& FilePath, const TArray<FAuracronPCGLogEntry>& Logs) const
{
    TArray<FString> LogLines;

    for (const FAuracronPCGLogEntry& Entry : Logs)
    {
        LogLines.Add(FormatLogEntry(Entry));
    }

    return FFileHelper::SaveStringArrayToFile(LogLines, *FilePath);
}

bool FAuracronPCGLogger::LoadLogsFromFile(const FString& FilePath, TArray<FAuracronPCGLogEntry>& OutLogs) const
{
    TArray<FString> LogLines;
    if (!FFileHelper::LoadFileToStringArray(LogLines, *FilePath))
    {
        return false;
    }

    OutLogs.Empty();

    // Parse log lines (simplified parsing - would need more robust implementation)
    for (const FString& Line : LogLines)
    {
        // This is a simplified parser - in production, you'd want more robust parsing
        TArray<FString> Parts;
        Line.ParseIntoArray(Parts, TEXT("|"), true);

        if (Parts.Num() >= 4)
        {
            FAuracronPCGLogEntry Entry;
            FDateTime::Parse(Parts[0].TrimStartAndEnd(), Entry.Timestamp);
            Entry.LogLevel = Parts[1].TrimStartAndEnd();
            Entry.Category = Parts[2].TrimStartAndEnd();
            Entry.Message = Parts[3].TrimStartAndEnd();

            if (Parts.Num() > 4)
            {
                Entry.Context = Parts[4].TrimStartAndEnd();
            }

            OutLogs.Add(Entry);
        }
    }

    return true;
}

bool FAuracronPCGLogger::ExportLogsToCSV(const FString& FilePath, const TArray<FAuracronPCGLogEntry>& Logs) const
{
    TArray<FString> CSVLines;

    // Add header
    CSVLines.Add(TEXT("Timestamp,LogLevel,Category,Message,Context,ThreadId,LineNumber,SourceFile"));

    // Add data rows
    for (const FAuracronPCGLogEntry& Entry : Logs)
    {
        FString CSVLine = FString::Printf(TEXT("%s,%s,%s,\"%s\",\"%s\",%s,%d,\"%s\""),
            *Entry.Timestamp.ToString(),
            *Entry.LogLevel,
            *Entry.Category,
            *Entry.Message.Replace(TEXT("\""), TEXT("\"\"")), // Escape quotes
            *Entry.Context.Replace(TEXT("\""), TEXT("\"\"")),
            *Entry.ThreadId,
            Entry.LineNumber,
            *Entry.SourceFile.Replace(TEXT("\""), TEXT("\"\""))
        );
        CSVLines.Add(CSVLine);
    }

    return FFileHelper::SaveStringArrayToFile(CSVLines, *FilePath);
}

bool FAuracronPCGLogger::ExportLogsToJSON(const FString& FilePath, const TArray<FAuracronPCGLogEntry>& Logs) const
{
    TSharedPtr<FJsonObject> RootObject = MakeShareable(new FJsonObject);
    TArray<TSharedPtr<FJsonValue>> LogArray;

    for (const FAuracronPCGLogEntry& Entry : Logs)
    {
        TSharedPtr<FJsonObject> LogObject = MakeShareable(new FJsonObject);
        LogObject->SetStringField(TEXT("timestamp"), Entry.Timestamp.ToIso8601());
        LogObject->SetStringField(TEXT("logLevel"), Entry.LogLevel);
        LogObject->SetStringField(TEXT("category"), Entry.Category);
        LogObject->SetStringField(TEXT("message"), Entry.Message);
        LogObject->SetStringField(TEXT("context"), Entry.Context);
        LogObject->SetStringField(TEXT("threadId"), Entry.ThreadId);
        LogObject->SetNumberField(TEXT("lineNumber"), Entry.LineNumber);
        LogObject->SetStringField(TEXT("sourceFile"), Entry.SourceFile);

        LogArray.Add(MakeShareable(new FJsonValueObject(LogObject)));
    }

    RootObject->SetArrayField(TEXT("logs"), LogArray);

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(RootObject.ToSharedRef(), Writer);

    return FFileHelper::SaveStringToFile(OutputString, *FilePath);
}

// Internal methods implementation

void FAuracronPCGLogger::WriteToFile(const FAuracronPCGLogEntry& Entry)
{
    FScopeLock Lock(&FileOperationLock);

    if (LogFileArchive.IsValid())
    {
        FString FormattedEntry = FormatLogEntry(Entry) + LINE_TERMINATOR;
        LogFileArchive->Serialize(TCHAR_TO_UTF8(*FormattedEntry), FormattedEntry.Len());

        if (Configuration.bAutoFlush)
        {
            LogFileArchive->Flush();
        }
    }
}

void FAuracronPCGLogger::WriteToConsole(const FAuracronPCGLogEntry& Entry)
{
    FString FormattedEntry = FormatLogEntry(Entry);

    // Use appropriate UE log category based on log level
    if (Entry.LogLevel == TEXT("Error"))
    {
        UE_LOG(LogAuracronPCG, Error, TEXT("%s"), *FormattedEntry);
    }
    else if (Entry.LogLevel == TEXT("Warning"))
    {
        UE_LOG(LogAuracronPCG, Warning, TEXT("%s"), *FormattedEntry);
    }
    else if (Entry.LogLevel == TEXT("Verbose"))
    {
        UE_LOG(LogAuracronPCG, Verbose, TEXT("%s"), *FormattedEntry);
    }
    else
    {
        UE_LOG(LogAuracronPCG, Log, TEXT("%s"), *FormattedEntry);
    }
}

void FAuracronPCGLogger::AddToMemory(const FAuracronPCGLogEntry& Entry)
{
    FScopeLock Lock(&LogEntriesLock);

    LogEntries.Add(Entry);

    // Remove old entries if we exceed the limit
    if (LogEntries.Num() > Configuration.MaxMemoryEntries)
    {
        int32 EntriesToRemove = LogEntries.Num() - Configuration.MaxMemoryEntries;
        LogEntries.RemoveAt(0, EntriesToRemove);
    }
}

void FAuracronPCGLogger::UpdateStatistics(const FAuracronPCGLogEntry& Entry)
{
    FScopeLock Lock(&StatisticsLock);

    // Update level count
    int32& LevelCount = LogCountByLevel.FindOrAdd(Entry.LogLevel);
    LevelCount++;

    // Update category count
    int32& CategoryCount = LogCountByCategory.FindOrAdd(Entry.Category);
    CategoryCount++;
}

FString FAuracronPCGLogger::FormatLogEntry(const FAuracronPCGLogEntry& Entry) const
{
    return FString::Printf(TEXT("%s | %s | %s | %s | %s | Thread:%s"),
        *Entry.Timestamp.ToString(TEXT("%Y-%m-%d %H:%M:%S.%f")),
        *Entry.LogLevel,
        *Entry.Category,
        *Entry.Message,
        *Entry.Context,
        *Entry.ThreadId
    );
}

FString FAuracronPCGLogger::GetLogLevelColor(const FString& LogLevel) const
{
    if (LogLevel == TEXT("Error"))
    {
        return TEXT("Red");
    }
    else if (LogLevel == TEXT("Warning"))
    {
        return TEXT("Yellow");
    }
    else if (LogLevel == TEXT("Info"))
    {
        return TEXT("White");
    }
    else if (LogLevel == TEXT("Verbose"))
    {
        return TEXT("Gray");
    }

    return TEXT("White");
}

bool FAuracronPCGLogger::CreateLogDirectory()
{
    FString LogDirectory = FPaths::GetPath(Configuration.LogFilePath);

    if (!FPaths::DirectoryExists(LogDirectory))
    {
        return IFileManager::Get().MakeDirectory(*LogDirectory, true);
    }

    return true;
}

FString FAuracronPCGLogger::GenerateLogFileName() const
{
    FString BaseFileName = FPaths::GetBaseFilename(Configuration.LogFilePath);
    FString Extension = FPaths::GetExtension(Configuration.LogFilePath, true);
    FString Directory = FPaths::GetPath(Configuration.LogFilePath);

    if (Extension.IsEmpty())
    {
        Extension = TEXT(".log");
    }

    FString Timestamp = FDateTime::Now().ToString(TEXT("%Y%m%d_%H%M%S"));
    FString FileName = FString::Printf(TEXT("%s_%s%s"), *BaseFileName, *Timestamp, *Extension);

    return FPaths::Combine(Directory, FileName);
}

bool FAuracronPCGLogger::ShouldRotateLogFile() const
{
    if (!Configuration.bEnableFileLogging || !LogFileArchive.IsValid())
    {
        return false;
    }

    int64 CurrentFileSize = LogFileArchive->Tell();
    return CurrentFileSize >= Configuration.MaxLogFileSize;
}

void FAuracronPCGLogger::PerformLogRotation()
{
    if (Configuration.bEnableFileLogging)
    {
        RotateLogFiles();
    }
}

void FAuracronPCGLogger::StartAutoFlushTimer()
{
    if (UWorld* World = GEngine->GetCurrentPlayWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.SetTimer(FlushTimer, this, &FAuracronPCGLogger::OnAutoFlushTimer,
                             Configuration.FlushIntervalSeconds, true);
    }
}

void FAuracronPCGLogger::StopAutoFlushTimer()
{
    if (UWorld* World = GEngine->GetCurrentPlayWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.ClearTimer(FlushTimer);
    }
}

void FAuracronPCGLogger::OnAutoFlushTimer()
{
    FlushLogs();
}

FString FAuracronPCGLogger::GetCurrentThreadName()
{
    return FString::Printf(TEXT("Thread_%d"), FPlatformTLS::GetCurrentThreadId());
}

FString FAuracronPCGLogger::SanitizeLogMessage(const FString& Message)
{
    // Remove or escape potentially problematic characters
    FString SanitizedMessage = Message;
    SanitizedMessage = SanitizedMessage.Replace(TEXT("\n"), TEXT("\\n"));
    SanitizedMessage = SanitizedMessage.Replace(TEXT("\r"), TEXT("\\r"));
    SanitizedMessage = SanitizedMessage.Replace(TEXT("\t"), TEXT("\\t"));

    return SanitizedMessage;
}

// Global logger access
FAuracronPCGLogger* GetAuracronPCGLogger()
{
    return GGlobalPCGLogger;
}

// Scoped performance logger implementation
FAuracronPCGScopedPerformanceLogger::FAuracronPCGScopedPerformanceLogger(const FString& InOperationName)
    : OperationName(InOperationName)
    , StartTime(FPlatformTime::Seconds())
{
}

FAuracronPCGScopedPerformanceLogger::~FAuracronPCGScopedPerformanceLogger()
{
    double Duration = FPlatformTime::Seconds() - StartTime;

    if (FAuracronPCGLogger* Logger = GetAuracronPCGLogger())
    {
        Logger->LogInfo(TEXT("Performance"),
                       FString::Printf(TEXT("Scoped operation '%s' took %.6f seconds"), *OperationName, Duration));
    }
}
