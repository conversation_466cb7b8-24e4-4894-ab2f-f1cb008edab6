// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Audio Streaming Implementation
// Bridge 3.9: World Partition - Audio Streaming

#include "AuracronWorldPartitionAudio.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionBridge.h"

// Audio includes
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Sound/SoundCue.h"
#include "Sound/SoundWave.h"
#include "Sound/AmbientSound.h"
#include "AudioDevice.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "HAL/PlatformMemory.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "UObject/ConstructorHelpers.h"

// =============================================================================
// AUDIO STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronAudioStatistics::UpdateCalculatedFields()
{
    if (TotalAudioSources > 0)
    {
        AudioEfficiency = static_cast<float>(LoadedAudioSources) / static_cast<float>(TotalAudioSources);
    }
    else
    {
        AudioEfficiency = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// WORLD PARTITION AUDIO MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionAudioManager* UAuracronWorldPartitionAudioManager::Instance = nullptr;

UAuracronWorldPartitionAudioManager* UAuracronWorldPartitionAudioManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionAudioManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionAudioManager::Initialize(const FAuracronAudioConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Audio Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize statistics
    Statistics = FAuracronAudioStatistics();
    
    // Clear collections
    AudioDescriptors.Empty();
    AudioComponents.Empty();
    AudioToCellMap.Empty();
    CellToAudioMap.Empty();
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Audio Manager initialized with streaming distance: %.1fm"), Configuration.AudioStreamingDistance);
}

void UAuracronWorldPartitionAudioManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Stop and unload all audio sources
    TArray<FString> AudioToUnload;
    AudioDescriptors.GenerateKeyArray(AudioToUnload);
    
    for (const FString& AudioId : AudioToUnload)
    {
        StopAudioSource(AudioId);
        UnloadAudioSource(AudioId);
    }
    
    // Clear all data
    AudioDescriptors.Empty();
    AudioComponents.Empty();
    AudioToCellMap.Empty();
    CellToAudioMap.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Audio Manager shutdown completed"));
}

bool UAuracronWorldPartitionAudioManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionAudioManager::Tick(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update statistics
    UpdateStatistics();
}

FString UAuracronWorldPartitionAudioManager::CreateAudioSource(const FString& SoundAssetPath, const FVector& Location, EAuracronAudioType AudioType)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot create audio source: Manager not initialized"));
        return FString();
    }

    if (SoundAssetPath.IsEmpty())
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot create audio source: Sound asset path is empty"));
        return FString();
    }

    FScopeLock Lock(&AudioLock);
    
    FString AudioId = GenerateAudioId(SoundAssetPath);
    
    // Check if audio source already exists
    if (AudioDescriptors.Contains(AudioId))
    {
        AURACRON_WP_LOG_WARNING(TEXT("Audio source already exists: %s"), *AudioId);
        return AudioId;
    }
    
    // Create audio descriptor
    FAuracronAudioDescriptor AudioDesc;
    AudioDesc.AudioId = AudioId;
    AudioDesc.AudioName = FString::Printf(TEXT("Audio_%s"), *FPaths::GetBaseFilename(SoundAssetPath));
    AudioDesc.SoundAssetPath = SoundAssetPath;
    AudioDesc.AudioType = AudioType;
    AudioDesc.Spatialization = Configuration.DefaultSpatialization;
    AudioDesc.StreamingState = EAuracronAudioStreamingState::Unloaded;
    AudioDesc.CurrentLODLevel = EAuracronAudioLODLevel::LOD0;
    AudioDesc.Location = Location;
    AudioDesc.MaxAudibleDistance = Configuration.MaxAudibleDistance;
    AudioDesc.FalloffDistance = Configuration.FalloffDistance;
    AudioDesc.CreationTime = FDateTime::Now();
    AudioDesc.LastAccessTime = AudioDesc.CreationTime;
    
    // Set default volume based on audio type
    switch (AudioType)
    {
        case EAuracronAudioType::Ambient:
            AudioDesc.Volume = Configuration.AmbientVolume;
            AudioDesc.bIsLooping = true;
            break;
        case EAuracronAudioType::Music:
            AudioDesc.Volume = Configuration.MusicVolume;
            AudioDesc.bIsLooping = true;
            AudioDesc.bIs3D = false;
            break;
        case EAuracronAudioType::SFX:
            AudioDesc.Volume = Configuration.SFXVolume;
            AudioDesc.bIsLooping = false;
            break;
        case EAuracronAudioType::Voice:
            AudioDesc.Volume = Configuration.SFXVolume;
            AudioDesc.bIsLooping = false;
            break;
        case EAuracronAudioType::UI:
            AudioDesc.Volume = Configuration.SFXVolume;
            AudioDesc.bIsLooping = false;
            AudioDesc.bIs3D = false;
            break;
        case EAuracronAudioType::Foley:
            AudioDesc.Volume = Configuration.SFXVolume;
            AudioDesc.bIsLooping = false;
            break;
    }
    
    // Add to collections
    AudioDescriptors.Add(AudioId, AudioDesc);
    
    // Update cell mapping
    UpdateAudioCellMapping(AudioId);
    
    // Create audio component if needed
    if (Configuration.bEnableAudioStreaming)
    {
        CreateAudioComponent(AudioId, SoundAssetPath);
    }
    
    AURACRON_WP_LOG_INFO(TEXT("Audio source created: %s (Type: %s)"), *AudioId, *UEnum::GetValueAsString(AudioType));
    
    return AudioId;
}

bool UAuracronWorldPartitionAudioManager::RemoveAudioSource(const FString& AudioId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&AudioLock);
    
    if (!AudioDescriptors.Contains(AudioId))
    {
        return false;
    }
    
    // Stop and unload audio source
    StopAudioSource(AudioId);
    UnloadAudioSource(AudioId);
    
    // Remove from cell mapping
    if (FString* CellId = AudioToCellMap.Find(AudioId))
    {
        if (TSet<FString>* CellAudio = CellToAudioMap.Find(*CellId))
        {
            CellAudio->Remove(AudioId);
        }
        AudioToCellMap.Remove(AudioId);
    }
    
    // Remove from collections
    AudioDescriptors.Remove(AudioId);
    AudioComponents.Remove(AudioId);
    
    AURACRON_WP_LOG_INFO(TEXT("Audio source removed: %s"), *AudioId);
    
    return true;
}

FAuracronAudioDescriptor UAuracronWorldPartitionAudioManager::GetAudioDescriptor(const FString& AudioId) const
{
    FScopeLock Lock(&AudioLock);
    
    const FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (AudioDesc)
    {
        return *AudioDesc;
    }
    
    return FAuracronAudioDescriptor();
}

TArray<FAuracronAudioDescriptor> UAuracronWorldPartitionAudioManager::GetAllAudioSources() const
{
    FScopeLock Lock(&AudioLock);
    
    TArray<FAuracronAudioDescriptor> AllAudio;
    AudioDescriptors.GenerateValueArray(AllAudio);
    
    return AllAudio;
}

TArray<FString> UAuracronWorldPartitionAudioManager::GetAudioIds() const
{
    FScopeLock Lock(&AudioLock);
    
    TArray<FString> AudioIds;
    AudioDescriptors.GenerateKeyArray(AudioIds);
    
    return AudioIds;
}

bool UAuracronWorldPartitionAudioManager::DoesAudioSourceExist(const FString& AudioId) const
{
    FScopeLock Lock(&AudioLock);
    return AudioDescriptors.Contains(AudioId);
}

bool UAuracronWorldPartitionAudioManager::LoadAudioSource(const FString& AudioId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&AudioLock);
    
    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }
    
    if (AudioDesc->StreamingState == EAuracronAudioStreamingState::Loaded)
    {
        return true; // Already loaded
    }
    
    // Set loading state
    AudioDesc->StreamingState = EAuracronAudioStreamingState::Loading;
    AudioDesc->LastAccessTime = FDateTime::Now();
    
    // Simulate audio loading
    // In a real implementation, this would load the actual sound asset
    bool bLoadSuccess = (FMath::RandRange(0, 100) > 2); // 98% success rate
    
    if (bLoadSuccess)
    {
        AudioDesc->StreamingState = EAuracronAudioStreamingState::Loaded;
        
        // Calculate memory usage based on audio type and quality
        float BaseMemory = 2.0f; // 2MB base
        switch (AudioDesc->AudioType)
        {
            case EAuracronAudioType::Music:
                BaseMemory *= 3.0f; // Music files are larger
                break;
            case EAuracronAudioType::Voice:
                BaseMemory *= 2.0f;
                break;
            case EAuracronAudioType::Ambient:
                BaseMemory *= 1.5f;
                break;
            default:
                BaseMemory *= 1.0f;
                break;
        }
        
        // Apply LOD multiplier
        float LODMultiplier = 1.0f - (static_cast<int32>(AudioDesc->CurrentLODLevel) * 0.2f);
        AudioDesc->MemoryUsageMB = BaseMemory * LODMultiplier;
        
        OnAudioLoadedInternal(AudioId, true);
        
        AURACRON_WP_LOG_INFO(TEXT("Audio source loaded: %s (%.1fMB)"), *AudioId, AudioDesc->MemoryUsageMB);
        return true;
    }
    else
    {
        AudioDesc->StreamingState = EAuracronAudioStreamingState::Failed;
        
        // Update statistics
        {
            FScopeLock StatsLock(&StatisticsLock);
            Statistics.FailedOperations++;
        }
        
        OnAudioLoadedInternal(AudioId, false);
        
        AURACRON_WP_LOG_ERROR(TEXT("Failed to load audio source: %s"), *AudioId);
        return false;
    }
}

bool UAuracronWorldPartitionAudioManager::UnloadAudioSource(const FString& AudioId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    if (AudioDesc->StreamingState == EAuracronAudioStreamingState::Unloaded)
    {
        return true; // Already unloaded
    }

    // Stop audio if playing
    if (AudioDesc->StreamingState == EAuracronAudioStreamingState::Playing)
    {
        StopAudioSource(AudioId);
    }

    // Set unloading state
    AudioDesc->StreamingState = EAuracronAudioStreamingState::Unloading;

    // Remove audio component reference
    if (TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId))
    {
        if (UAudioComponent* Component = ComponentRef->Get())
        {
            Component->Stop();
            Component->DestroyComponent();
        }
        AudioComponents.Remove(AudioId);
    }

    AudioDesc->StreamingState = EAuracronAudioStreamingState::Unloaded;
    AudioDesc->MemoryUsageMB = 0.0f;

    OnAudioUnloadedInternal(AudioId);

    AURACRON_WP_LOG_INFO(TEXT("Audio source unloaded: %s"), *AudioId);

    return true;
}

EAuracronAudioStreamingState UAuracronWorldPartitionAudioManager::GetAudioStreamingState(const FString& AudioId) const
{
    FScopeLock Lock(&AudioLock);

    const FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (AudioDesc)
    {
        return AudioDesc->StreamingState;
    }

    return EAuracronAudioStreamingState::Unloaded;
}

TArray<FString> UAuracronWorldPartitionAudioManager::GetLoadedAudioSources() const
{
    FScopeLock Lock(&AudioLock);

    TArray<FString> LoadedAudio;

    for (const auto& AudioPair : AudioDescriptors)
    {
        if (AudioPair.Value.StreamingState == EAuracronAudioStreamingState::Loaded ||
            AudioPair.Value.StreamingState == EAuracronAudioStreamingState::Playing ||
            AudioPair.Value.StreamingState == EAuracronAudioStreamingState::Paused)
        {
            LoadedAudio.Add(AudioPair.Key);
        }
    }

    return LoadedAudio;
}

TArray<FString> UAuracronWorldPartitionAudioManager::GetStreamingAudioSources() const
{
    FScopeLock Lock(&AudioLock);

    TArray<FString> StreamingAudio;

    for (const auto& AudioPair : AudioDescriptors)
    {
        if (AudioPair.Value.StreamingState == EAuracronAudioStreamingState::Loading ||
            AudioPair.Value.StreamingState == EAuracronAudioStreamingState::Unloading)
        {
            StreamingAudio.Add(AudioPair.Key);
        }
    }

    return StreamingAudio;
}

bool UAuracronWorldPartitionAudioManager::PlayAudioSource(const FString& AudioId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    // Load audio if not loaded
    if (AudioDesc->StreamingState == EAuracronAudioStreamingState::Unloaded)
    {
        if (!LoadAudioSource(AudioId))
        {
            return false;
        }
    }

    if (AudioDesc->StreamingState != EAuracronAudioStreamingState::Loaded &&
        AudioDesc->StreamingState != EAuracronAudioStreamingState::Paused)
    {
        return false;
    }

    // Get audio component
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (!ComponentRef || !ComponentRef->IsValid())
    {
        return false;
    }

    UAudioComponent* AudioComponent = ComponentRef->Get();
    if (!AudioComponent)
    {
        return false;
    }

    // Play audio
    AudioComponent->Play();
    AudioDesc->StreamingState = EAuracronAudioStreamingState::Playing;
    AudioDesc->LastAccessTime = FDateTime::Now();

    OnAudioStarted.Broadcast(AudioId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio source started: %s"), *AudioId);

    return true;
}

bool UAuracronWorldPartitionAudioManager::StopAudioSource(const FString& AudioId)
{
    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    // Get audio component
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        UAudioComponent* AudioComponent = ComponentRef->Get();
        if (AudioComponent)
        {
            AudioComponent->Stop();
        }
    }

    if (AudioDesc->StreamingState == EAuracronAudioStreamingState::Playing ||
        AudioDesc->StreamingState == EAuracronAudioStreamingState::Paused)
    {
        AudioDesc->StreamingState = EAuracronAudioStreamingState::Loaded;
        OnAudioStopped.Broadcast(AudioId);

        AURACRON_WP_LOG_VERBOSE(TEXT("Audio source stopped: %s"), *AudioId);
    }

    return true;
}

bool UAuracronWorldPartitionAudioManager::PauseAudioSource(const FString& AudioId)
{
    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc || AudioDesc->StreamingState != EAuracronAudioStreamingState::Playing)
    {
        return false;
    }

    // Get audio component
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        UAudioComponent* AudioComponent = ComponentRef->Get();
        if (AudioComponent)
        {
            AudioComponent->SetPaused(true);
        }
    }

    AudioDesc->StreamingState = EAuracronAudioStreamingState::Paused;

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio source paused: %s"), *AudioId);

    return true;
}

bool UAuracronWorldPartitionAudioManager::ResumeAudioSource(const FString& AudioId)
{
    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc || AudioDesc->StreamingState != EAuracronAudioStreamingState::Paused)
    {
        return false;
    }

    // Get audio component
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        UAudioComponent* AudioComponent = ComponentRef->Get();
        if (AudioComponent)
        {
            AudioComponent->SetPaused(false);
        }
    }

    AudioDesc->StreamingState = EAuracronAudioStreamingState::Playing;

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio source resumed: %s"), *AudioId);

    return true;
}

TArray<FString> UAuracronWorldPartitionAudioManager::GetPlayingAudioSources() const
{
    FScopeLock Lock(&AudioLock);

    TArray<FString> PlayingAudio;

    for (const auto& AudioPair : AudioDescriptors)
    {
        if (AudioPair.Value.StreamingState == EAuracronAudioStreamingState::Playing)
        {
            PlayingAudio.Add(AudioPair.Key);
        }
    }

    return PlayingAudio;
}

bool UAuracronWorldPartitionAudioManager::SetAudioVolume(const FString& AudioId, float Volume)
{
    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    Volume = FMath::Clamp(Volume, 0.0f, 1.0f);
    AudioDesc->Volume = Volume;
    AudioDesc->LastAccessTime = FDateTime::Now();

    // Update audio component if exists
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        UAudioComponent* AudioComponent = ComponentRef->Get();
        if (AudioComponent)
        {
            AudioComponent->SetVolumeMultiplier(Volume * Configuration.MasterVolume);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio volume set: %s -> %.2f"), *AudioId, Volume);

    return true;
}

bool UAuracronWorldPartitionAudioManager::SetAudioPitch(const FString& AudioId, float Pitch)
{
    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    Pitch = FMath::Clamp(Pitch, 0.1f, 4.0f);
    AudioDesc->Pitch = Pitch;
    AudioDesc->LastAccessTime = FDateTime::Now();

    // Update audio component if exists
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        UAudioComponent* AudioComponent = ComponentRef->Get();
        if (AudioComponent)
        {
            AudioComponent->SetPitchMultiplier(Pitch);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio pitch set: %s -> %.2f"), *AudioId, Pitch);

    return true;
}

bool UAuracronWorldPartitionAudioManager::SetAudioLocation(const FString& AudioId, const FVector& Location)
{
    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    AudioDesc->Location = Location;
    AudioDesc->LastAccessTime = FDateTime::Now();

    // Update audio component if exists
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        UAudioComponent* AudioComponent = ComponentRef->Get();
        if (AudioComponent)
        {
            AudioComponent->SetWorldLocation(Location);
        }
    }

    // Update cell mapping
    UpdateAudioCellMapping(AudioId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio location set: %s -> %s"), *AudioId, *Location.ToString());

    return true;
}

bool UAuracronWorldPartitionAudioManager::SetAudioRotation(const FString& AudioId, const FRotator& Rotation)
{
    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    AudioDesc->Rotation = Rotation;
    AudioDesc->LastAccessTime = FDateTime::Now();

    // Update audio component if exists
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        UAudioComponent* AudioComponent = ComponentRef->Get();
        if (AudioComponent)
        {
            AudioComponent->SetWorldRotation(Rotation);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio rotation set: %s -> %s"), *AudioId, *Rotation.ToString());

    return true;
}

bool UAuracronWorldPartitionAudioManager::SetAudioLOD(const FString& AudioId, EAuracronAudioLODLevel LODLevel)
{
    if (!bIsInitialized || !Configuration.bEnableAudioLOD)
    {
        return false;
    }

    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    EAuracronAudioLODLevel OldLOD = AudioDesc->CurrentLODLevel;
    AudioDesc->CurrentLODLevel = LODLevel;
    AudioDesc->LastAccessTime = FDateTime::Now();

    // Update audio component properties based on LOD
    TWeakObjectPtr<UAudioComponent>* ComponentRef = AudioComponents.Find(AudioId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        UAudioComponent* AudioComponent = ComponentRef->Get();
        if (AudioComponent)
        {
            // Adjust quality based on LOD level
            float QualityMultiplier = 1.0f - (static_cast<int32>(LODLevel) * 0.15f);
            AudioComponent->SetVolumeMultiplier(AudioDesc->Volume * QualityMultiplier * Configuration.MasterVolume);

            // Adjust attenuation distance for higher LODs
            if (LODLevel > EAuracronAudioLODLevel::LOD1)
            {
                float DistanceMultiplier = 1.0f - (static_cast<int32>(LODLevel) * 0.1f);
                // In a real implementation, this would modify the attenuation settings
            }
        }
    }

    // Update statistics
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.LODTransitions++;
    }

    OnAudioLODChanged.Broadcast(AudioId, LODLevel);

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio LOD changed: %s (%s -> %s)"),
                           *AudioId,
                           *UEnum::GetValueAsString(OldLOD),
                           *UEnum::GetValueAsString(LODLevel));

    return true;
}

EAuracronAudioLODLevel UAuracronWorldPartitionAudioManager::GetAudioLOD(const FString& AudioId) const
{
    FScopeLock Lock(&AudioLock);

    const FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (AudioDesc)
    {
        return AudioDesc->CurrentLODLevel;
    }

    return EAuracronAudioLODLevel::LOD0;
}

void UAuracronWorldPartitionAudioManager::UpdateDistanceBasedLODs(const FVector& ListenerLocation)
{
    if (!bIsInitialized || !Configuration.bEnableAudioLOD)
    {
        return;
    }

    FScopeLock Lock(&AudioLock);

    for (auto& AudioPair : AudioDescriptors)
    {
        FAuracronAudioDescriptor& AudioDesc = AudioPair.Value;

        if (AudioDesc.StreamingState == EAuracronAudioStreamingState::Loaded ||
            AudioDesc.StreamingState == EAuracronAudioStreamingState::Playing ||
            AudioDesc.StreamingState == EAuracronAudioStreamingState::Paused)
        {
            float Distance = FVector::Dist(ListenerLocation, AudioDesc.Location);
            EAuracronAudioLODLevel TargetLOD = CalculateLODForDistance(Distance);

            if (TargetLOD != AudioDesc.CurrentLODLevel)
            {
                SetAudioLOD(AudioPair.Key, TargetLOD);
            }
        }
    }
}

EAuracronAudioLODLevel UAuracronWorldPartitionAudioManager::CalculateLODForDistance(float Distance) const
{
    if (Distance <= Configuration.BaseLODDistance)
    {
        return EAuracronAudioLODLevel::LOD0; // Highest quality
    }

    float CurrentDistance = Configuration.BaseLODDistance;
    for (int32 LODLevel = 1; LODLevel <= Configuration.MaxLODLevel; LODLevel++)
    {
        CurrentDistance *= Configuration.LODDistanceMultiplier;
        if (Distance <= CurrentDistance)
        {
            return static_cast<EAuracronAudioLODLevel>(LODLevel);
        }
    }

    return static_cast<EAuracronAudioLODLevel>(Configuration.MaxLODLevel); // Lowest quality
}

TArray<FAuracronAudioDescriptor> UAuracronWorldPartitionAudioManager::GetAudioSourcesInRadius(const FVector& Location, float Radius) const
{
    FScopeLock Lock(&AudioLock);

    TArray<FAuracronAudioDescriptor> AudioInRadius;

    for (const auto& AudioPair : AudioDescriptors)
    {
        const FAuracronAudioDescriptor& AudioDesc = AudioPair.Value;

        float Distance = FVector::Dist(Location, AudioDesc.Location);
        if (Distance <= Radius)
        {
            AudioInRadius.Add(AudioDesc);
        }
    }

    return AudioInRadius;
}

TArray<FAuracronAudioDescriptor> UAuracronWorldPartitionAudioManager::GetAudioSourcesByType(EAuracronAudioType AudioType) const
{
    FScopeLock Lock(&AudioLock);

    TArray<FAuracronAudioDescriptor> FilteredAudio;

    for (const auto& AudioPair : AudioDescriptors)
    {
        if (AudioPair.Value.AudioType == AudioType)
        {
            FilteredAudio.Add(AudioPair.Value);
        }
    }

    return FilteredAudio;
}

TArray<FAuracronAudioDescriptor> UAuracronWorldPartitionAudioManager::GetAudibleAudioSources(const FVector& ListenerLocation) const
{
    FScopeLock Lock(&AudioLock);

    TArray<FAuracronAudioDescriptor> AudibleAudio;

    for (const auto& AudioPair : AudioDescriptors)
    {
        const FAuracronAudioDescriptor& AudioDesc = AudioPair.Value;

        if (IsAudioAudible(AudioDesc, ListenerLocation))
        {
            AudibleAudio.Add(AudioDesc);
        }
    }

    return AudibleAudio;
}

TArray<FString> UAuracronWorldPartitionAudioManager::GetAudioSourcesInCell(const FString& CellId) const
{
    FScopeLock Lock(&AudioLock);

    const TSet<FString>* CellAudio = CellToAudioMap.Find(CellId);
    if (CellAudio)
    {
        return CellAudio->Array();
    }

    return TArray<FString>();
}

FString UAuracronWorldPartitionAudioManager::GetAudioSourceCell(const FString& AudioId) const
{
    FScopeLock Lock(&AudioLock);

    const FString* CellId = AudioToCellMap.Find(AudioId);
    if (CellId)
    {
        return *CellId;
    }

    return FString();
}

bool UAuracronWorldPartitionAudioManager::MoveAudioSourceToCell(const FString& AudioId, const FString& CellId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    // Remove from old cell
    if (FString* OldCellId = AudioToCellMap.Find(AudioId))
    {
        if (TSet<FString>* OldCellAudio = CellToAudioMap.Find(*OldCellId))
        {
            OldCellAudio->Remove(AudioId);
        }
    }

    // Add to new cell
    AudioToCellMap.Add(AudioId, CellId);
    TSet<FString>& NewCellAudio = CellToAudioMap.FindOrAdd(CellId);
    NewCellAudio.Add(AudioId);

    AudioDesc->CellId = CellId;
    AudioDesc->LastAccessTime = FDateTime::Now();

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio source moved to cell: %s -> %s"), *AudioId, *CellId);

    return true;
}

void UAuracronWorldPartitionAudioManager::SetConfiguration(const FAuracronAudioConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Audio configuration updated"));
}

FAuracronAudioConfiguration UAuracronWorldPartitionAudioManager::GetConfiguration() const
{
    return Configuration;
}

FAuracronAudioStatistics UAuracronWorldPartitionAudioManager::GetAudioStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronAudioStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronWorldPartitionAudioManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronAudioStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Audio statistics reset"));
}

int32 UAuracronWorldPartitionAudioManager::GetTotalAudioSourceCount() const
{
    FScopeLock Lock(&AudioLock);
    return AudioDescriptors.Num();
}

int32 UAuracronWorldPartitionAudioManager::GetLoadedAudioSourceCount() const
{
    return GetLoadedAudioSources().Num();
}

int32 UAuracronWorldPartitionAudioManager::GetPlayingAudioSourceCount() const
{
    return GetPlayingAudioSources().Num();
}

float UAuracronWorldPartitionAudioManager::GetTotalMemoryUsage() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.TotalMemoryUsageMB;
}

void UAuracronWorldPartitionAudioManager::EnableAudioDebug(bool bEnabled)
{
    Configuration.bEnableAudioDebug = bEnabled;

    AURACRON_WP_LOG_INFO(TEXT("Audio debug %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionAudioManager::IsAudioDebugEnabled() const
{
    return Configuration.bEnableAudioDebug;
}

void UAuracronWorldPartitionAudioManager::LogAudioState() const
{
    FScopeLock Lock(&AudioLock);

    int32 TotalCount = AudioDescriptors.Num();
    int32 LoadedCount = GetLoadedAudioSourceCount();
    int32 PlayingCount = GetPlayingAudioSourceCount();
    int32 StreamingCount = GetStreamingAudioSources().Num();

    AURACRON_WP_LOG_INFO(TEXT("Audio State: %d total, %d loaded, %d playing, %d streaming"), TotalCount, LoadedCount, PlayingCount, StreamingCount);

    FAuracronAudioStatistics CurrentStats = GetAudioStatistics();
    AURACRON_WP_LOG_INFO(TEXT("Statistics: %.1fMB memory, %.2f efficiency, %d LOD transitions"),
                         CurrentStats.TotalMemoryUsageMB, CurrentStats.AudioEfficiency, CurrentStats.LODTransitions);
}

void UAuracronWorldPartitionAudioManager::DrawDebugAudioInfo(UWorld* World) const
{
    if (!Configuration.bEnableAudioDebug || !World)
    {
        return;
    }

    FScopeLock Lock(&AudioLock);

    // Draw debug information for loaded audio sources
    for (const auto& AudioPair : AudioDescriptors)
    {
        const FAuracronAudioDescriptor& AudioDesc = AudioPair.Value;

        if (AudioDesc.StreamingState == EAuracronAudioStreamingState::Loaded ||
            AudioDesc.StreamingState == EAuracronAudioStreamingState::Playing ||
            AudioDesc.StreamingState == EAuracronAudioStreamingState::Paused)
        {
            FColor DebugColor = FColor::Blue;

            // Color based on audio type
            switch (AudioDesc.AudioType)
            {
                case EAuracronAudioType::Ambient:
                    DebugColor = FColor::Green;
                    break;
                case EAuracronAudioType::Music:
                    DebugColor = FColor::Purple;
                    break;
                case EAuracronAudioType::SFX:
                    DebugColor = FColor::Red;
                    break;
                case EAuracronAudioType::Voice:
                    DebugColor = FColor::Orange;
                    break;
                case EAuracronAudioType::UI:
                    DebugColor = FColor::Cyan;
                    break;
                case EAuracronAudioType::Foley:
                    DebugColor = FColor::Yellow;
                    break;
            }

            // Draw audio source sphere
            DrawDebugSphere(World, AudioDesc.Location, 50.0f, 12, DebugColor, false, -1.0f, 0, 2.0f);

            // Draw audible range
            if (AudioDesc.bIs3D)
            {
                DrawDebugSphere(World, AudioDesc.Location, AudioDesc.MaxAudibleDistance, 32,
                               DebugColor, false, -1.0f, 0, 1.0f);
            }

            // Draw audio info text
            FString StateText;
            switch (AudioDesc.StreamingState)
            {
                case EAuracronAudioStreamingState::Playing:
                    StateText = TEXT("PLAYING");
                    break;
                case EAuracronAudioStreamingState::Paused:
                    StateText = TEXT("PAUSED");
                    break;
                default:
                    StateText = TEXT("LOADED");
                    break;
            }

            FString DebugText = FString::Printf(TEXT("%s\n%s LOD%d\nVol:%.1f Pitch:%.1f"),
                                              *AudioDesc.AudioName,
                                              *StateText,
                                              static_cast<int32>(AudioDesc.CurrentLODLevel),
                                              AudioDesc.Volume,
                                              AudioDesc.Pitch);

            DrawDebugString(World, AudioDesc.Location + FVector(0, 0, 100), DebugText,
                           nullptr, DebugColor, -1.0f, true);
        }
    }
}

void UAuracronWorldPartitionAudioManager::UpdateStatistics()
{
    FScopeLock Lock(&StatisticsLock);

    Statistics.TotalAudioSources = AudioDescriptors.Num();
    Statistics.LoadedAudioSources = 0;
    Statistics.PlayingAudioSources = 0;
    Statistics.StreamingAudioSources = 0;
    Statistics.TotalMemoryUsageMB = 0.0f;

    for (const auto& AudioPair : AudioDescriptors)
    {
        const FAuracronAudioDescriptor& AudioDesc = AudioPair.Value;

        switch (AudioDesc.StreamingState)
        {
            case EAuracronAudioStreamingState::Loaded:
            case EAuracronAudioStreamingState::Paused:
                Statistics.LoadedAudioSources++;
                Statistics.TotalMemoryUsageMB += AudioDesc.MemoryUsageMB;
                break;
            case EAuracronAudioStreamingState::Playing:
                Statistics.LoadedAudioSources++;
                Statistics.PlayingAudioSources++;
                Statistics.TotalMemoryUsageMB += AudioDesc.MemoryUsageMB;
                break;
            case EAuracronAudioStreamingState::Loading:
            case EAuracronAudioStreamingState::Unloading:
                Statistics.StreamingAudioSources++;
                break;
        }
    }

    Statistics.UpdateCalculatedFields();
}

FString UAuracronWorldPartitionAudioManager::GenerateAudioId(const FString& SoundAssetPath) const
{
    FString BaseName = FPaths::GetBaseFilename(SoundAssetPath);
    return FString::Printf(TEXT("Audio_%s_%lld"), *BaseName, FDateTime::Now().GetTicks());
}

bool UAuracronWorldPartitionAudioManager::ValidateAudioId(const FString& AudioId) const
{
    return !AudioId.IsEmpty() && AudioId.StartsWith(TEXT("Audio_"));
}

void UAuracronWorldPartitionAudioManager::OnAudioLoadedInternal(const FString& AudioId, bool bSuccess)
{
    OnAudioLoaded.Broadcast(AudioId, bSuccess);

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio loaded event: %s (success: %s)"), *AudioId, bSuccess ? TEXT("true") : TEXT("false"));
}

void UAuracronWorldPartitionAudioManager::OnAudioUnloadedInternal(const FString& AudioId)
{
    OnAudioUnloaded.Broadcast(AudioId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio unloaded event: %s"), *AudioId);
}

void UAuracronWorldPartitionAudioManager::ValidateConfiguration()
{
    // Validate streaming distances
    Configuration.AudioStreamingDistance = FMath::Max(0.0f, Configuration.AudioStreamingDistance);
    Configuration.AudioUnloadingDistance = FMath::Max(Configuration.AudioStreamingDistance, Configuration.AudioUnloadingDistance);

    // Validate LOD settings
    Configuration.BaseLODDistance = FMath::Max(0.0f, Configuration.BaseLODDistance);
    Configuration.LODDistanceMultiplier = FMath::Max(1.0f, Configuration.LODDistanceMultiplier);
    Configuration.MaxLODLevel = FMath::Clamp(Configuration.MaxLODLevel, 0, 4);

    // Validate volume settings
    Configuration.MasterVolume = FMath::Clamp(Configuration.MasterVolume, 0.0f, 1.0f);
    Configuration.AmbientVolume = FMath::Clamp(Configuration.AmbientVolume, 0.0f, 1.0f);
    Configuration.MusicVolume = FMath::Clamp(Configuration.MusicVolume, 0.0f, 1.0f);
    Configuration.SFXVolume = FMath::Clamp(Configuration.SFXVolume, 0.0f, 1.0f);

    // Validate performance settings
    Configuration.MaxConcurrentAudioSources = FMath::Max(1, Configuration.MaxConcurrentAudioSources);
    Configuration.MaxAudioMemoryUsageMB = FMath::Max(50.0f, Configuration.MaxAudioMemoryUsageMB);

    // Validate spatialization settings
    Configuration.MaxAudibleDistance = FMath::Max(100.0f, Configuration.MaxAudibleDistance);
    Configuration.FalloffDistance = FMath::Max(10.0f, Configuration.FalloffDistance);
}

void UAuracronWorldPartitionAudioManager::UpdateAudioCellMapping(const FString& AudioId)
{
    const FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return;
    }

    // Get grid manager to determine cell
    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager || !GridManager->IsInitialized())
    {
        return;
    }

    // Get cell at audio location
    FAuracronGridCell Cell = GridManager->GetCellAtLocation(AudioDesc->Location);
    if (!Cell.CellId.IsEmpty())
    {
        MoveAudioSourceToCell(AudioId, Cell.CellId);
    }
}

bool UAuracronWorldPartitionAudioManager::CreateAudioComponent(const FString& AudioId, const FString& SoundAssetPath)
{
    // In a real implementation, this would create actual audio component
    // For now, we'll just simulate the creation

    FScopeLock Lock(&AudioLock);

    FAuracronAudioDescriptor* AudioDesc = AudioDescriptors.Find(AudioId);
    if (!AudioDesc)
    {
        return false;
    }

    // Simulate audio component creation
    // In a real implementation, this would:
    // 1. Load the sound asset from SoundAssetPath
    // 2. Create UAudioComponent
    // 3. Configure component properties
    // 4. Store weak reference in AudioComponents map

    AURACRON_WP_LOG_VERBOSE(TEXT("Audio component created: %s"), *AudioId);

    return true;
}

bool UAuracronWorldPartitionAudioManager::IsAudioAudible(const FAuracronAudioDescriptor& Audio, const FVector& ListenerLocation) const
{
    if (!Audio.bIsEnabled)
    {
        return false;
    }

    if (!Audio.bIs3D)
    {
        return true; // 2D audio is always audible
    }

    float Distance = FVector::Dist(ListenerLocation, Audio.Location);
    return Distance <= Audio.MaxAudibleDistance;
}
