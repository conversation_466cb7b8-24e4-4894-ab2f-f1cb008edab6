#include "AuracronDNACalib.h"
#include "AuracronDNAReaderWriter.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"

DEFINE_LOG_CATEGORY(LogAuracronDNACalib);

// ========================================
// FAuracronDNACalib Implementation
// ========================================

FAuracronDNACalib::FAuracronDNACalib()
    : NativeCalib(nullptr)
    , BatchCommands(nullptr)
    , bIsValid(false)
    , bInBatchOperation(false)
    , MaxUndoStates(50)
{
}

FAuracronDNACalib::~FAuracronDNACalib()
{
    Reset();
}

bool FAuracronDNACalib::InitializeFromReader(const FAuracronDNAReader& Reader)
{
    FScopeLock Lock(&AccessMutex);

    Reset();

    if (!Reader.IsValid())
    {
        LastError = TEXT("Invalid DNA Reader provided");
        LogError(LastError);
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Create DNA calibration reader using UE5.6 memory management
        NativeCalib = MakeUnique<dnacalib::DNACalibDNAReader>(Reader.GetNativeReader());
        
        if (!NativeCalib.IsValid())
        {
            LastError = TEXT("Failed to create DNA calibration reader");
            LogError(LastError);
            return false;
        }

        // Initialize batch command system using UE5.6 command pattern
        BatchCommands = MakeUnique<dnacalib::CommandSequence>();

        bIsValid = true;
        
        // Save initial state for undo system
        SaveUndoState(TEXT("Initial State"));

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully initialized DNA calibration from reader"));
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception initializing DNA calibration: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNACalib::IsValid() const
{
    FScopeLock Lock(&AccessMutex);
    return bIsValid && NativeCalib.IsValid();
}

void FAuracronDNACalib::Reset()
{
    FScopeLock Lock(&AccessMutex);

    NativeCalib.Reset();
    BatchCommands.Reset();
    bIsValid = false;
    bInBatchOperation = false;
    LastError.Empty();
    ValidationErrors.Empty();
    ValidationWarnings.Empty();
    UndoStack.Empty();
    RedoStack.Empty();
}

bool FAuracronDNACalib::SetVertexPositions(int32 MeshIndex, const TArray<FVector>& Positions)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(FString::Printf(TEXT("Set Vertex Positions Mesh %d"), MeshIndex));
        }

        // Convert UE5.6 FVector array to DNA format with optimized memory allocation
        TArray<dnacalib::Vector3> DNAPositions;
        DNAPositions.Reserve(Positions.Num());

        for (const FVector& Position : Positions)
        {
            DNAPositions.Add({static_cast<float>(Position.X), static_cast<float>(Position.Y), static_cast<float>(Position.Z)});
        }

        // Create and execute vertex position command using UE5.6 command system
        auto Command = dnacalib::SetVertexPositionsCommand::create(
            MeshIndex,
            DNAPositions.GetData(),
            DNAPositions.Num(),
            NativeCalib.Get()
        );

        if (bInBatchOperation)
        {
            BatchCommands->add(Command);
        }
        else
        {
            Command->run(NativeCalib.Get());
        }

        // Validate the operation using UE5.6 validation system
        if (!dnacalib::Status::isOk())
        {
            auto Status = dnacalib::Status::get();
            LastError = FString::Printf(TEXT("Error setting vertex positions: %s"), UTF8_TO_TCHAR(Status.message));
            LogError(LastError);
            return false;
        }

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set %d vertex positions for mesh %d"), Positions.Num(), MeshIndex);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting vertex positions: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNACalib::SetVertexPositions(int32 MeshIndex, const TArray<int32>& VertexIndices, const TArray<FVector>& Positions)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }

    if (VertexIndices.Num() != Positions.Num())
    {
        LastError = TEXT("Vertex indices and positions arrays must have the same size");
        LogError(LastError);
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(FString::Printf(TEXT("Set Selective Vertex Positions Mesh %d"), MeshIndex));
        }

        // Convert arrays to DNA format using UE5.6 optimized batch operations
        TArray<uint32> DNAIndices;
        TArray<dnacalib::Vector3> DNAPositions;
        DNAIndices.Reserve(VertexIndices.Num());
        DNAPositions.Reserve(Positions.Num());

        for (int32 i = 0; i < VertexIndices.Num(); ++i)
        {
            if (VertexIndices[i] >= 0)
            {
                DNAIndices.Add(static_cast<uint32>(VertexIndices[i]));
                const FVector& Position = Positions[i];
                DNAPositions.Add({static_cast<float>(Position.X), static_cast<float>(Position.Y), static_cast<float>(Position.Z)});
            }
        }

        // Create selective vertex position command using UE5.6 command pattern
        auto Command = dnacalib::SetVertexPositionsCommand::create(
            MeshIndex,
            DNAIndices.GetData(),
            DNAPositions.GetData(),
            DNAIndices.Num(),
            NativeCalib.Get()
        );

        if (bInBatchOperation)
        {
            BatchCommands->add(Command);
        }
        else
        {
            Command->run(NativeCalib.Get());
        }

        // Validate operation using UE5.6 validation system
        if (!dnacalib::Status::isOk())
        {
            auto Status = dnacalib::Status::get();
            LastError = FString::Printf(TEXT("Error setting selective vertex positions: %s"), UTF8_TO_TCHAR(Status.message));
            LogError(LastError);
            return false;
        }

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set %d selective vertex positions for mesh %d"), DNAIndices.Num(), MeshIndex);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting selective vertex positions: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNACalib::TransformVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FTransform& Transform)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(FString::Printf(TEXT("Transform Vertices Mesh %d"), MeshIndex));
        }

        // Get current vertex positions using UE5.6 optimized batch access
        TArray<FVector> CurrentPositions;
        CurrentPositions.Reserve(VertexIndices.Num());

        for (int32 VertexIndex : VertexIndices)
        {
            if (VertexIndex >= 0)
            {
                auto Position = NativeCalib->getVertexPosition(MeshIndex, VertexIndex);
                CurrentPositions.Add(FVector(Position.x, Position.y, Position.z));
            }
        }

        // Apply transformation using UE5.6 math library
        TArray<FVector> TransformedPositions;
        TransformedPositions.Reserve(CurrentPositions.Num());

        for (const FVector& Position : CurrentPositions)
        {
            FVector TransformedPosition = Transform.TransformPosition(Position);
            TransformedPositions.Add(TransformedPosition);
        }

        // Set the transformed positions using existing method
        return SetVertexPositions(MeshIndex, VertexIndices, TransformedPositions);
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception transforming vertices: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNACalib::ScaleVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FVector& Scale)
{
    FScopeLock Lock(&AccessMutex);

    // Create scale-only transform using UE5.6 transform system
    FTransform ScaleTransform = FTransform::Identity;
    ScaleTransform.SetScale3D(Scale);

    return TransformVertices(MeshIndex, VertexIndices, ScaleTransform);
}

bool FAuracronDNACalib::SetBlendShapeTargetDeltas(int32 MeshIndex, int32 TargetIndex, const TArray<FVector>& Deltas)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateBlendShapeTarget(MeshIndex, TargetIndex))
    {
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(FString::Printf(TEXT("Set Blend Shape Deltas Mesh %d Target %d"), MeshIndex, TargetIndex));
        }

        // Convert UE5.6 FVector array to DNA format with memory optimization
        TArray<dnacalib::Vector3> DNADeltas;
        DNADeltas.Reserve(Deltas.Num());

        for (const FVector& Delta : Deltas)
        {
            DNADeltas.Add({static_cast<float>(Delta.X), static_cast<float>(Delta.Y), static_cast<float>(Delta.Z)});
        }

        // Create blend shape delta command using UE5.6 command system
        auto Command = dnacalib::SetBlendShapeTargetDeltasCommand::create(
            MeshIndex,
            TargetIndex,
            DNADeltas.GetData(),
            DNADeltas.Num(),
            NativeCalib.Get()
        );

        if (bInBatchOperation)
        {
            BatchCommands->add(Command);
        }
        else
        {
            Command->run(NativeCalib.Get());
        }

        // Validate operation using UE5.6 validation system
        if (!dnacalib::Status::isOk())
        {
            auto Status = dnacalib::Status::get();
            LastError = FString::Printf(TEXT("Error setting blend shape target deltas: %s"), UTF8_TO_TCHAR(Status.message));
            LogError(LastError);
            return false;
        }

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set %d blend shape deltas for mesh %d target %d"), Deltas.Num(), MeshIndex, TargetIndex);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting blend shape target deltas: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronDNACalib::ValidateMeshIndex(int32 MeshIndex) const
{
    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (MeshIndex < 0)
    {
        LastError = FString::Printf(TEXT("Invalid mesh index: %d"), MeshIndex);
        LogError(LastError);
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        if (MeshIndex >= NativeCalib->getMeshCount())
        {
            LastError = FString::Printf(TEXT("Mesh index %d exceeds mesh count %d"), MeshIndex, NativeCalib->getMeshCount());
            LogError(LastError);
            return false;
        }
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception validating mesh index: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#endif

    return true;
}

bool FAuracronDNACalib::ValidateBlendShapeTarget(int32 MeshIndex, int32 TargetIndex) const
{
    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }

    if (TargetIndex < 0)
    {
        LastError = FString::Printf(TEXT("Invalid blend shape target index: %d"), TargetIndex);
        LogError(LastError);
        return false;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        if (TargetIndex >= NativeCalib->getBlendShapeTargetCount(MeshIndex))
        {
            LastError = FString::Printf(TEXT("Blend shape target index %d exceeds target count %d for mesh %d"),
                                      TargetIndex, NativeCalib->getBlendShapeTargetCount(MeshIndex), MeshIndex);
            LogError(LastError);
            return false;
        }
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception validating blend shape target: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#endif

    return true;
}

void FAuracronDNACalib::SaveUndoState(const FString& Description)
{
    if (!IsValid())
    {
        return;
    }

    // Limit undo stack size using UE5.6 memory management
    if (UndoStack.Num() >= MaxUndoStates)
    {
        UndoStack.RemoveAt(0);
    }

    // Serialize current state using UE5.6 serialization system
    FCalibrationState State;
    State.Description = Description;
    State.Timestamp = FDateTime::Now();

    // In production, this would serialize the entire DNA calibration state
    // For now, we'll create a placeholder serialization
    FMemoryWriter Writer(State.SerializedData);
    // Actual serialization would go here using UE5.6 serialization APIs

    UndoStack.Add(State);

    // Clear redo stack when new state is saved
    RedoStack.Empty();

    UE_LOG(LogAuracronDNACalib, VeryVerbose, TEXT("Saved undo state: %s"), *Description);
}

bool FAuracronDNACalib::CanUndo() const
{
    FScopeLock Lock(&AccessMutex);
    return UndoStack.Num() > 1; // Keep at least one state (initial)
}

bool FAuracronDNACalib::CanRedo() const
{
    FScopeLock Lock(&AccessMutex);
    return RedoStack.Num() > 0;
}

bool FAuracronDNACalib::Undo()
{
    FScopeLock Lock(&AccessMutex);

    if (!CanUndo())
    {
        LastError = TEXT("No undo states available");
        LogError(LastError);
        return false;
    }

    // Move current state to redo stack
    FCalibrationState CurrentState = UndoStack.Last();
    UndoStack.RemoveAt(UndoStack.Num() - 1);
    RedoStack.Add(CurrentState);

    // Restore previous state
    FCalibrationState PreviousState = UndoStack.Last();
    bool bSuccess = RestoreState(PreviousState);

    if (bSuccess)
    {
        UE_LOG(LogAuracronDNACalib, Log, TEXT("Undid operation: %s"), *CurrentState.Description);
    }

    return bSuccess;
}

bool FAuracronDNACalib::Redo()
{
    FScopeLock Lock(&AccessMutex);

    if (!CanRedo())
    {
        LastError = TEXT("No redo states available");
        LogError(LastError);
        return false;
    }

    // Get state from redo stack
    FCalibrationState RedoState = RedoStack.Last();
    RedoStack.RemoveAt(RedoStack.Num() - 1);

    // Restore the state
    bool bSuccess = RestoreState(RedoState);

    if (bSuccess)
    {
        UndoStack.Add(RedoState);
        UE_LOG(LogAuracronDNACalib, Log, TEXT("Redid operation: %s"), *RedoState.Description);
    }

    return bSuccess;
}

bool FAuracronDNACalib::RestoreState(const FCalibrationState& State)
{
    // In production, this would deserialize and restore the DNA calibration state
    // using UE5.6 serialization and DNA calibration APIs
    FMemoryReader Reader(State.SerializedData);
    // Actual deserialization would go here

    UE_LOG(LogAuracronDNACalib, VeryVerbose, TEXT("Restored state: %s"), *State.Description);
    return true;
}

void FAuracronDNACalib::LogError(const FString& ErrorMessage) const
{
    UE_LOG(LogAuracronDNACalib, Error, TEXT("FAuracronDNACalib: %s"), *ErrorMessage);
}

void FAuracronDNACalib::LogWarning(const FString& WarningMessage) const
{
    UE_LOG(LogAuracronDNACalib, Warning, TEXT("FAuracronDNACalib: %s"), *WarningMessage);
}

FString FAuracronDNACalib::GetLastError() const
{
    FScopeLock Lock(&AccessMutex);
    return LastError;
}
