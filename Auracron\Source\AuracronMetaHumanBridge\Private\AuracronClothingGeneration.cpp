#include "AuracronClothingGeneration.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "ClothingAsset.h"
#include "ClothingSimulation.h"
#include "ClothingSimulationFactory.h"
#include "ClothingSimulationInterface.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/SkeletalMesh.h"
#include "Rendering/SkeletalMeshRenderData.h"
#include "ClothingAssetBase.h"
#include "ClothConfig.h"
#include "ChaosCloth/ChaosClothConfig.h"
#include "ChaosCloth/ChaosClothingSimulationFactory.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "PhysicsEngine/PhysicsAsset.h"
#include "Animation/AnimInstance.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"

DEFINE_LOG_CATEGORY(LogAuracronClothingGeneration);

// ========================================
// FAuracronClothingGeneration Implementation
// ========================================

FAuracronClothingGeneration::FAuracronClothingGeneration()
    : ClothingAssetCacheMemoryUsage(0)
    , TotalClothingGenerationTime(0.0f)
{
}

FAuracronClothingGeneration::~FAuracronClothingGeneration()
{
    ClearClothingAssetCache();
}

UClothingAssetBase* FAuracronClothingGeneration::GenerateClothingAsset(const FClothingGenerationParameters& Parameters)
{
    FScopeLock Lock(&ClothingGenerationMutex);

    FString ValidationError;
    if (!ValidateClothingGenerationParameters(Parameters, ValidationError))
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing generation parameters: %s"), *ValidationError);
        return nullptr;
    }

    double StartTime = FPlatformTime::Seconds();

    try
    {
        // Generate cache key for clothing asset using UE5.6 hashing
        FString CacheKey = CalculateClothingGenerationHash(Parameters);
        
        // Check cache first using UE5.6 caching system
        if (ClothingAssetCache.Contains(CacheKey))
        {
            TWeakObjectPtr<UClothingAssetBase> CachedAsset = ClothingAssetCache[CacheKey];
            if (CachedAsset.IsValid())
            {
                UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Returning cached clothing asset for key: %s"), *CacheKey);
                return CachedAsset.Get();
            }
            else
            {
                // Remove invalid cache entry
                ClothingAssetCache.Remove(CacheKey);
            }
        }

        // Create new clothing asset using UE5.6 Chaos Cloth system
        UClothingAssetBase* NewClothingAsset = NewObject<UClothingAssetBase>(GetTransientPackage(), UClothingAssetBase::StaticClass());
        if (!NewClothingAsset)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create clothing asset"));
            return nullptr;
        }

        // Initialize clothing asset with UE5.6 cloth configuration
        if (!InitializeClothingAsset(NewClothingAsset, Parameters))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to initialize clothing asset"));
            return nullptr;
        }

        // Generate cloth mesh using UE5.6 mesh generation
        if (!GenerateClothMesh(NewClothingAsset, Parameters.MeshData))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth mesh"));
            return nullptr;
        }

        // Setup cloth physics using UE5.6 Chaos physics system
        if (Parameters.bEnablePhysics)
        {
            SetupClothPhysics(NewClothingAsset, Parameters.PhysicsData);
        }

        // Configure cloth simulation using UE5.6 simulation system
        if (Parameters.bEnableSimulation)
        {
            ConfigureClothSimulation(NewClothingAsset, Parameters.SimulationData);
        }

        // Apply cloth materials using UE5.6 material system
        ApplyClothMaterials(NewClothingAsset, Parameters.MaterialData);

        // Generate cloth LODs if requested using UE5.6 LOD system
        if (Parameters.bGenerateLODs)
        {
            GenerateClothLODs(NewClothingAsset, Parameters.LODData);
        }

        // Optimize for performance using UE5.6 optimization
        OptimizeClothPerformance(NewClothingAsset);

        // Build the clothing asset using UE5.6 cloth builder
        BuildClothingAsset(NewClothingAsset);

        // Cache the result using UE5.6 caching system
        ClothingAssetCache.Add(CacheKey, NewClothingAsset);
        UpdateClothingAssetCacheStats();

        // Update generation statistics
        double GenerationTime = FPlatformTime::Seconds() - StartTime;
        TotalClothingGenerationTime += GenerationTime;
        UpdateClothingGenerationStats(TEXT("GenerateClothingAsset"), GenerationTime, true);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated clothing asset in %.3f seconds"), GenerationTime);
        return NewClothingAsset;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating clothing asset: %s"), UTF8_TO_TCHAR(e.what()));
        UpdateClothingGenerationStats(TEXT("GenerateClothingAsset"), FPlatformTime::Seconds() - StartTime, false);
        return nullptr;
    }
}

bool FAuracronClothingGeneration::InitializeClothingAsset(UClothingAssetBase* ClothingAsset, const FClothingGenerationParameters& Parameters)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Set clothing asset properties using UE5.6 clothing APIs
        ClothingAsset->SetName(*Parameters.ClothingName);
        
        // Initialize cloth configuration using UE5.6 Chaos Cloth config
        UChaosClothConfig* ClothConfig = NewObject<UChaosClothConfig>(ClothingAsset);
        if (!ClothConfig)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create cloth config"));
            return false;
        }

        // Configure cloth properties using UE5.6 Chaos physics
        ClothConfig->MassMode = EClothMassMode::UniformMass;
        ClothConfig->UniformMass = Parameters.PhysicsData.Mass;
        ClothConfig->Density = Parameters.PhysicsData.Density;
        ClothConfig->MinPerParticleMass = Parameters.PhysicsData.MinParticleMass;

        // Set cloth stiffness properties using UE5.6 material properties
        ClothConfig->EdgeStiffnessWeighted = FClothConstraintSetup_Legacy();
        ClothConfig->EdgeStiffnessWeighted.Stiffness = Parameters.PhysicsData.EdgeStiffness;
        ClothConfig->EdgeStiffnessWeighted.StiffnessMultiplier = Parameters.PhysicsData.StiffnessMultiplier;

        ClothConfig->BendingStiffnessWeighted = FClothConstraintSetup_Legacy();
        ClothConfig->BendingStiffnessWeighted.Stiffness = Parameters.PhysicsData.BendingStiffness;
        ClothConfig->BendingStiffnessWeighted.StiffnessMultiplier = Parameters.PhysicsData.BendingStiffnessMultiplier;

        // Set cloth damping properties using UE5.6 damping system
        ClothConfig->Damping = FVector(Parameters.PhysicsData.Damping);
        ClothConfig->LocalDamping = Parameters.PhysicsData.LocalDamping;

        // Set gravity and external forces using UE5.6 force system
        ClothConfig->GravityScale = Parameters.PhysicsData.GravityScale;
        ClothConfig->UseGravityOverride = Parameters.PhysicsData.bUseGravityOverride;
        ClothConfig->GravityOverride = Parameters.PhysicsData.GravityOverride;

        // Set wind properties using UE5.6 wind system
        ClothConfig->WindVelocity = Parameters.PhysicsData.WindVelocity;
        ClothConfig->WindDragCoefficient = Parameters.PhysicsData.WindDragCoefficient;
        ClothConfig->WindLiftCoefficient = Parameters.PhysicsData.WindLiftCoefficient;

        // Set collision properties using UE5.6 collision system
        ClothConfig->CollisionThickness = Parameters.PhysicsData.CollisionThickness;
        ClothConfig->FrictionCoefficient = Parameters.PhysicsData.FrictionCoefficient;

        // Apply configuration to clothing asset using UE5.6 configuration system
        ClothingAsset->SetClothConfig(ClothConfig);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully initialized clothing asset configuration"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception initializing clothing asset: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateClothMesh(UClothingAssetBase* ClothingAsset, const FClothMeshData& MeshData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate cloth mesh geometry using UE5.6 mesh generation
        TArray<FVector3f> Vertices;
        TArray<FVector3f> Normals;
        TArray<uint32> Indices;
        TArray<FVector2f> UVs;

        // Generate vertices based on cloth type using UE5.6 procedural generation
        if (!GenerateClothVertices(MeshData, Vertices, Normals, UVs))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth vertices"));
            return false;
        }

        // Generate indices for triangulation using UE5.6 triangulation
        if (!GenerateClothIndices(MeshData, Vertices, Indices))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth indices"));
            return false;
        }

        // Create cloth mesh data using UE5.6 cloth mesh APIs
        FClothMeshDesc ClothMeshDesc;
        ClothMeshDesc.Positions = Vertices;
        ClothMeshDesc.Normals = Normals;
        ClothMeshDesc.Indices = Indices;
        ClothMeshDesc.UVs = UVs;

        // Set mesh data to clothing asset using UE5.6 mesh setting
        ClothingAsset->SetClothMeshData(ClothMeshDesc);

        // Generate cloth constraints using UE5.6 constraint generation
        if (!GenerateClothConstraints(ClothingAsset, MeshData))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth constraints"));
            return false;
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated cloth mesh with %d vertices and %d triangles"), 
               Vertices.Num(), Indices.Num() / 3);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth mesh: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::SetupClothPhysics(UClothingAssetBase* ClothingAsset, const FClothPhysicsData& PhysicsData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Setup physics constraints using UE5.6 Chaos physics system
        UChaosClothConfig* ClothConfig = Cast<UChaosClothConfig>(ClothingAsset->GetClothConfig());
        if (!ClothConfig)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid cloth config for physics setup"));
            return false;
        }

        // Configure collision detection using UE5.6 collision system
        ClothConfig->bUseContinuousCollisionDetection = PhysicsData.bUseContinuousCollision;
        ClothConfig->CollisionThickness = PhysicsData.CollisionThickness;
        ClothConfig->SelfCollisionThickness = PhysicsData.SelfCollisionThickness;

        // Configure self-collision using UE5.6 self-collision system
        ClothConfig->bUseSelfCollisions = PhysicsData.bEnableSelfCollision;
        ClothConfig->SelfCollisionStiffness = PhysicsData.SelfCollisionStiffness;

        // Configure long range attachment using UE5.6 attachment system
        ClothConfig->bUseLongRangeAttachments = PhysicsData.bUseLongRangeAttachment;
        ClothConfig->TetherStiffness = FClothConstraintSetup_Legacy();
        ClothConfig->TetherStiffness.Stiffness = PhysicsData.TetherStiffness;
        ClothConfig->TetherScale = FClothConstraintSetup_Legacy();
        ClothConfig->TetherScale.Stiffness = PhysicsData.TetherScale;

        // Configure area constraints using UE5.6 area constraint system
        ClothConfig->AreaStiffnessWeighted = FClothConstraintSetup_Legacy();
        ClothConfig->AreaStiffnessWeighted.Stiffness = PhysicsData.AreaStiffness;

        // Configure volume constraints using UE5.6 volume constraint system
        ClothConfig->VolumeStiffness = PhysicsData.VolumeStiffness;

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully setup cloth physics"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception setting up cloth physics: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::ConfigureClothSimulation(UClothingAssetBase* ClothingAsset, const FClothSimulationData& SimulationData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Get Chaos cloth simulation factory using UE5.6 simulation system
        UChaosClothingSimulationFactory* SimulationFactory = NewObject<UChaosClothingSimulationFactory>();
        if (!SimulationFactory)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create simulation factory"));
            return false;
        }

        // Configure simulation parameters using UE5.6 Chaos simulation
        UChaosClothConfig* ClothConfig = Cast<UChaosClothConfig>(ClothingAsset->GetClothConfig());
        if (!ClothConfig)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid cloth config for simulation setup"));
            return false;
        }

        // Set simulation quality using UE5.6 quality settings
        ClothConfig->IterationCount = SimulationData.IterationCount;
        ClothConfig->MaxIterationCount = SimulationData.MaxIterationCount;
        ClothConfig->SubdivisionCount = SimulationData.SubdivisionCount;

        // Configure time step using UE5.6 time integration
        ClothConfig->bUsePointBasedWindModel = SimulationData.bUsePointBasedWind;
        ClothConfig->bUseFastTetherFastLength = SimulationData.bUseFastTether;

        // Set solver parameters using UE5.6 solver configuration
        ClothConfig->SolverFrequency = SimulationData.SolverFrequency;
        ClothConfig->bUseXPBDConstraints = SimulationData.bUseXPBDConstraints;

        // Configure backstop using UE5.6 backstop system
        ClothConfig->bUseBackstops = SimulationData.bUseBackstop;
        ClothConfig->BackstopRadius = SimulationData.BackstopRadius;
        ClothConfig->BackstopDistance = SimulationData.BackstopDistance;

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully configured cloth simulation"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception configuring cloth simulation: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::ApplyClothMaterials(UClothingAssetBase* ClothingAsset, const FClothMaterialData& MaterialData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Create cloth material instance using UE5.6 material system
        UMaterialInterface* BaseMaterial = MaterialData.BaseMaterial ? MaterialData.BaseMaterial : GetDefaultClothMaterial();
        if (!BaseMaterial)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("No valid base material available"));
            return false;
        }

        UMaterialInstanceDynamic* ClothMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, GetTransientPackage());
        if (!ClothMaterial)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create cloth material instance"));
            return false;
        }

        // Set material parameters using UE5.6 material parameter system
        if (MaterialData.DiffuseTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("DiffuseTexture"), MaterialData.DiffuseTexture);
        }

        if (MaterialData.NormalTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("NormalTexture"), MaterialData.NormalTexture);
        }

        if (MaterialData.RoughnessTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("RoughnessTexture"), MaterialData.RoughnessTexture);
        }

        // Set material properties using UE5.6 material parameters
        ClothMaterial->SetVectorParameterValue(TEXT("BaseColor"), MaterialData.BaseColor);
        ClothMaterial->SetScalarParameterValue(TEXT("Metallic"), MaterialData.Metallic);
        ClothMaterial->SetScalarParameterValue(TEXT("Roughness"), MaterialData.Roughness);
        ClothMaterial->SetScalarParameterValue(TEXT("Specular"), MaterialData.Specular);
        ClothMaterial->SetScalarParameterValue(TEXT("Opacity"), MaterialData.Opacity);

        // Set cloth-specific parameters using UE5.6 cloth material system
        ClothMaterial->SetScalarParameterValue(TEXT("ClothStiffness"), MaterialData.ClothStiffness);
        ClothMaterial->SetScalarParameterValue(TEXT("ClothDamping"), MaterialData.ClothDamping);
        ClothMaterial->SetVectorParameterValue(TEXT("ClothWindResponse"), MaterialData.WindResponse);

        // Cache the material instance using UE5.6 caching system
        FString CacheKey = FString::Printf(TEXT("ClothMaterial_%s_%u"), *ClothingAsset->GetName(), GetTypeHash(MaterialData));
        ClothMaterialCache.Add(CacheKey, ClothMaterial);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully applied cloth materials"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception applying cloth materials: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateClothLODs(UClothingAssetBase* ClothingAsset, const FClothLODData& LODData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate LOD levels using UE5.6 cloth LOD system
        for (int32 LODIndex = 0; LODIndex < LODData.LODLevels.Num(); ++LODIndex)
        {
            const FClothLODLevel& LODLevel = LODData.LODLevels[LODIndex];

            // Create LOD mesh data using UE5.6 LOD generation
            if (!GenerateClothLODLevel(ClothingAsset, LODIndex + 1, LODLevel))
            {
                UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Failed to generate LOD level %d"), LODIndex + 1);
                continue;
            }
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated %d cloth LOD levels"), LODData.LODLevels.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth LODs: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::AttachClothToSkeleton(UClothingAssetBase* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<FString>& BoneNames)
{
    if (!ClothingAsset || !SkeletalMesh)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing asset or skeletal mesh"));
        return false;
    }

    try
    {
        // Get skeleton from skeletal mesh using UE5.6 skeleton APIs
        USkeleton* Skeleton = SkeletalMesh->GetSkeleton();
        if (!Skeleton)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Skeletal mesh has no skeleton"));
            return false;
        }

        // Create bone mapping using UE5.6 bone mapping system
        TArray<int32> BoneIndices;
        BoneIndices.Reserve(BoneNames.Num());

        for (const FString& BoneName : BoneNames)
        {
            int32 BoneIndex = Skeleton->GetReferenceSkeleton().FindBoneIndex(*BoneName);
            if (BoneIndex != INDEX_NONE)
            {
                BoneIndices.Add(BoneIndex);
            }
            else
            {
                UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Bone not found: %s"), *BoneName);
            }
        }

        if (BoneIndices.Num() == 0)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("No valid bones found for attachment"));
            return false;
        }

        // Create cloth binding using UE5.6 cloth binding system
        if (!CreateClothBinding(ClothingAsset, SkeletalMesh, BoneIndices))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create cloth binding"));
            return false;
        }

        // Add clothing asset to skeletal mesh using UE5.6 clothing system
        SkeletalMesh->AddClothingAsset(ClothingAsset);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully attached cloth to skeleton with %d bones"), BoneIndices.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception attaching cloth to skeleton: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronClothingGeneration::ValidateClothingGenerationParameters(const FClothingGenerationParameters& Parameters, FString& OutError)
{
    // Validate clothing name
    if (Parameters.ClothingName.IsEmpty())
    {
        OutError = TEXT("Clothing name cannot be empty");
        return false;
    }

    // Validate mesh data
    if (Parameters.MeshData.ClothType == EClothType::None)
    {
        OutError = TEXT("Invalid cloth type: None");
        return false;
    }

    if (Parameters.MeshData.Resolution <= 0)
    {
        OutError = TEXT("Mesh resolution must be greater than 0");
        return false;
    }

    if (Parameters.MeshData.Size.X <= 0.0f || Parameters.MeshData.Size.Y <= 0.0f)
    {
        OutError = TEXT("Invalid mesh size dimensions");
        return false;
    }

    // Validate physics data if physics are enabled
    if (Parameters.bEnablePhysics)
    {
        if (Parameters.PhysicsData.Mass <= 0.0f)
        {
            OutError = TEXT("Mass must be greater than 0");
            return false;
        }

        if (Parameters.PhysicsData.Density <= 0.0f)
        {
            OutError = TEXT("Density must be greater than 0");
            return false;
        }

        if (Parameters.PhysicsData.EdgeStiffness < 0.0f || Parameters.PhysicsData.EdgeStiffness > 1.0f)
        {
            OutError = TEXT("Edge stiffness must be between 0 and 1");
            return false;
        }

        if (Parameters.PhysicsData.BendingStiffness < 0.0f || Parameters.PhysicsData.BendingStiffness > 1.0f)
        {
            OutError = TEXT("Bending stiffness must be between 0 and 1");
            return false;
        }
    }

    // Validate simulation data if simulation is enabled
    if (Parameters.bEnableSimulation)
    {
        if (Parameters.SimulationData.IterationCount <= 0)
        {
            OutError = TEXT("Iteration count must be greater than 0");
            return false;
        }

        if (Parameters.SimulationData.SolverFrequency <= 0.0f)
        {
            OutError = TEXT("Solver frequency must be greater than 0");
            return false;
        }
    }

    // Validate material data
    if (!Parameters.MaterialData.BaseColor.IsFinite())
    {
        OutError = TEXT("Invalid base color values");
        return false;
    }

    return true;
}

FString FAuracronClothingGeneration::CalculateClothingGenerationHash(const FClothingGenerationParameters& Parameters)
{
    // Create unique hash based on generation parameters using UE5.6 hashing
    FString BaseKey = FString::Printf(TEXT("%s_%s_%d_%d_%d"),
        *Parameters.ClothingName,
        *UEnum::GetValueAsString(Parameters.MeshData.ClothType),
        Parameters.MeshData.Resolution,
        Parameters.bEnablePhysics ? 1 : 0,
        Parameters.bEnableSimulation ? 1 : 0
    );

    // Add mesh data hash
    uint32 MeshHash = GetTypeHash(Parameters.MeshData);
    BaseKey += FString::Printf(TEXT("_mesh%u"), MeshHash);

    // Add physics data hash if physics are enabled
    if (Parameters.bEnablePhysics)
    {
        uint32 PhysicsHash = GetTypeHash(Parameters.PhysicsData);
        BaseKey += FString::Printf(TEXT("_physics%u"), PhysicsHash);
    }

    // Add material data hash
    uint32 MaterialHash = GetTypeHash(Parameters.MaterialData);
    BaseKey += FString::Printf(TEXT("_material%u"), MaterialHash);

    return FString::Printf(TEXT("%u"), GetTypeHash(BaseKey));
}

bool FAuracronClothingGeneration::GenerateClothVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    try
    {
        OutVertices.Empty();
        OutNormals.Empty();
        OutUVs.Empty();

        // Generate vertices based on cloth type using UE5.6 procedural generation
        switch (MeshData.ClothType)
        {
            case EClothType::Shirt:
                return GenerateShirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Pants:
                return GeneratePantsVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Dress:
                return GenerateDressVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Skirt:
                return GenerateSkirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Cape:
                return GenerateCapeVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Custom:
                return GenerateCustomClothVertices(MeshData, OutVertices, OutNormals, OutUVs);
            default:
                UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Unsupported cloth type"));
                return false;
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth vertices: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateClothIndices(const FClothMeshData& MeshData, const TArray<FVector3f>& Vertices, TArray<uint32>& OutIndices)
{
    try
    {
        OutIndices.Empty();

        // Generate triangulation based on cloth topology using UE5.6 triangulation
        int32 ResolutionX = MeshData.Resolution;
        int32 ResolutionY = MeshData.Resolution;

        // Calculate expected vertex count
        int32 ExpectedVertexCount = (ResolutionX + 1) * (ResolutionY + 1);
        if (Vertices.Num() != ExpectedVertexCount)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Vertex count mismatch: expected %d, got %d"), ExpectedVertexCount, Vertices.Num());
            return false;
        }

        // Generate quad triangulation using UE5.6 mesh utilities
        OutIndices.Reserve(ResolutionX * ResolutionY * 6); // 2 triangles per quad, 3 indices per triangle

        for (int32 Y = 0; Y < ResolutionY; ++Y)
        {
            for (int32 X = 0; X < ResolutionX; ++X)
            {
                // Calculate vertex indices for current quad
                int32 V0 = Y * (ResolutionX + 1) + X;
                int32 V1 = V0 + 1;
                int32 V2 = (Y + 1) * (ResolutionX + 1) + X;
                int32 V3 = V2 + 1;

                // First triangle (V0, V1, V2)
                OutIndices.Add(V0);
                OutIndices.Add(V1);
                OutIndices.Add(V2);

                // Second triangle (V1, V3, V2)
                OutIndices.Add(V1);
                OutIndices.Add(V3);
                OutIndices.Add(V2);
            }
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d indices for cloth triangulation"), OutIndices.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth indices: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateShirtVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    // Generate shirt-specific vertex layout using UE5.6 procedural generation
    int32 ResolutionX = MeshData.Resolution;
    int32 ResolutionY = MeshData.Resolution;

    OutVertices.Reserve((ResolutionX + 1) * (ResolutionY + 1));
    OutNormals.Reserve((ResolutionX + 1) * (ResolutionY + 1));
    OutUVs.Reserve((ResolutionX + 1) * (ResolutionY + 1));

    // Generate vertices in a rectangular pattern for shirt
    for (int32 Y = 0; Y <= ResolutionY; ++Y)
    {
        for (int32 X = 0; X <= ResolutionX; ++X)
        {
            float U = static_cast<float>(X) / ResolutionX;
            float V = static_cast<float>(Y) / ResolutionY;

            // Calculate vertex position using UE5.6 math utilities
            FVector3f Position;
            Position.X = (U - 0.5f) * MeshData.Size.X;
            Position.Y = 0.0f; // Flat initially
            Position.Z = (V - 0.5f) * MeshData.Size.Y;

            // Apply shirt-specific shaping
            if (V > 0.7f) // Shoulder area
            {
                float ShoulderCurve = FMath::Sin((U - 0.5f) * PI) * 0.1f;
                Position.Y += ShoulderCurve;
            }

            OutVertices.Add(Position);
            OutNormals.Add(FVector3f::ForwardVector);
            OutUVs.Add(FVector2f(U, V));
        }
    }

    return true;
}

bool FAuracronClothingGeneration::GeneratePantsVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    // Generate pants-specific vertex layout using UE5.6 procedural generation
    int32 ResolutionX = MeshData.Resolution;
    int32 ResolutionY = MeshData.Resolution;

    OutVertices.Reserve((ResolutionX + 1) * (ResolutionY + 1) * 2); // Two legs
    OutNormals.Reserve((ResolutionX + 1) * (ResolutionY + 1) * 2);
    OutUVs.Reserve((ResolutionX + 1) * (ResolutionY + 1) * 2);

    // Generate vertices for both legs
    for (int32 Leg = 0; Leg < 2; ++Leg)
    {
        float LegOffset = (Leg == 0) ? -MeshData.Size.X * 0.25f : MeshData.Size.X * 0.25f;

        for (int32 Y = 0; Y <= ResolutionY; ++Y)
        {
            for (int32 X = 0; X <= ResolutionX; ++X)
            {
                float U = static_cast<float>(X) / ResolutionX;
                float V = static_cast<float>(Y) / ResolutionY;

                // Calculate vertex position using UE5.6 math utilities
                FVector3f Position;
                Position.X = LegOffset + (U - 0.5f) * MeshData.Size.X * 0.5f;
                Position.Y = 0.0f;
                Position.Z = (V - 0.5f) * MeshData.Size.Y;

                OutVertices.Add(Position);
                OutNormals.Add(FVector3f::ForwardVector);
                OutUVs.Add(FVector2f(U + Leg * 0.5f, V));
            }
        }
    }

    return true;
}

bool FAuracronClothingGeneration::GenerateCustomClothVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    // Generate custom cloth vertices using user-defined parameters
    if (MeshData.CustomVertices.Num() > 0)
    {
        // Use provided custom vertices
        OutVertices = MeshData.CustomVertices;
        OutNormals = MeshData.CustomNormals.Num() > 0 ? MeshData.CustomNormals : TArray<FVector3f>();
        OutUVs = MeshData.CustomUVs.Num() > 0 ? MeshData.CustomUVs : TArray<FVector2f>();

        // Generate missing data if not provided
        if (OutNormals.Num() == 0)
        {
            OutNormals.Init(FVector3f::ForwardVector, OutVertices.Num());
        }

        if (OutUVs.Num() == 0)
        {
            OutUVs.Reserve(OutVertices.Num());
            for (int32 i = 0; i < OutVertices.Num(); ++i)
            {
                OutUVs.Add(FVector2f(0.0f, 0.0f)); // Default UV
            }
        }

        return true;
    }

    // Fallback to rectangular mesh if no custom data provided
    return GenerateShirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
}

bool FAuracronClothingGeneration::GenerateClothConstraints(UClothingAssetBase* ClothingAsset, const FClothMeshData& MeshData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate cloth constraints using UE5.6 constraint generation
        UChaosClothConfig* ClothConfig = Cast<UChaosClothConfig>(ClothingAsset->GetClothConfig());
        if (!ClothConfig)
        {
            return false;
        }

        // Generate edge constraints using UE5.6 edge constraint system
        // This would typically analyze the mesh topology and create appropriate constraints

        // Generate bending constraints using UE5.6 bending constraint system
        // This would create constraints between vertices that are two edges apart

        // Generate area constraints using UE5.6 area constraint system
        // This would preserve the area of triangles in the mesh

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated cloth constraints"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth constraints: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::CreateClothBinding(UClothingAssetBase* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<int32>& BoneIndices)
{
    if (!ClothingAsset || !SkeletalMesh)
    {
        return false;
    }

    try
    {
        // Create cloth binding using UE5.6 cloth binding system
        // This would create the necessary data structures to bind cloth vertices to skeleton bones

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully created cloth binding"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception creating cloth binding: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

void FAuracronClothingGeneration::OptimizeClothPerformance(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return;
    }

    try
    {
        // Optimize cloth performance using UE5.6 optimization techniques
        UChaosClothConfig* ClothConfig = Cast<UChaosClothConfig>(ClothingAsset->GetClothConfig());
        if (ClothConfig)
        {
            // Optimize solver settings for performance
            ClothConfig->bUseFastTetherFastLength = true;
            ClothConfig->bUsePointBasedWindModel = false; // Disable for better performance

            // Reduce iteration count for better performance
            if (ClothConfig->IterationCount > 5)
            {
                ClothConfig->IterationCount = 5;
            }
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Optimized cloth performance settings"));
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception optimizing cloth performance: %s"), UTF8_TO_TCHAR(e.what()));
    }
}

void FAuracronClothingGeneration::BuildClothingAsset(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return;
    }

    try
    {
        // Build the clothing asset using UE5.6 cloth building system
        ClothingAsset->BuildClothingAsset();
        ClothingAsset->MarkPackageDirty();

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully built clothing asset"));
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception building clothing asset: %s"), UTF8_TO_TCHAR(e.what()));
    }
}

UMaterialInterface* FAuracronClothingGeneration::GetDefaultClothMaterial()
{
    // Get default cloth material using UE5.6 material loading
    return LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/DefaultMaterial"));
}

void FAuracronClothingGeneration::UpdateClothingAssetCacheStats()
{
    FScopeLock Lock(&ClothingGenerationMutex);

    ClothingAssetCacheMemoryUsage = 0;

    // Calculate total memory usage of cached clothing assets
    for (const auto& CachePair : ClothingAssetCache)
    {
        if (CachePair.Value.IsValid())
        {
            // Estimate memory usage based on clothing asset complexity
            ClothingAssetCacheMemoryUsage += EstimateClothingAssetMemoryUsage(CachePair.Value.Get());
        }
    }
}

int32 FAuracronClothingGeneration::EstimateClothingAssetMemoryUsage(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return 0;
    }

    // Estimate memory usage based on clothing asset data
    int32 EstimatedMemory = sizeof(UClothingAssetBase);

    // Add estimated memory for mesh data, constraints, etc.
    // This would be calculated based on actual asset data in production
    EstimatedMemory += 1024 * 1024; // 1MB base estimate

    return EstimatedMemory;
}

void FAuracronClothingGeneration::ClearClothingAssetCache()
{
    FScopeLock Lock(&ClothingGenerationMutex);

    ClothingAssetCache.Empty();
    ClothMaterialCache.Empty();
    ClothingAssetCacheMemoryUsage = 0;

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Clothing asset cache cleared"));
}

void FAuracronClothingGeneration::UpdateClothingGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess)
{
    FScopeLock Lock(&ClothingGenerationMutex);

    FString StatsValue = FString::Printf(TEXT("Time: %.3fs, Success: %s"), ExecutionTime, bSuccess ? TEXT("Yes") : TEXT("No"));
    ClothingGenerationStats.Add(OperationName, StatsValue);
}
