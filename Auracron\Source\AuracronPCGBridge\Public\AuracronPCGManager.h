// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - PCG Manager Header
// Bridge 2.1: PCG Framework - Core Infrastructure

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"
#include "AuracronPCGFramework.h"

// PCG Framework includes
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSubsystem.h"
#include "PCGWorldActor.h"

#include "AuracronPCGManager.generated.h"

// Forward declarations
class UAuracronPCGElementBase;
class UAuracronPCGGeneratorBase;
class UPCGComponent;
class UPCGGraph;

// Delegate declarations
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPCGGenerationComplete, bool, bSuccess, const FAuracronPCGPerformanceMetrics&, Metrics);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPCGGenerationProgress, float, Progress, const FString&, CurrentOperation);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPCGError, const FAuracronPCGErrorInfo&, ErrorInfo);

// PCG generation request structure
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGGenerationRequest
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    EAuracronPCGGenerationType GenerationType = EAuracronPCGGenerationType::Points;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    UPCGGraph* PCGGraph = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    AActor* TargetActor = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    FVector GenerationBounds = FVector(1000.0f, 1000.0f, 100.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    FAuracronPCGConfiguration Configuration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    TMap<FString, FString> Parameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bAsynchronous = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 Seed = 0;

    FAuracronPCGGenerationRequest()
    {
        Seed = FMath::Rand();
    }
};

// PCG generation result structure
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGGenerationResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FAuracronPCGPerformanceMetrics PerformanceMetrics;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FAuracronPCGErrorInfo> Errors;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<AActor*> GeneratedActors;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 PointsGenerated = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 MeshesGenerated = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString GenerationId;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FDateTime CompletionTime;

    FAuracronPCGGenerationResult()
    {
        GenerationId = FGuid::NewGuid().ToString();
        CompletionTime = FDateTime::Now();
    }
};

/**
 * Main manager class for AURACRON PCG Framework
 * Handles PCG graph execution, element management, and performance monitoring
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGManager : public UObject
{
    GENERATED_BODY()

public:
    UAuracronPCGManager();

    // Initialization and shutdown
    UFUNCTION(BlueprintCallable, Category = "PCG Manager")
    virtual void Initialize();

    UFUNCTION(BlueprintCallable, Category = "PCG Manager")
    virtual void Shutdown();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Manager")
    bool IsInitialized() const { return bIsInitialized; }

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "PCG Manager")
    void ApplyConfiguration(const FAuracronPCGConfiguration& NewConfiguration);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Manager")
    const FAuracronPCGConfiguration& GetConfiguration() const { return Configuration; }

    // PCG generation
    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    FString StartGeneration(const FAuracronPCGGenerationRequest& Request);

    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    bool StopGeneration(const FString& GenerationId);

    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    bool IsGenerationActive(const FString& GenerationId) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Generation")
    float GetGenerationProgress(const FString& GenerationId) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Generation")
    FAuracronPCGGenerationResult GetGenerationResult(const FString& GenerationId) const;

    // Graph management
    UFUNCTION(BlueprintCallable, Category = "PCG Graph")
    UPCGGraph* CreatePCGGraph(const FString& GraphName);

    UFUNCTION(BlueprintCallable, Category = "PCG Graph")
    bool ValidatePCGGraph(UPCGGraph* Graph, TArray<FString>& ValidationErrors);

    UFUNCTION(BlueprintCallable, Category = "PCG Graph")
    bool ExecutePCGGraph(UPCGGraph* Graph, AActor* TargetActor, const FAuracronPCGConfiguration& Config);

    // Element management
    UFUNCTION(BlueprintCallable, Category = "PCG Elements")
    void RegisterCustomElement(TSubclassOf<UAuracronPCGElementBase> ElementClass);

    UFUNCTION(BlueprintCallable, Category = "PCG Elements")
    void UnregisterCustomElement(TSubclassOf<UAuracronPCGElementBase> ElementClass);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "PCG Elements")
    TArray<TSubclassOf<UAuracronPCGElementBase>> GetRegisteredElements() const;

    // Performance monitoring
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Performance")
    FAuracronPCGPerformanceMetrics GetCurrentPerformanceMetrics() const;

    UFUNCTION(BlueprintCallable, Category = "Performance")
    void ResetPerformanceMetrics();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Performance")
    TArray<FString> GetPerformanceRecommendations() const;

    // Error handling
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Error Handling")
    TArray<FAuracronPCGErrorInfo> GetErrorHistory(int32 MaxEntries = 10) const;

    UFUNCTION(BlueprintCallable, Category = "Error Handling")
    void ClearErrorHistory();

    UFUNCTION(BlueprintCallable, Category = "Error Handling")
    void ReportError(const FAuracronPCGErrorInfo& ErrorInfo);

    // System information
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Info")
    FString GetVersionString() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Info")
    bool IsGPUAccelerationAvailable() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Info")
    int32 GetAvailableThreadCount() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "System Info")
    float GetSystemHealthScore() const;

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Utilities")
    void CleanupGeneratedContent();

    UFUNCTION(BlueprintCallable, Category = "Utilities")
    void OptimizeMemoryUsage();

    UFUNCTION(BlueprintCallable, Category = "Utilities")
    bool ValidateSystemRequirements(TArray<FString>& ValidationMessages);

    // Delegates
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPCGGenerationComplete OnGenerationComplete;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPCGGenerationProgress OnGenerationProgress;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPCGError OnError;

protected:
    // Core state
    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronPCGConfiguration Configuration;

    UPROPERTY()
    FAuracronPCGPerformanceMetrics PerformanceMetrics;

    // Generation management
    UPROPERTY()
    TMap<FString, FAuracronPCGGenerationRequest> ActiveGenerations;

    UPROPERTY()
    TMap<FString, FAuracronPCGGenerationResult> CompletedGenerations;

    UPROPERTY()
    TMap<FString, float> GenerationProgress;

    // Element registry
    UPROPERTY()
    TArray<TSubclassOf<UAuracronPCGElementBase>> RegisteredElements;

    // Error tracking
    UPROPERTY()
    TArray<FAuracronPCGErrorInfo> ErrorHistory;

    // PCG subsystem reference
    UPROPERTY()
    UPCGSubsystem* PCGSubsystem = nullptr;

    // Internal methods
    virtual void InitializePCGSubsystem();
    virtual void InitializeDefaultElements();
    virtual void InitializePerformanceMonitoring();

    virtual void UpdatePerformanceMetrics();
    virtual void ProcessGenerationQueue();
    virtual void HandleGenerationComplete(const FString& GenerationId, bool bSuccess);

    virtual bool ValidateGenerationRequest(const FAuracronPCGGenerationRequest& Request, TArray<FString>& ValidationErrors);
    virtual void ExecuteGenerationRequest(const FString& GenerationId, const FAuracronPCGGenerationRequest& Request);

    // Timer handles
    FTimerHandle PerformanceUpdateTimer;
    FTimerHandle GenerationQueueTimer;

private:
    // Internal state tracking
    mutable FCriticalSection GenerationLock;
    mutable FCriticalSection ErrorLock;
    mutable FCriticalSection PerformanceLock;

    // Generation ID counter
    static int32 GenerationIdCounter;
};

// Inline implementations for performance-critical functions
FORCEINLINE float UAuracronPCGManager::GetGenerationProgress(const FString& GenerationId) const
{
    FScopeLock Lock(&GenerationLock);
    if (const float* Progress = GenerationProgress.Find(GenerationId))
    {
        return *Progress;
    }
    return 0.0f;
}

FORCEINLINE bool UAuracronPCGManager::IsGenerationActive(const FString& GenerationId) const
{
    FScopeLock Lock(&GenerationLock);
    return ActiveGenerations.Contains(GenerationId);
}

// Static member initialization
int32 UAuracronPCGManager::GenerationIdCounter = 0;
