// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Spline System Header
// Bridge 2.8: PCG Framework - Spline System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGPointData.h"
#include "PCGSpatialData.h"
#include "PCGSplineData.h"
#include "PCGMetadata.h"

// Spline includes
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/SplineActor.h"
#include "SplineMeshActor.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Math/UnrealMathUtility.h"
#include "Curves/CurveFloat.h"
#include "Curves/CurveVector.h"

#include "AuracronPCGSplineSystem.generated.h"

// Forward declarations
class USplineComponent;
class USplineMeshComponent;
class ASplineActor;
class ASplineMeshActor;

// Spline creation modes
UENUM(BlueprintType)
enum class EAuracronPCGSplineCreationMode : uint8
{
    FromPoints          UMETA(DisplayName = "From Points"),
    FromCurve           UMETA(DisplayName = "From Curve"),
    FromPath            UMETA(DisplayName = "From Path"),
    Procedural          UMETA(DisplayName = "Procedural"),
    Bezier              UMETA(DisplayName = "Bezier"),
    CatmullRom          UMETA(DisplayName = "Catmull-Rom"),
    Linear              UMETA(DisplayName = "Linear"),
    Smooth              UMETA(DisplayName = "Smooth")
};

// Spline distribution modes
UENUM(BlueprintType)
enum class EAuracronPCGSplineDistributionMode : uint8
{
    Uniform             UMETA(DisplayName = "Uniform"),
    ByDistance          UMETA(DisplayName = "By Distance"),
    BySegment           UMETA(DisplayName = "By Segment"),
    ByParameter         UMETA(DisplayName = "By Parameter"),
    Random              UMETA(DisplayName = "Random"),
    Noise               UMETA(DisplayName = "Noise Based"),
    Curve               UMETA(DisplayName = "Curve Based"),
    Adaptive            UMETA(DisplayName = "Adaptive")
};

// Spline mesh generation modes
UENUM(BlueprintType)
enum class EAuracronPCGSplineMeshMode : uint8
{
    SingleMesh          UMETA(DisplayName = "Single Mesh"),
    SegmentMesh         UMETA(DisplayName = "Segment Mesh"),
    TiledMesh           UMETA(DisplayName = "Tiled Mesh"),
    AdaptiveMesh        UMETA(DisplayName = "Adaptive Mesh"),
    ProceduralMesh      UMETA(DisplayName = "Procedural Mesh"),
    InstancedMesh       UMETA(DisplayName = "Instanced Mesh"),
    SplineMeshComponent UMETA(DisplayName = "Spline Mesh Component"),
    CustomMesh          UMETA(DisplayName = "Custom Mesh")
};

// Spline tangent modes
UENUM(BlueprintType)
enum class EAuracronPCGSplineTangentMode : uint8
{
    Auto                UMETA(DisplayName = "Auto"),
    User                UMETA(DisplayName = "User"),
    Break               UMETA(DisplayName = "Break"),
    Linear              UMETA(DisplayName = "Linear"),
    Constant            UMETA(DisplayName = "Constant"),
    CurveClamped        UMETA(DisplayName = "Curve Clamped"),
    CurveBreak          UMETA(DisplayName = "Curve Break"),
    None                UMETA(DisplayName = "None")
};

// Path finding integration modes
UENUM(BlueprintType)
enum class EAuracronPCGPathFindingMode : uint8
{
    None                UMETA(DisplayName = "None"),
    AStar               UMETA(DisplayName = "A* Algorithm"),
    Dijkstra            UMETA(DisplayName = "Dijkstra"),
    FlowField           UMETA(DisplayName = "Flow Field"),
    NavMesh             UMETA(DisplayName = "NavMesh"),
    Custom              UMETA(DisplayName = "Custom Algorithm"),
    Hybrid              UMETA(DisplayName = "Hybrid"),
    RRT                 UMETA(DisplayName = "RRT (Rapidly-exploring Random Tree)")
};

// Spline deformation modes
UENUM(BlueprintType)
enum class EAuracronPCGSplineDeformationMode : uint8
{
    None                UMETA(DisplayName = "None"),
    Bend                UMETA(DisplayName = "Bend"),
    Twist               UMETA(DisplayName = "Twist"),
    Scale               UMETA(DisplayName = "Scale"),
    Noise               UMETA(DisplayName = "Noise"),
    Wave                UMETA(DisplayName = "Wave"),
    Spiral              UMETA(DisplayName = "Spiral"),
    Custom              UMETA(DisplayName = "Custom")
};

// =============================================================================
// SPLINE POINT DESCRIPTOR
// =============================================================================

/**
 * Spline Point Descriptor
 * Describes a spline point with all its properties
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGSplinePointDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point")
    FVector ArriveTangent = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point")
    FVector LeaveTangent = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point")
    FVector Scale = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point")
    EAuracronPCGSplineTangentMode TangentMode = EAuracronPCGSplineTangentMode::Auto;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
    float Width = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
    float Speed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
    float Density = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
    FString PointType = TEXT("Default");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
    TMap<FString, float> CustomAttributes;

    FAuracronPCGSplinePointDescriptor()
    {
        Location = FVector::ZeroVector;
        ArriveTangent = FVector::ZeroVector;
        LeaveTangent = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Scale = FVector::OneVector;
        TangentMode = EAuracronPCGSplineTangentMode::Auto;
        Width = 100.0f;
        Speed = 1.0f;
        Density = 1.0f;
        PointType = TEXT("Default");
    }
};

// =============================================================================
// SPLINE MESH DESCRIPTOR
// =============================================================================

/**
 * Spline Mesh Descriptor
 * Describes parameters for spline mesh generation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGSplineMeshDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    TSoftObjectPtr<UStaticMesh> StaticMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    EAuracronPCGSplineMeshMode MeshMode = EAuracronPCGSplineMeshMode::SplineMeshComponent;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    TArray<TSoftObjectPtr<UMaterialInterface>> MaterialOverrides;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tiling")
    bool bTileMesh = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tiling", meta = (EditCondition = "bTileMesh"))
    float TileLength = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tiling", meta = (EditCondition = "bTileMesh"))
    bool bStretchToFit = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Deformation")
    EAuracronPCGSplineDeformationMode DeformationMode = EAuracronPCGSplineDeformationMode::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Deformation")
    float DeformationStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scaling")
    bool bUseCustomScale = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scaling", meta = (EditCondition = "bUseCustomScale"))
    TSoftObjectPtr<UCurveVector> ScaleCurve;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    bool bGenerateCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision", meta = (EditCondition = "bGenerateCollision"))
    bool bComplexCollision = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bGenerateLODs = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD", meta = (EditCondition = "bGenerateLODs"))
    int32 LODCount = 3;

    FAuracronPCGSplineMeshDescriptor()
    {
        MeshMode = EAuracronPCGSplineMeshMode::SplineMeshComponent;
        bTileMesh = false;
        TileLength = 100.0f;
        bStretchToFit = false;
        DeformationMode = EAuracronPCGSplineDeformationMode::None;
        DeformationStrength = 1.0f;
        bUseCustomScale = false;
        bGenerateCollision = true;
        bComplexCollision = false;
        bGenerateLODs = false;
        LODCount = 3;
    }
};

// =============================================================================
// PATH FINDING DESCRIPTOR
// =============================================================================

/**
 * Path Finding Descriptor
 * Describes parameters for path finding integration
 */
USTRUCT(BlueprintType)
struct AURACRONPCGFRAMEWORK_API FAuracronPCGPathFindingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Finding")
    EAuracronPCGPathFindingMode PathFindingMode = EAuracronPCGPathFindingMode::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Finding")
    FVector StartLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Finding")
    FVector EndLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Finding")
    TArray<FVector> Waypoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Obstacles")
    bool bAvoidObstacles = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Obstacles", meta = (EditCondition = "bAvoidObstacles"))
    float ObstacleAvoidanceRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Obstacles", meta = (EditCondition = "bAvoidObstacles"))
    TArray<FString> ObstacleTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Smoothing")
    bool bSmoothPath = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Smoothing", meta = (EditCondition = "bSmoothPath"))
    float SmoothingStrength = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Smoothing", meta = (EditCondition = "bSmoothPath"))
    int32 SmoothingIterations = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    float MaxSlope = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    float MinWidth = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraints")
    bool bStayOnNavMesh = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxSearchNodes = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float SearchTimeout = 5.0f;

    FAuracronPCGPathFindingDescriptor()
    {
        PathFindingMode = EAuracronPCGPathFindingMode::None;
        StartLocation = FVector::ZeroVector;
        EndLocation = FVector::ZeroVector;
        bAvoidObstacles = true;
        ObstacleAvoidanceRadius = 100.0f;
        bSmoothPath = true;
        SmoothingStrength = 0.5f;
        SmoothingIterations = 3;
        MaxSlope = 45.0f;
        MinWidth = 50.0f;
        bStayOnNavMesh = false;
        MaxSearchNodes = 10000;
        SearchTimeout = 5.0f;
    }
};

// =============================================================================
// ADVANCED SPLINE CREATOR
// =============================================================================

/**
 * Advanced Spline Creator
 * Creates splines from various sources with advanced features
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGAdvancedSplineCreatorSettings, FAuracronPCGAdvancedSplineCreatorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGAdvancedSplineCreatorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGAdvancedSplineCreatorSettings();

    // Creation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creation")
    EAuracronPCGSplineCreationMode CreationMode = EAuracronPCGSplineCreationMode::FromPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creation")
    bool bClosedSpline = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creation")
    bool bCreateComponent = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creation")
    bool bCreateActor = false;

    // Point processing
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Processing")
    bool bSortPointsByDistance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Processing")
    bool bRemoveDuplicatePoints = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Processing", meta = (EditCondition = "bRemoveDuplicatePoints"))
    float DuplicateThreshold = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Processing")
    bool bSimplifySpline = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Processing", meta = (EditCondition = "bSimplifySpline"))
    float SimplificationTolerance = 10.0f;

    // Tangent settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tangents")
    EAuracronPCGSplineTangentMode DefaultTangentMode = EAuracronPCGSplineTangentMode::Auto;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tangents")
    float TangentScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tangents")
    bool bUseCustomTangents = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tangents", meta = (EditCondition = "bUseCustomTangents"))
    FString ArriveTangentAttribute = TEXT("ArriveTangent");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tangents", meta = (EditCondition = "bUseCustomTangents"))
    FString LeaveTangentAttribute = TEXT("LeaveTangent");

    // Procedural generation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural", meta = (EditCondition = "CreationMode == EAuracronPCGSplineCreationMode::Procedural"))
    int32 ProceduralPointCount = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural", meta = (EditCondition = "CreationMode == EAuracronPCGSplineCreationMode::Procedural"))
    float ProceduralLength = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural", meta = (EditCondition = "CreationMode == EAuracronPCGSplineCreationMode::Procedural"))
    float ProceduralVariation = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural", meta = (EditCondition = "CreationMode == EAuracronPCGSplineCreationMode::Procedural"))
    int32 ProceduralSeed = 12345;

    // Path finding integration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Finding")
    FAuracronPCGPathFindingDescriptor PathFindingDescriptor;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputSplinePoints = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputDebugInfo = false;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedSplineCreatorElement, UAuracronPCGAdvancedSplineCreatorSettings)

// =============================================================================
// SPLINE POINT DISTRIBUTOR
// =============================================================================

/**
 * Spline Point Distributor
 * Distributes points along splines using various distribution algorithms
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGSplinePointDistributorSettings, FAuracronPCGSplinePointDistributorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGSplinePointDistributorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGSplinePointDistributorSettings();

    // Distribution settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distribution")
    EAuracronPCGSplineDistributionMode DistributionMode = EAuracronPCGSplineDistributionMode::Uniform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distribution")
    int32 PointCount = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distribution")
    float PointSpacing = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distribution")
    bool bUsePointCountOverSpacing = true;

    // Uniform distribution
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Uniform", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Uniform"))
    bool bIncludeSplineStart = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Uniform", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Uniform"))
    bool bIncludeSplineEnd = true;

    // Random distribution
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Random", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Random"))
    int32 RandomSeed = 12345;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Random", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Random"))
    float RandomVariation = 0.5f;

    // Noise distribution
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Noise"))
    float NoiseScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Noise"))
    float NoiseStrength = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Noise"))
    int32 NoiseOctaves = 3;

    // Curve distribution
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Curve", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Curve"))
    TSoftObjectPtr<UCurveFloat> DistributionCurve;

    // Adaptive distribution
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptive", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Adaptive"))
    float CurvatureThreshold = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptive", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Adaptive"))
    float MinPointSpacing = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptive", meta = (EditCondition = "DistributionMode == EAuracronPCGSplineDistributionMode::Adaptive"))
    float MaxPointSpacing = 200.0f;

    // Point properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Properties")
    bool bAlignToSpline = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Properties")
    bool bUseSplineWidth = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Properties", meta = (EditCondition = "bUseSplineWidth"))
    FString WidthAttribute = TEXT("Width");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Properties")
    bool bProjectToSurface = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Properties", meta = (EditCondition = "bProjectToSurface"))
    float ProjectionDistance = 1000.0f;

    // Offset settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Offset")
    FVector PointOffset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Offset")
    bool bRandomizeOffset = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Offset", meta = (EditCondition = "bRandomizeOffset"))
    FVector OffsetVariation = FVector(10.0f);

    // Filtering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterBySplineSegment = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterBySplineSegment"))
    TArray<int32> AllowedSegments;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bFilterByDistance = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering", meta = (EditCondition = "bFilterByDistance"))
    FVector2D DistanceRange = FVector2D(0.0f, 1000.0f);

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGSplinePointDistributorElement, UAuracronPCGSplinePointDistributorSettings)

// =============================================================================
// SPLINE MESH GENERATOR
// =============================================================================

/**
 * Spline Mesh Generator
 * Generates meshes along splines using various mesh generation modes
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGSplineMeshGeneratorSettings, FAuracronPCGSplineMeshGeneratorElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGSplineMeshGeneratorSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGSplineMeshGeneratorSettings();

    // Mesh generation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Generation")
    TArray<FAuracronPCGSplineMeshDescriptor> MeshDescriptors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Generation")
    bool bGenerateStartCap = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Generation", meta = (EditCondition = "bGenerateStartCap"))
    TSoftObjectPtr<UStaticMesh> StartCapMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Generation")
    bool bGenerateEndCap = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh Generation", meta = (EditCondition = "bGenerateEndCap"))
    TSoftObjectPtr<UStaticMesh> EndCapMesh;

    // Segmentation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Segmentation")
    bool bUseCustomSegmentation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Segmentation", meta = (EditCondition = "bUseCustomSegmentation"))
    float SegmentLength = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Segmentation", meta = (EditCondition = "bUseCustomSegmentation"))
    bool bAdaptiveSegmentation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Segmentation", meta = (EditCondition = "bAdaptiveSegmentation"))
    float MaxSegmentAngle = 30.0f;

    // Deformation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Deformation")
    bool bApplyDeformation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Deformation", meta = (EditCondition = "bApplyDeformation"))
    EAuracronPCGSplineDeformationMode DeformationMode = EAuracronPCGSplineDeformationMode::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Deformation", meta = (EditCondition = "bApplyDeformation"))
    float DeformationStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Deformation", meta = (EditCondition = "bApplyDeformation"))
    float DeformationFrequency = 1.0f;

    // UV settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Settings")
    bool bGenerateUVs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Settings", meta = (EditCondition = "bGenerateUVs"))
    float UVTiling = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Settings", meta = (EditCondition = "bGenerateUVs"))
    bool bUseWorldSpaceUVs = false;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseInstancedMeshes = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseLODs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (EditCondition = "bUseLODs"))
    TArray<float> LODDistances;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (EditCondition = "bUseCulling"))
    float CullingDistance = 5000.0f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGSplineMeshGeneratorElement, UAuracronPCGSplineMeshGeneratorSettings)

// =============================================================================
// SPLINE PATH FINDER
// =============================================================================

/**
 * Spline Path Finder
 * Creates splines using path finding algorithms
 */
AURACRON_PCG_NODE_SETTINGS(UAuracronPCGSplinePathFinderSettings, FAuracronPCGSplinePathFinderElement)

UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGSplinePathFinderSettings : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGSplinePathFinderSettings();

    // Path finding configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Finding")
    FAuracronPCGPathFindingDescriptor PathFindingDescriptor;

    // Multiple paths
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multiple Paths")
    bool bGenerateMultiplePaths = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multiple Paths", meta = (EditCondition = "bGenerateMultiplePaths"))
    int32 PathCount = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multiple Paths", meta = (EditCondition = "bGenerateMultiplePaths"))
    float PathVariation = 0.3f;

    // Cost functions
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cost Functions")
    bool bUseTerrainCost = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cost Functions", meta = (EditCondition = "bUseTerrainCost"))
    float SlopeCostMultiplier = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cost Functions")
    bool bUseDistanceCost = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cost Functions", meta = (EditCondition = "bUseDistanceCost"))
    float DistanceCostMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cost Functions")
    bool bUseCustomCost = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cost Functions", meta = (EditCondition = "bUseCustomCost"))
    FString CostAttribute = TEXT("Cost");

    // Optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bOptimizePath = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (EditCondition = "bOptimizePath"))
    int32 OptimizationIterations = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (EditCondition = "bOptimizePath"))
    float OptimizationStrength = 0.5f;

    // Debug settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowSearchNodes = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowPathCosts = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bOutputSearchStatistics = false;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGSplinePathFinderElement, UAuracronPCGSplinePathFinderSettings)

// =============================================================================
// SPLINE SYSTEM UTILITIES
// =============================================================================

/**
 * Spline System Utilities
 * Utility functions for advanced spline system operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGFRAMEWORK_API UAuracronPCGSplineSystemUtils : public UObject
{
    GENERATED_BODY()

public:
    // Spline creation utilities
    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static USplineComponent* CreateSplineFromPoints(const TArray<FVector>& Points, bool bClosedSpline = false, EAuracronPCGSplineTangentMode TangentMode = EAuracronPCGSplineTangentMode::Auto);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static USplineComponent* CreateSplineFromPointData(const UPCGPointData* PointData, bool bClosedSpline = false);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static bool ValidateSplinePoints(const TArray<FVector>& Points, float MinDistance = 1.0f);

    // Point distribution utilities
    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static TArray<FVector> DistributePointsAlongSpline(USplineComponent* SplineComponent, int32 PointCount, EAuracronPCGSplineDistributionMode DistributionMode);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static TArray<FTransform> DistributeTransformsAlongSpline(USplineComponent* SplineComponent, int32 PointCount, bool bAlignToSpline = true);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static float CalculateSplineLength(USplineComponent* SplineComponent);

    // Mesh generation utilities
    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static USplineMeshComponent* CreateSplineMeshComponent(USplineComponent* SplineComponent, UStaticMesh* StaticMesh);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static bool GenerateSplineMeshes(USplineComponent* SplineComponent, const TArray<FAuracronPCGSplineMeshDescriptor>& MeshDescriptors);

    // Path finding utilities
    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static TArray<FVector> FindPath(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static bool IsPathValid(const TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static TArray<FVector> SmoothPath(const TArray<FVector>& Path, float SmoothingStrength = 0.5f, int32 Iterations = 3);

    // Spline analysis utilities
    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static float CalculateSplineCurvature(USplineComponent* SplineComponent, float Distance);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static FVector GetSplineDirectionAtDistance(USplineComponent* SplineComponent, float Distance);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static float GetSplineWidthAtDistance(USplineComponent* SplineComponent, float Distance);

    // Optimization utilities
    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static bool OptimizeSpline(USplineComponent* SplineComponent, float Tolerance = 10.0f);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static bool SimplifySpline(USplineComponent* SplineComponent, float Tolerance = 10.0f);

    UFUNCTION(BlueprintCallable, Category = "Spline System Utils")
    static int32 GetOptimalPointCount(float SplineLength, float TargetSpacing = 100.0f);
};

// Namespace for spline system utility functions
namespace AuracronPCGSplineSystemUtils
{
    AURACRONPCGFRAMEWORK_API bool CreateSplineFromPCGData(const UPCGSplineData* SplineData, USplineComponent* OutSplineComponent);
    AURACRONPCGFRAMEWORK_API UPCGSplineData* CreatePCGDataFromSpline(USplineComponent* SplineComponent);
    AURACRONPCGFRAMEWORK_API bool InterpolateSplinePoints(const TArray<FVector>& ControlPoints, TArray<FVector>& OutInterpolatedPoints, int32 PointsPerSegment = 10);
    AURACRONPCGFRAMEWORK_API float CalculatePathCost(const TArray<FVector>& Path, const FAuracronPCGPathFindingDescriptor& PathFindingDescriptor);
    AURACRONPCGFRAMEWORK_API bool ValidateSplineForMeshGeneration(USplineComponent* SplineComponent, const FAuracronPCGSplineMeshDescriptor& MeshDescriptor);
    AURACRONPCGFRAMEWORK_API void ApplySplineDeformation(USplineComponent* SplineComponent, EAuracronPCGSplineDeformationMode DeformationMode, float Strength, float Frequency);
    AURACRONPCGFRAMEWORK_API TArray<FVector> GenerateAdaptivePoints(USplineComponent* SplineComponent, float CurvatureThreshold, float MinSpacing, float MaxSpacing);
    AURACRONPCGFRAMEWORK_API bool PerformAStarPathFinding(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& Descriptor, TArray<FVector>& OutPath);
    AURACRONPCGFRAMEWORK_API bool PerformDijkstraPathFinding(const FVector& Start, const FVector& End, const FAuracronPCGPathFindingDescriptor& Descriptor, TArray<FVector>& OutPath);
    AURACRONPCGFRAMEWORK_API void OptimizeSplineTangents(USplineComponent* SplineComponent, float SmoothingFactor = 0.5f);
    AURACRONPCGFRAMEWORK_API bool GenerateSplineCollision(USplineComponent* SplineComponent, float CollisionWidth = 100.0f, bool bComplexCollision = false);
    AURACRONPCGFRAMEWORK_API void UpdateSplineMeshLODs(USplineMeshComponent* SplineMeshComponent, const TArray<float>& LODDistances);
}
