// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Async Task Manager Implementation
// Bridge 2.16: PCG Framework - Async Processing

#include "AuracronPCGAsyncProcessing.h"
#include "AuracronPCGBridge.h"
#include "AuracronPCGLogger.h"

// Engine includes
#include "HAL/PlatformProcess.h"
#include "Misc/Guid.h"
#include "Async/Async.h"

// =============================================================================
// CANCELLATION TOKEN IMPLEMENTATION
// =============================================================================

UAuracronPCGCancellationToken* UAuracronPCGCancellationToken::CreateToken()
{
    UAuracronPCGCancellationToken* Token = NewObject<UAuracronPCGCancellationToken>();
    Token->CreationTime = FDateTime::Now();
    return Token;
}

void UAuracronPCGCancellationToken::Cancel()
{
    FScopeLock Lock(&TokenLock);
    
    if (!bIsCancelled.Load())
    {
        bIsCancelled = true;
        ExecuteCancellationCallbacks();
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cancellation token cancelled"));
    }
}

void UAuracronPCGCancellationToken::Reset()
{
    FScopeLock Lock(&TokenLock);
    
    bIsCancelled = false;
    CreationTime = FDateTime::Now();
    bHasTimeout = false;
    TimeoutSeconds = 0.0f;
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cancellation token reset"));
}

bool UAuracronPCGCancellationToken::IsCancelled() const
{
    CheckTimeout();
    return bIsCancelled.Load();
}

void UAuracronPCGCancellationToken::ThrowIfCancelled() const
{
    if (IsCancelled())
    {
        // In a real implementation, you might throw a custom exception
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Operation was cancelled"));
    }
}

void UAuracronPCGCancellationToken::RegisterCancellationCallback(const FString& CallbackId)
{
    FScopeLock Lock(&TokenLock);
    
    if (!CancellationCallbacks.Contains(CallbackId))
    {
        CancellationCallbacks.Add(CallbackId);
    }
}

void UAuracronPCGCancellationToken::UnregisterCancellationCallback(const FString& CallbackId)
{
    FScopeLock Lock(&TokenLock);
    
    CancellationCallbacks.Remove(CallbackId);
}

void UAuracronPCGCancellationToken::SetTimeout(float InTimeoutSeconds)
{
    FScopeLock Lock(&TokenLock);
    
    TimeoutSeconds = InTimeoutSeconds;
    bHasTimeout = true;
    CreationTime = FDateTime::Now();
}

void UAuracronPCGCancellationToken::ClearTimeout()
{
    FScopeLock Lock(&TokenLock);
    
    bHasTimeout = false;
    TimeoutSeconds = 0.0f;
}

bool UAuracronPCGCancellationToken::HasTimeout() const
{
    FScopeLock Lock(&TokenLock);
    return bHasTimeout;
}

float UAuracronPCGCancellationToken::GetRemainingTime() const
{
    FScopeLock Lock(&TokenLock);
    
    if (!bHasTimeout)
    {
        return -1.0f; // No timeout
    }
    
    float ElapsedTime = (FDateTime::Now() - CreationTime).GetTotalSeconds();
    return FMath::Max(0.0f, TimeoutSeconds - ElapsedTime);
}

void UAuracronPCGCancellationToken::CheckTimeout() const
{
    if (bHasTimeout && !bIsCancelled.Load())
    {
        float ElapsedTime = (FDateTime::Now() - CreationTime).GetTotalSeconds();
        if (ElapsedTime >= TimeoutSeconds)
        {
            const_cast<UAuracronPCGCancellationToken*>(this)->Cancel();
        }
    }
}

void UAuracronPCGCancellationToken::ExecuteCancellationCallbacks()
{
    for (const FString& CallbackId : CancellationCallbacks)
    {
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Executing cancellation callback: %s"), *CallbackId);
        // In a real implementation, you'd execute the actual callback functions
    }
}

// =============================================================================
// ASYNC TASK MANAGER IMPLEMENTATION
// =============================================================================

UAuracronPCGAsyncTaskManager* UAuracronPCGAsyncTaskManager::Instance = nullptr;

UAuracronPCGAsyncTaskManager* UAuracronPCGAsyncTaskManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronPCGAsyncTaskManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronPCGAsyncTaskManager::Initialize()
{
    if (bIsInitialized)
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Async Task Manager already initialized"));
        return;
    }

    // Initialize progress tracker
    ProgressTracker = NewObject<UAuracronPCGProgressTracker>();
    
    // Initialize memory pool manager
    MemoryPoolManager = UAuracronPCGMemoryPoolManager::GetInstance();
    MemoryPoolManager->InitializePool(DefaultTaskDescriptor.MemoryPoolSize);
    
    // Set default values
    MaxConcurrentTasks = FMath::Max(1, FPlatformMisc::NumberOfCores() - 1);
    TotalTasksExecuted = 0;
    TotalExecutionTime = 0.0f;
    
    bIsInitialized = true;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Async Task Manager initialized with %d max concurrent tasks"), MaxConcurrentTasks);
}

void UAuracronPCGAsyncTaskManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Cancel all active tasks
    CancelAllTasks();
    
    // Wait for tasks to complete (with timeout)
    float TimeoutTime = 10.0f; // 10 seconds timeout
    float StartTime = FPlatformTime::Seconds();
    
    while (GetActiveTaskCount() > 0 && (FPlatformTime::Seconds() - StartTime) < TimeoutTime)
    {
        FPlatformProcess::Sleep(0.1f);
    }
    
    // Force cleanup remaining tasks
    CleanupAllTasks();
    
    // Shutdown memory pool
    if (MemoryPoolManager)
    {
        MemoryPoolManager->ShutdownPool();
    }
    
    bIsInitialized = false;
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Async Task Manager shutdown"));
}

bool UAuracronPCGAsyncTaskManager::IsInitialized() const
{
    return bIsInitialized;
}

FString UAuracronPCGAsyncTaskManager::ExecutePointProcessingAsync(UPCGPointData* PointData, const FAuracronPCGAsyncTaskDescriptor& Descriptor)
{
    if (!bIsInitialized || !PointData)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot execute point processing: Manager not initialized or invalid point data"));
        return FString();
    }

    if (!CanExecuteNewTask())
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Cannot execute new task: Maximum concurrent tasks reached"));
        return FString();
    }

    FString TaskId = GenerateTaskId();
    
    // Create a simple point processing task
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, TaskId, PointData, Descriptor]()
    {
        // Start progress tracking
        int32 PointCount = PointData->GetPoints().Num();
        ProgressTracker->StartTracking(TaskId, Descriptor.TaskName, PointCount);
        
        FAuracronPCGAsyncTaskResult Result;
        Result.TaskId = TaskId;
        Result.bSuccess = true;
        
        FDateTime StartTime = FDateTime::Now();
        
        try
        {
            // Simulate point processing
            for (int32 i = 0; i < PointCount; i++)
            {
                // Check for cancellation
                UAuracronPCGCancellationToken* Token = CancellationTokens.FindRef(TaskId);
                if (Token && Token->IsCancelled())
                {
                    Result.bSuccess = false;
                    Result.ErrorMessage = TEXT("Task was cancelled");
                    break;
                }
                
                // Simulate processing work
                FPlatformProcess::Sleep(0.001f); // 1ms per point
                
                // Update progress
                if (i % 100 == 0) // Update every 100 points
                {
                    ProgressTracker->UpdateProgress(TaskId, i, FString::Printf(TEXT("Processing point %d/%d"), i, PointCount));
                }
            }
            
            if (Result.bSuccess)
            {
                Result.ItemsProcessed = PointCount;
                ProgressTracker->UpdateProgress(TaskId, PointCount, TEXT("Completed"));
            }
        }
        catch (...)
        {
            Result.bSuccess = false;
            Result.ErrorMessage = TEXT("Unexpected error during point processing");
        }
        
        FDateTime EndTime = FDateTime::Now();
        Result.TotalExecutionTime = (EndTime - StartTime).GetTotalSeconds();
        
        // Finish on game thread
        AsyncTask(ENamedThreads::GameThread, [this, TaskId, Result]()
        {
            OnTaskCompletedInternal(TaskId, Result);
        });
    });
    
    // Create cancellation token
    UAuracronPCGCancellationToken* CancellationToken = UAuracronPCGCancellationToken::CreateToken();
    if (Descriptor.TimeoutSeconds > 0.0f)
    {
        CancellationToken->SetTimeout(Descriptor.TimeoutSeconds);
    }
    
    {
        FScopeLock Lock(&TaskMapLock);
        CancellationTokens.Add(TaskId, CancellationToken);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Started point processing task: %s"), *TaskId);
    return TaskId;
}

FString UAuracronPCGAsyncTaskManager::ExecuteGraphAsync(UPCGGraph* Graph, const FAuracronPCGAsyncTaskDescriptor& Descriptor)
{
    if (!bIsInitialized || !Graph)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot execute graph: Manager not initialized or invalid graph"));
        return FString();
    }

    if (!CanExecuteNewTask())
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Cannot execute new task: Maximum concurrent tasks reached"));
        return FString();
    }

    FString TaskId = GenerateTaskId();
    
    // Create a simple graph execution task
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, TaskId, Graph, Descriptor]()
    {
        // Start progress tracking
        ProgressTracker->StartTracking(TaskId, Descriptor.TaskName, 100); // Assume 100 steps
        
        FAuracronPCGAsyncTaskResult Result;
        Result.TaskId = TaskId;
        Result.bSuccess = true;
        
        FDateTime StartTime = FDateTime::Now();
        
        try
        {
            // Simulate graph execution
            for (int32 i = 0; i < 100; i++)
            {
                // Check for cancellation
                UAuracronPCGCancellationToken* Token = CancellationTokens.FindRef(TaskId);
                if (Token && Token->IsCancelled())
                {
                    Result.bSuccess = false;
                    Result.ErrorMessage = TEXT("Task was cancelled");
                    break;
                }
                
                // Simulate processing work
                FPlatformProcess::Sleep(0.01f); // 10ms per step
                
                // Update progress
                ProgressTracker->UpdateProgress(TaskId, i, FString::Printf(TEXT("Executing step %d/100"), i));
            }
            
            if (Result.bSuccess)
            {
                Result.ItemsProcessed = 100;
                ProgressTracker->UpdateProgress(TaskId, 100, TEXT("Completed"));
            }
        }
        catch (...)
        {
            Result.bSuccess = false;
            Result.ErrorMessage = TEXT("Unexpected error during graph execution");
        }
        
        FDateTime EndTime = FDateTime::Now();
        Result.TotalExecutionTime = (EndTime - StartTime).GetTotalSeconds();
        
        // Finish on game thread
        AsyncTask(ENamedThreads::GameThread, [this, TaskId, Result]()
        {
            OnTaskCompletedInternal(TaskId, Result);
        });
    });
    
    // Create cancellation token
    UAuracronPCGCancellationToken* CancellationToken = UAuracronPCGCancellationToken::CreateToken();
    if (Descriptor.TimeoutSeconds > 0.0f)
    {
        CancellationToken->SetTimeout(Descriptor.TimeoutSeconds);
    }
    
    {
        FScopeLock Lock(&TaskMapLock);
        CancellationTokens.Add(TaskId, CancellationToken);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Started graph execution task: %s"), *TaskId);
    return TaskId;
}

FString UAuracronPCGAsyncTaskManager::ExecuteCustomTaskAsync(const FAuracronPCGAsyncTaskDescriptor& Descriptor, const FString& TaskType)
{
    if (!bIsInitialized)
    {
        AURACRON_PCG_LOG_ELEMENTS(Error, TEXT("Cannot execute custom task: Manager not initialized"));
        return FString();
    }

    if (!CanExecuteNewTask())
    {
        AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Cannot execute new task: Maximum concurrent tasks reached"));
        return FString();
    }

    FString TaskId = GenerateTaskId();
    
    // Create a custom task
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, TaskId, Descriptor, TaskType]()
    {
        // Start progress tracking
        ProgressTracker->StartTracking(TaskId, Descriptor.TaskName, 50); // Assume 50 steps
        
        FAuracronPCGAsyncTaskResult Result;
        Result.TaskId = TaskId;
        Result.bSuccess = true;
        
        FDateTime StartTime = FDateTime::Now();
        
        try
        {
            // Simulate custom task execution
            for (int32 i = 0; i < 50; i++)
            {
                // Check for cancellation
                UAuracronPCGCancellationToken* Token = CancellationTokens.FindRef(TaskId);
                if (Token && Token->IsCancelled())
                {
                    Result.bSuccess = false;
                    Result.ErrorMessage = TEXT("Task was cancelled");
                    break;
                }
                
                // Simulate processing work
                FPlatformProcess::Sleep(0.02f); // 20ms per step
                
                // Update progress
                ProgressTracker->UpdateProgress(TaskId, i, FString::Printf(TEXT("Executing %s step %d/50"), *TaskType, i));
            }
            
            if (Result.bSuccess)
            {
                Result.ItemsProcessed = 50;
                ProgressTracker->UpdateProgress(TaskId, 50, TEXT("Completed"));
            }
        }
        catch (...)
        {
            Result.bSuccess = false;
            Result.ErrorMessage = TEXT("Unexpected error during custom task execution");
        }
        
        FDateTime EndTime = FDateTime::Now();
        Result.TotalExecutionTime = (EndTime - StartTime).GetTotalSeconds();
        
        // Finish on game thread
        AsyncTask(ENamedThreads::GameThread, [this, TaskId, Result]()
        {
            OnTaskCompletedInternal(TaskId, Result);
        });
    });
    
    // Create cancellation token
    UAuracronPCGCancellationToken* CancellationToken = UAuracronPCGCancellationToken::CreateToken();
    if (Descriptor.TimeoutSeconds > 0.0f)
    {
        CancellationToken->SetTimeout(Descriptor.TimeoutSeconds);
    }
    
    {
        FScopeLock Lock(&TaskMapLock);
        CancellationTokens.Add(TaskId, CancellationToken);
    }
    
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Started custom task: %s (%s)"), *TaskId, *TaskType);
    return TaskId;
}

void UAuracronPCGAsyncTaskManager::CancelTask(const FString& TaskId)
{
    FScopeLock Lock(&TaskMapLock);

    UAuracronPCGCancellationToken* Token = CancellationTokens.FindRef(TaskId);
    if (Token)
    {
        Token->Cancel();
        AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cancelled task: %s"), *TaskId);
    }
}

void UAuracronPCGAsyncTaskManager::CancelAllTasks()
{
    FScopeLock Lock(&TaskMapLock);

    int32 CancelledCount = 0;
    for (auto& TokenPair : CancellationTokens)
    {
        if (TokenPair.Value)
        {
            TokenPair.Value->Cancel();
            CancelledCount++;
        }
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cancelled %d tasks"), CancelledCount);
}

void UAuracronPCGAsyncTaskManager::PauseTask(const FString& TaskId)
{
    // Note: Pausing is handled by individual tasks, this is a placeholder
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Pause requested for task: %s"), *TaskId);
}

void UAuracronPCGAsyncTaskManager::ResumeTask(const FString& TaskId)
{
    // Note: Resuming is handled by individual tasks, this is a placeholder
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Resume requested for task: %s"), *TaskId);
}

void UAuracronPCGAsyncTaskManager::PauseAllTasks()
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Pause requested for all tasks"));
}

void UAuracronPCGAsyncTaskManager::ResumeAllTasks()
{
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Resume requested for all tasks"));
}

bool UAuracronPCGAsyncTaskManager::IsTaskActive(const FString& TaskId) const
{
    if (ProgressTracker)
    {
        return ProgressTracker->IsTaskActive(TaskId);
    }
    return false;
}

EAuracronPCGTaskState UAuracronPCGAsyncTaskManager::GetTaskState(const FString& TaskId) const
{
    if (ProgressTracker)
    {
        FAuracronPCGProgressInfo ProgressInfo = ProgressTracker->GetProgressInfo(TaskId);
        return ProgressInfo.TaskState;
    }
    return EAuracronPCGTaskState::Pending;
}

FAuracronPCGProgressInfo UAuracronPCGAsyncTaskManager::GetTaskProgress(const FString& TaskId) const
{
    if (ProgressTracker)
    {
        return ProgressTracker->GetProgressInfo(TaskId);
    }
    return FAuracronPCGProgressInfo();
}

FAuracronPCGAsyncTaskResult UAuracronPCGAsyncTaskManager::GetTaskResult(const FString& TaskId) const
{
    FScopeLock Lock(&CompletedTasksLock);

    const FAuracronPCGAsyncTaskResult* Result = CompletedTasks.Find(TaskId);
    if (Result)
    {
        return *Result;
    }

    return FAuracronPCGAsyncTaskResult();
}

TArray<FString> UAuracronPCGAsyncTaskManager::GetActiveTasks() const
{
    TArray<FString> ActiveTasks;

    if (ProgressTracker)
    {
        TArray<FAuracronPCGProgressInfo> AllProgress = ProgressTracker->GetAllProgressInfo();
        for (const FAuracronPCGProgressInfo& ProgressInfo : AllProgress)
        {
            if (ProgressInfo.TaskState == EAuracronPCGTaskState::Running ||
                ProgressInfo.TaskState == EAuracronPCGTaskState::Paused)
            {
                ActiveTasks.Add(ProgressInfo.TaskId);
            }
        }
    }

    return ActiveTasks;
}

TArray<FString> UAuracronPCGAsyncTaskManager::GetCompletedTasks() const
{
    FScopeLock Lock(&CompletedTasksLock);

    TArray<FString> CompletedTaskIds;
    CompletedTasks.GetKeys(CompletedTaskIds);
    return CompletedTaskIds;
}

int32 UAuracronPCGAsyncTaskManager::GetActiveTaskCount() const
{
    if (ProgressTracker)
    {
        return ProgressTracker->GetActiveTaskCount();
    }
    return 0;
}

TArray<FString> UAuracronPCGAsyncTaskManager::ExecuteBatchPointProcessing(const TArray<UPCGPointData*>& PointDataArray, const FAuracronPCGAsyncTaskDescriptor& Descriptor)
{
    TArray<FString> TaskIds;

    for (int32 i = 0; i < PointDataArray.Num(); i++)
    {
        if (PointDataArray[i])
        {
            FAuracronPCGAsyncTaskDescriptor BatchDescriptor = Descriptor;
            BatchDescriptor.TaskName = FString::Printf(TEXT("%s_Batch_%d"), *Descriptor.TaskName, i);

            FString TaskId = ExecutePointProcessingAsync(PointDataArray[i], BatchDescriptor);
            if (!TaskId.IsEmpty())
            {
                TaskIds.Add(TaskId);
            }
        }
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Started batch processing with %d tasks"), TaskIds.Num());
    return TaskIds;
}

void UAuracronPCGAsyncTaskManager::WaitForBatchCompletion(const TArray<FString>& TaskIds, float TimeoutSeconds)
{
    float StartTime = FPlatformTime::Seconds();
    bool bHasTimeout = TimeoutSeconds > 0.0f;

    while (!AreBatchTasksCompleted(TaskIds))
    {
        if (bHasTimeout && (FPlatformTime::Seconds() - StartTime) >= TimeoutSeconds)
        {
            AURACRON_PCG_LOG_ELEMENTS(Warning, TEXT("Batch completion wait timed out after %.2f seconds"), TimeoutSeconds);
            break;
        }

        FPlatformProcess::Sleep(0.1f); // 100ms sleep
    }
}

bool UAuracronPCGAsyncTaskManager::AreBatchTasksCompleted(const TArray<FString>& TaskIds) const
{
    for (const FString& TaskId : TaskIds)
    {
        if (IsTaskActive(TaskId))
        {
            return false;
        }
    }
    return true;
}

TMap<FString, float> UAuracronPCGAsyncTaskManager::GetPerformanceMetrics() const
{
    TMap<FString, float> Metrics;

    Metrics.Add(TEXT("TotalTasksExecuted"), static_cast<float>(TotalTasksExecuted));
    Metrics.Add(TEXT("AverageExecutionTime"), GetAverageTaskExecutionTime());
    Metrics.Add(TEXT("ActiveTaskCount"), static_cast<float>(GetActiveTaskCount()));
    Metrics.Add(TEXT("MaxConcurrentTasks"), static_cast<float>(MaxConcurrentTasks));
    Metrics.Add(TEXT("SystemLoadPercentage"), GetSystemLoadPercentage());

    if (MemoryPoolManager)
    {
        Metrics.Add(TEXT("MemoryUsagePercentage"), MemoryPoolManager->GetMemoryUsagePercentage());
        Metrics.Add(TEXT("MemoryUsedMB"), static_cast<float>(MemoryPoolManager->GetUsedMemory()));
    }

    return Metrics;
}

float UAuracronPCGAsyncTaskManager::GetAverageTaskExecutionTime() const
{
    if (TotalTasksExecuted == 0)
    {
        return 0.0f;
    }

    return TotalExecutionTime / static_cast<float>(TotalTasksExecuted);
}

int32 UAuracronPCGAsyncTaskManager::GetTotalTasksExecuted() const
{
    return TotalTasksExecuted;
}

float UAuracronPCGAsyncTaskManager::GetSystemLoadPercentage() const
{
    float LoadPercentage = (static_cast<float>(GetActiveTaskCount()) / static_cast<float>(MaxConcurrentTasks)) * 100.0f;
    return FMath::Clamp(LoadPercentage, 0.0f, 100.0f);
}

void UAuracronPCGAsyncTaskManager::SetMaxConcurrentTasks(int32 MaxTasks)
{
    MaxConcurrentTasks = FMath::Max(1, MaxTasks);
    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Max concurrent tasks set to: %d"), MaxConcurrentTasks);
}

int32 UAuracronPCGAsyncTaskManager::GetMaxConcurrentTasks() const
{
    return MaxConcurrentTasks;
}

void UAuracronPCGAsyncTaskManager::SetDefaultTaskDescriptor(const FAuracronPCGAsyncTaskDescriptor& Descriptor)
{
    DefaultTaskDescriptor = Descriptor;
}

FAuracronPCGAsyncTaskDescriptor UAuracronPCGAsyncTaskManager::GetDefaultTaskDescriptor() const
{
    return DefaultTaskDescriptor;
}

void UAuracronPCGAsyncTaskManager::CleanupCompletedTasks()
{
    FScopeLock Lock(&CompletedTasksLock);

    TArray<FString> TasksToRemove;
    for (const auto& TaskPair : CompletedTasks)
    {
        if (TaskPair.Value.FinalState == EAuracronPCGTaskState::Completed)
        {
            TasksToRemove.Add(TaskPair.Key);
        }
    }

    for (const FString& TaskId : TasksToRemove)
    {
        CompletedTasks.Remove(TaskId);
        CleanupTaskInternal(TaskId);
    }

    if (ProgressTracker)
    {
        ProgressTracker->ClearCompletedTasks();
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleaned up %d completed tasks"), TasksToRemove.Num());
}

void UAuracronPCGAsyncTaskManager::CleanupFailedTasks()
{
    FScopeLock Lock(&CompletedTasksLock);

    TArray<FString> TasksToRemove;
    for (const auto& TaskPair : CompletedTasks)
    {
        if (TaskPair.Value.FinalState == EAuracronPCGTaskState::Failed)
        {
            TasksToRemove.Add(TaskPair.Key);
        }
    }

    for (const FString& TaskId : TasksToRemove)
    {
        CompletedTasks.Remove(TaskId);
        CleanupTaskInternal(TaskId);
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleaned up %d failed tasks"), TasksToRemove.Num());
}

void UAuracronPCGAsyncTaskManager::CleanupAllTasks()
{
    {
        FScopeLock Lock(&TaskMapLock);
        CancellationTokens.Empty();
    }

    {
        FScopeLock Lock(&CompletedTasksLock);
        CompletedTasks.Empty();
    }

    if (ProgressTracker)
    {
        ProgressTracker->ClearAllTasks();
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Cleaned up all tasks"));
}

FString UAuracronPCGAsyncTaskManager::GenerateTaskId() const
{
    return FGuid::NewGuid().ToString();
}

void UAuracronPCGAsyncTaskManager::RegisterTask(const FString& TaskId, TSharedPtr<FAuracronPCGAsyncTaskBase> Task)
{
    FScopeLock Lock(&TaskMapLock);
    ActiveTasks.Add(TaskId, Task);
}

void UAuracronPCGAsyncTaskManager::UnregisterTask(const FString& TaskId)
{
    FScopeLock Lock(&TaskMapLock);
    ActiveTasks.Remove(TaskId);
}

void UAuracronPCGAsyncTaskManager::OnTaskCompletedInternal(const FString& TaskId, const FAuracronPCGAsyncTaskResult& Result)
{
    // Store completed task result
    {
        FScopeLock Lock(&CompletedTasksLock);
        CompletedTasks.Add(TaskId, Result);
    }

    // Update performance metrics
    UpdatePerformanceMetrics(Result);

    // Clean up cancellation token
    {
        FScopeLock Lock(&TaskMapLock);
        CancellationTokens.Remove(TaskId);
    }

    // Broadcast events
    if (Result.bSuccess)
    {
        OnTaskCompleted.Broadcast(TaskId, Result);
    }
    else
    {
        OnTaskFailed.Broadcast(TaskId, Result.ErrorMessage);
    }

    AURACRON_PCG_LOG_ELEMENTS(Log, TEXT("Task completed: %s (Success: %s, Time: %.3fs)"),
                              *TaskId, Result.bSuccess ? TEXT("Yes") : TEXT("No"), Result.TotalExecutionTime);
}

void UAuracronPCGAsyncTaskManager::UpdatePerformanceMetrics(const FAuracronPCGAsyncTaskResult& Result)
{
    TotalTasksExecuted++;
    TotalExecutionTime += Result.TotalExecutionTime;

    // Keep execution time history (last 100 tasks)
    ExecutionTimeHistory.Add(Result.TotalExecutionTime);
    if (ExecutionTimeHistory.Num() > 100)
    {
        ExecutionTimeHistory.RemoveAt(0);
    }
}

bool UAuracronPCGAsyncTaskManager::CanExecuteNewTask() const
{
    return GetActiveTaskCount() < MaxConcurrentTasks;
}

void UAuracronPCGAsyncTaskManager::CleanupTaskInternal(const FString& TaskId)
{
    // Remove from active tasks
    UnregisterTask(TaskId);

    // Remove cancellation token
    {
        FScopeLock Lock(&TaskMapLock);
        CancellationTokens.Remove(TaskId);
    }
}
