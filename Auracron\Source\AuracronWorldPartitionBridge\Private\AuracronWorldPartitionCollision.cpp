// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Collision Streaming Implementation
// Bridge 3.8: World Partition - Collision Streaming

#include "AuracronWorldPartitionCollision.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionBridge.h"

// Collision includes
#include "Components/StaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/StaticMeshActor.h"
#include "PhysicsEngine/BodySetup.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "HAL/PlatformMemory.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"

// =============================================================================
// COLLISION STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronCollisionStatistics::UpdateCalculatedFields()
{
    if (TotalCollisionObjects > 0)
    {
        CollisionEfficiency = static_cast<float>(LoadedCollisionObjects) / static_cast<float>(TotalCollisionObjects);
    }
    else
    {
        CollisionEfficiency = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// WORLD PARTITION COLLISION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionCollisionManager* UAuracronWorldPartitionCollisionManager::Instance = nullptr;

UAuracronWorldPartitionCollisionManager* UAuracronWorldPartitionCollisionManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionCollisionManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionCollisionManager::Initialize(const FAuracronCollisionConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Collision Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize statistics
    Statistics = FAuracronCollisionStatistics();
    
    // Clear collections
    CollisionDescriptors.Empty();
    CollisionComponents.Empty();
    CollisionToCellMap.Empty();
    CellToCollisionMap.Empty();
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Collision Manager initialized with streaming distance: %.1fm"), Configuration.CollisionStreamingDistance);
}

void UAuracronWorldPartitionCollisionManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Unload all collision objects
    TArray<FString> CollisionsToUnload;
    CollisionDescriptors.GenerateKeyArray(CollisionsToUnload);
    
    for (const FString& CollisionId : CollisionsToUnload)
    {
        UnloadCollisionObject(CollisionId);
    }
    
    // Clear all data
    CollisionDescriptors.Empty();
    CollisionComponents.Empty();
    CollisionToCellMap.Empty();
    CellToCollisionMap.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Collision Manager shutdown completed"));
}

bool UAuracronWorldPartitionCollisionManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionCollisionManager::Tick(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update statistics
    UpdateStatistics();
}

FString UAuracronWorldPartitionCollisionManager::CreateCollisionObject(const FString& SourceActorId, EAuracronCollisionType CollisionType)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot create collision object: Manager not initialized"));
        return FString();
    }

    if (SourceActorId.IsEmpty())
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot create collision object: Source actor ID is empty"));
        return FString();
    }

    FScopeLock Lock(&CollisionLock);
    
    FString CollisionId = GenerateCollisionId(SourceActorId);
    
    // Check if collision object already exists
    if (CollisionDescriptors.Contains(CollisionId))
    {
        AURACRON_WP_LOG_WARNING(TEXT("Collision object already exists: %s"), *CollisionId);
        return CollisionId;
    }
    
    // Create collision descriptor
    FAuracronCollisionDescriptor CollisionDesc;
    CollisionDesc.CollisionId = CollisionId;
    CollisionDesc.CollisionName = FString::Printf(TEXT("Collision_%s"), *SourceActorId);
    CollisionDesc.SourceActorId = SourceActorId;
    CollisionDesc.CollisionType = CollisionType;
    CollisionDesc.CollisionComplexity = Configuration.DefaultCollisionComplexity;
    CollisionDesc.StreamingState = EAuracronCollisionStreamingState::Unloaded;
    CollisionDesc.CurrentLODLevel = EAuracronCollisionLODLevel::LOD0;
    CollisionDesc.CreationTime = FDateTime::Now();
    CollisionDesc.LastAccessTime = CollisionDesc.CreationTime;
    
    // Add to collections
    CollisionDescriptors.Add(CollisionId, CollisionDesc);
    
    // Update cell mapping
    UpdateCollisionCellMapping(CollisionId);
    
    // Create collision mesh if needed
    if (Configuration.bEnableCollisionStreaming)
    {
        CreateCollisionMesh(CollisionId, SourceActorId);
    }
    
    AURACRON_WP_LOG_INFO(TEXT("Collision object created: %s (Type: %s)"), *CollisionId, *UEnum::GetValueAsString(CollisionType));
    
    return CollisionId;
}

bool UAuracronWorldPartitionCollisionManager::RemoveCollisionObject(const FString& CollisionId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&CollisionLock);
    
    if (!CollisionDescriptors.Contains(CollisionId))
    {
        return false;
    }
    
    // Unload collision object if loaded
    UnloadCollisionObject(CollisionId);
    
    // Remove from cell mapping
    if (FString* CellId = CollisionToCellMap.Find(CollisionId))
    {
        if (TSet<FString>* CellCollisions = CellToCollisionMap.Find(*CellId))
        {
            CellCollisions->Remove(CollisionId);
        }
        CollisionToCellMap.Remove(CollisionId);
    }
    
    // Remove from collections
    CollisionDescriptors.Remove(CollisionId);
    CollisionComponents.Remove(CollisionId);
    
    AURACRON_WP_LOG_INFO(TEXT("Collision object removed: %s"), *CollisionId);
    
    return true;
}

FAuracronCollisionDescriptor UAuracronWorldPartitionCollisionManager::GetCollisionDescriptor(const FString& CollisionId) const
{
    FScopeLock Lock(&CollisionLock);
    
    const FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (CollisionDesc)
    {
        return *CollisionDesc;
    }
    
    return FAuracronCollisionDescriptor();
}

TArray<FAuracronCollisionDescriptor> UAuracronWorldPartitionCollisionManager::GetAllCollisionObjects() const
{
    FScopeLock Lock(&CollisionLock);
    
    TArray<FAuracronCollisionDescriptor> AllCollisions;
    CollisionDescriptors.GenerateValueArray(AllCollisions);
    
    return AllCollisions;
}

TArray<FString> UAuracronWorldPartitionCollisionManager::GetCollisionIds() const
{
    FScopeLock Lock(&CollisionLock);
    
    TArray<FString> CollisionIds;
    CollisionDescriptors.GenerateKeyArray(CollisionIds);
    
    return CollisionIds;
}

bool UAuracronWorldPartitionCollisionManager::DoesCollisionObjectExist(const FString& CollisionId) const
{
    FScopeLock Lock(&CollisionLock);
    return CollisionDescriptors.Contains(CollisionId);
}

bool UAuracronWorldPartitionCollisionManager::LoadCollisionObject(const FString& CollisionId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&CollisionLock);
    
    FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (!CollisionDesc)
    {
        return false;
    }
    
    if (CollisionDesc->StreamingState == EAuracronCollisionStreamingState::Loaded)
    {
        return true; // Already loaded
    }
    
    // Set loading state
    CollisionDesc->StreamingState = EAuracronCollisionStreamingState::Loading;
    CollisionDesc->LastAccessTime = FDateTime::Now();
    
    // Simulate collision loading
    // In a real implementation, this would interface with UE5.6's collision system
    bool bLoadSuccess = (FMath::RandRange(0, 100) > 3); // 97% success rate
    
    if (bLoadSuccess)
    {
        CollisionDesc->StreamingState = EAuracronCollisionStreamingState::Loaded;
        
        // Calculate memory usage based on collision complexity
        float BaseMemory = 1.0f; // 1MB base
        switch (CollisionDesc->CollisionType)
        {
            case EAuracronCollisionType::Static:
                BaseMemory *= 1.0f;
                break;
            case EAuracronCollisionType::Dynamic:
                BaseMemory *= 1.5f;
                break;
            case EAuracronCollisionType::Kinematic:
                BaseMemory *= 1.2f;
                break;
            case EAuracronCollisionType::Trigger:
                BaseMemory *= 0.5f;
                break;
            case EAuracronCollisionType::Query:
                BaseMemory *= 0.3f;
                break;
        }
        
        CollisionDesc->MemoryUsageMB = BaseMemory;
        
        OnCollisionLoadedInternal(CollisionId, true);
        
        AURACRON_WP_LOG_INFO(TEXT("Collision object loaded: %s (%.1fMB)"), *CollisionId, CollisionDesc->MemoryUsageMB);
        return true;
    }
    else
    {
        CollisionDesc->StreamingState = EAuracronCollisionStreamingState::Failed;
        
        // Update statistics
        {
            FScopeLock StatsLock(&StatisticsLock);
            Statistics.FailedOperations++;
        }
        
        OnCollisionLoadedInternal(CollisionId, false);
        
        AURACRON_WP_LOG_ERROR(TEXT("Failed to load collision object: %s"), *CollisionId);
        return false;
    }
}

bool UAuracronWorldPartitionCollisionManager::UnloadCollisionObject(const FString& CollisionId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (!CollisionDesc)
    {
        return false;
    }

    if (CollisionDesc->StreamingState == EAuracronCollisionStreamingState::Unloaded)
    {
        return true; // Already unloaded
    }

    // Set unloading state
    CollisionDesc->StreamingState = EAuracronCollisionStreamingState::Unloading;

    // Remove collision component reference
    if (TWeakObjectPtr<UPrimitiveComponent>* ComponentRef = CollisionComponents.Find(CollisionId))
    {
        if (UPrimitiveComponent* Component = ComponentRef->Get())
        {
            // In a real implementation, properly disable collision
            Component->SetCollisionEnabled(ECollisionEnabled::NoCollision);
        }
        CollisionComponents.Remove(CollisionId);
    }

    CollisionDesc->StreamingState = EAuracronCollisionStreamingState::Unloaded;
    CollisionDesc->MemoryUsageMB = 0.0f;

    OnCollisionUnloadedInternal(CollisionId);

    AURACRON_WP_LOG_INFO(TEXT("Collision object unloaded: %s"), *CollisionId);

    return true;
}

EAuracronCollisionStreamingState UAuracronWorldPartitionCollisionManager::GetCollisionStreamingState(const FString& CollisionId) const
{
    FScopeLock Lock(&CollisionLock);

    const FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (CollisionDesc)
    {
        return CollisionDesc->StreamingState;
    }

    return EAuracronCollisionStreamingState::Unloaded;
}

TArray<FString> UAuracronWorldPartitionCollisionManager::GetLoadedCollisionObjects() const
{
    FScopeLock Lock(&CollisionLock);

    TArray<FString> LoadedCollisions;

    for (const auto& CollisionPair : CollisionDescriptors)
    {
        if (CollisionPair.Value.StreamingState == EAuracronCollisionStreamingState::Loaded)
        {
            LoadedCollisions.Add(CollisionPair.Key);
        }
    }

    return LoadedCollisions;
}

TArray<FString> UAuracronWorldPartitionCollisionManager::GetStreamingCollisionObjects() const
{
    FScopeLock Lock(&CollisionLock);

    TArray<FString> StreamingCollisions;

    for (const auto& CollisionPair : CollisionDescriptors)
    {
        if (CollisionPair.Value.StreamingState == EAuracronCollisionStreamingState::Loading ||
            CollisionPair.Value.StreamingState == EAuracronCollisionStreamingState::Unloading)
        {
            StreamingCollisions.Add(CollisionPair.Key);
        }
    }

    return StreamingCollisions;
}

bool UAuracronWorldPartitionCollisionManager::SetCollisionLOD(const FString& CollisionId, EAuracronCollisionLODLevel LODLevel)
{
    if (!bIsInitialized || !Configuration.bEnableCollisionLOD)
    {
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (!CollisionDesc)
    {
        return false;
    }

    EAuracronCollisionLODLevel OldLOD = CollisionDesc->CurrentLODLevel;
    CollisionDesc->CurrentLODLevel = LODLevel;
    CollisionDesc->LastAccessTime = FDateTime::Now();

    // Update statistics
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.LODTransitions++;
    }

    OnCollisionLODChanged.Broadcast(CollisionId, LODLevel);

    AURACRON_WP_LOG_VERBOSE(TEXT("Collision LOD changed: %s (%s -> %s)"),
                           *CollisionId,
                           *UEnum::GetValueAsString(OldLOD),
                           *UEnum::GetValueAsString(LODLevel));

    return true;
}

EAuracronCollisionLODLevel UAuracronWorldPartitionCollisionManager::GetCollisionLOD(const FString& CollisionId) const
{
    FScopeLock Lock(&CollisionLock);

    const FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (CollisionDesc)
    {
        return CollisionDesc->CurrentLODLevel;
    }

    return EAuracronCollisionLODLevel::LOD0;
}

void UAuracronWorldPartitionCollisionManager::UpdateDistanceBasedLODs(const FVector& ViewerLocation)
{
    if (!bIsInitialized || !Configuration.bEnableCollisionLOD)
    {
        return;
    }

    FScopeLock Lock(&CollisionLock);

    for (auto& CollisionPair : CollisionDescriptors)
    {
        FAuracronCollisionDescriptor& CollisionDesc = CollisionPair.Value;

        if (CollisionDesc.StreamingState == EAuracronCollisionStreamingState::Loaded)
        {
            float Distance = FVector::Dist(ViewerLocation, CollisionDesc.Location);
            EAuracronCollisionLODLevel TargetLOD = CalculateLODForDistance(Distance);

            if (TargetLOD != CollisionDesc.CurrentLODLevel)
            {
                SetCollisionLOD(CollisionPair.Key, TargetLOD);
            }
        }
    }
}

EAuracronCollisionLODLevel UAuracronWorldPartitionCollisionManager::CalculateLODForDistance(float Distance) const
{
    if (Distance <= Configuration.BaseLODDistance)
    {
        return EAuracronCollisionLODLevel::LOD0; // Highest quality
    }

    float CurrentDistance = Configuration.BaseLODDistance;
    for (int32 LODLevel = 1; LODLevel <= Configuration.MaxLODLevel; LODLevel++)
    {
        CurrentDistance *= Configuration.LODDistanceMultiplier;
        if (Distance <= CurrentDistance)
        {
            return static_cast<EAuracronCollisionLODLevel>(LODLevel);
        }
    }

    return static_cast<EAuracronCollisionLODLevel>(Configuration.MaxLODLevel); // Lowest quality
}

TArray<FAuracronCollisionDescriptor> UAuracronWorldPartitionCollisionManager::ExecuteCollisionQuery(const FAuracronCollisionQueryParameters& QueryParams) const
{
    TArray<FAuracronCollisionDescriptor> QueryResults;

    FScopeLock Lock(&CollisionLock);

    for (const auto& CollisionPair : CollisionDescriptors)
    {
        const FAuracronCollisionDescriptor& CollisionDesc = CollisionPair.Value;

        // Apply filters
        if (!QueryParams.bIncludeDisabled && !CollisionDesc.bIsEnabled)
        {
            continue;
        }

        if (!QueryParams.bIncludeInvisible && !CollisionDesc.bIsVisible)
        {
            continue;
        }

        if (QueryParams.FilterTypes.Num() > 0 && !QueryParams.FilterTypes.Contains(CollisionDesc.CollisionType))
        {
            continue;
        }

        // Check spatial criteria
        if (IsCollisionInQueryRange(CollisionDesc, QueryParams))
        {
            QueryResults.Add(CollisionDesc);

            // Check result limit
            if (QueryResults.Num() >= QueryParams.MaxResults)
            {
                break;
            }
        }
    }

    // Update statistics
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.CollisionQueries++;
    }

    return QueryResults;
}

TArray<FAuracronCollisionDescriptor> UAuracronWorldPartitionCollisionManager::GetCollisionObjectsInRadius(const FVector& Location, float Radius) const
{
    FAuracronCollisionQueryParameters QueryParams;
    QueryParams.QueryLocation = Location;
    QueryParams.QueryExtent = FVector(Radius);

    return ExecuteCollisionQuery(QueryParams);
}

TArray<FAuracronCollisionDescriptor> UAuracronWorldPartitionCollisionManager::GetCollisionObjectsInBox(const FBox& Box) const
{
    FAuracronCollisionQueryParameters QueryParams;
    QueryParams.QueryLocation = Box.GetCenter();
    QueryParams.QueryExtent = Box.GetExtent();

    return ExecuteCollisionQuery(QueryParams);
}

TArray<FAuracronCollisionDescriptor> UAuracronWorldPartitionCollisionManager::GetCollisionObjectsByType(EAuracronCollisionType CollisionType) const
{
    FScopeLock Lock(&CollisionLock);

    TArray<FAuracronCollisionDescriptor> FilteredCollisions;

    for (const auto& CollisionPair : CollisionDescriptors)
    {
        if (CollisionPair.Value.CollisionType == CollisionType)
        {
            FilteredCollisions.Add(CollisionPair.Value);
        }
    }

    return FilteredCollisions;
}

bool UAuracronWorldPartitionCollisionManager::EnableCollisionObject(const FString& CollisionId, bool bEnabled)
{
    FScopeLock Lock(&CollisionLock);

    FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (!CollisionDesc)
    {
        return false;
    }

    bool bOldEnabled = CollisionDesc->bIsEnabled;
    CollisionDesc->bIsEnabled = bEnabled;
    CollisionDesc->LastAccessTime = FDateTime::Now();

    // Update collision component if loaded
    if (TWeakObjectPtr<UPrimitiveComponent>* ComponentRef = CollisionComponents.Find(CollisionId))
    {
        if (UPrimitiveComponent* Component = ComponentRef->Get())
        {
            Component->SetCollisionEnabled(bEnabled ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);
        }
    }

    OnCollisionStateChanged.Broadcast(CollisionId, bEnabled);

    AURACRON_WP_LOG_VERBOSE(TEXT("Collision object %s: %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"), *CollisionId);

    return true;
}

bool UAuracronWorldPartitionCollisionManager::IsCollisionObjectEnabled(const FString& CollisionId) const
{
    FScopeLock Lock(&CollisionLock);

    const FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (CollisionDesc)
    {
        return CollisionDesc->bIsEnabled;
    }

    return false;
}

bool UAuracronWorldPartitionCollisionManager::SetCollisionObjectVisibility(const FString& CollisionId, bool bVisible)
{
    FScopeLock Lock(&CollisionLock);

    FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (!CollisionDesc)
    {
        return false;
    }

    CollisionDesc->bIsVisible = bVisible;
    CollisionDesc->LastAccessTime = FDateTime::Now();

    AURACRON_WP_LOG_VERBOSE(TEXT("Collision object visibility set: %s -> %s"), *CollisionId, bVisible ? TEXT("visible") : TEXT("hidden"));

    return true;
}

bool UAuracronWorldPartitionCollisionManager::IsCollisionObjectVisible(const FString& CollisionId) const
{
    FScopeLock Lock(&CollisionLock);

    const FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (CollisionDesc)
    {
        return CollisionDesc->bIsVisible;
    }

    return false;
}

TArray<FString> UAuracronWorldPartitionCollisionManager::GetCollisionObjectsInCell(const FString& CellId) const
{
    FScopeLock Lock(&CollisionLock);

    const TSet<FString>* CellCollisions = CellToCollisionMap.Find(CellId);
    if (CellCollisions)
    {
        return CellCollisions->Array();
    }

    return TArray<FString>();
}

FString UAuracronWorldPartitionCollisionManager::GetCollisionObjectCell(const FString& CollisionId) const
{
    FScopeLock Lock(&CollisionLock);

    const FString* CellId = CollisionToCellMap.Find(CollisionId);
    if (CellId)
    {
        return *CellId;
    }

    return FString();
}

bool UAuracronWorldPartitionCollisionManager::MoveCollisionObjectToCell(const FString& CollisionId, const FString& CellId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (!CollisionDesc)
    {
        return false;
    }

    // Remove from old cell
    if (FString* OldCellId = CollisionToCellMap.Find(CollisionId))
    {
        if (TSet<FString>* OldCellCollisions = CellToCollisionMap.Find(*OldCellId))
        {
            OldCellCollisions->Remove(CollisionId);
        }
    }

    // Add to new cell
    CollisionToCellMap.Add(CollisionId, CellId);
    TSet<FString>& NewCellCollisions = CellToCollisionMap.FindOrAdd(CellId);
    NewCellCollisions.Add(CollisionId);

    CollisionDesc->CellId = CellId;
    CollisionDesc->LastAccessTime = FDateTime::Now();

    AURACRON_WP_LOG_VERBOSE(TEXT("Collision object moved to cell: %s -> %s"), *CollisionId, *CellId);

    return true;
}

void UAuracronWorldPartitionCollisionManager::SetConfiguration(const FAuracronCollisionConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Collision configuration updated"));
}

FAuracronCollisionConfiguration UAuracronWorldPartitionCollisionManager::GetConfiguration() const
{
    return Configuration;
}

FAuracronCollisionStatistics UAuracronWorldPartitionCollisionManager::GetCollisionStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronCollisionStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronWorldPartitionCollisionManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronCollisionStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Collision statistics reset"));
}

int32 UAuracronWorldPartitionCollisionManager::GetTotalCollisionObjectCount() const
{
    FScopeLock Lock(&CollisionLock);
    return CollisionDescriptors.Num();
}

int32 UAuracronWorldPartitionCollisionManager::GetLoadedCollisionObjectCount() const
{
    FScopeLock Lock(&CollisionLock);

    int32 LoadedCount = 0;
    for (const auto& CollisionPair : CollisionDescriptors)
    {
        if (CollisionPair.Value.StreamingState == EAuracronCollisionStreamingState::Loaded)
        {
            LoadedCount++;
        }
    }

    return LoadedCount;
}

float UAuracronWorldPartitionCollisionManager::GetTotalMemoryUsage() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.TotalMemoryUsageMB;
}

void UAuracronWorldPartitionCollisionManager::EnableCollisionDebug(bool bEnabled)
{
    Configuration.bEnableCollisionDebug = bEnabled;

    AURACRON_WP_LOG_INFO(TEXT("Collision debug %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionCollisionManager::IsCollisionDebugEnabled() const
{
    return Configuration.bEnableCollisionDebug;
}

void UAuracronWorldPartitionCollisionManager::LogCollisionState() const
{
    FScopeLock Lock(&CollisionLock);

    int32 TotalCount = CollisionDescriptors.Num();
    int32 LoadedCount = GetLoadedCollisionObjectCount();
    int32 StreamingCount = GetStreamingCollisionObjects().Num();

    AURACRON_WP_LOG_INFO(TEXT("Collision State: %d total, %d loaded, %d streaming"), TotalCount, LoadedCount, StreamingCount);

    FAuracronCollisionStatistics CurrentStats = GetCollisionStatistics();
    AURACRON_WP_LOG_INFO(TEXT("Statistics: %.1fMB memory, %.2f efficiency, %d queries"),
                         CurrentStats.TotalMemoryUsageMB, CurrentStats.CollisionEfficiency, CurrentStats.CollisionQueries);
}

void UAuracronWorldPartitionCollisionManager::DrawDebugCollisionInfo(UWorld* World) const
{
    if (!Configuration.bEnableCollisionDebug || !World)
    {
        return;
    }

    FScopeLock Lock(&CollisionLock);

    // Draw debug information for loaded collision objects
    for (const auto& CollisionPair : CollisionDescriptors)
    {
        const FAuracronCollisionDescriptor& CollisionDesc = CollisionPair.Value;

        if (CollisionDesc.StreamingState == EAuracronCollisionStreamingState::Loaded)
        {
            FColor DebugColor = FColor::Cyan;

            // Color based on collision type
            switch (CollisionDesc.CollisionType)
            {
                case EAuracronCollisionType::Static:
                    DebugColor = FColor::Green;
                    break;
                case EAuracronCollisionType::Dynamic:
                    DebugColor = FColor::Red;
                    break;
                case EAuracronCollisionType::Kinematic:
                    DebugColor = FColor::Yellow;
                    break;
                case EAuracronCollisionType::Trigger:
                    DebugColor = FColor::Magenta;
                    break;
                case EAuracronCollisionType::Query:
                    DebugColor = FColor::Cyan;
                    break;
            }

            // Draw collision bounds
            DrawDebugBox(World, CollisionDesc.Bounds.GetCenter(), CollisionDesc.Bounds.GetExtent(),
                        DebugColor, false, -1.0f, 0, 1.0f);

            // Draw collision info text
            FString DebugText = FString::Printf(TEXT("%s\nLOD%d %s"),
                                              *CollisionDesc.CollisionName,
                                              static_cast<int32>(CollisionDesc.CurrentLODLevel),
                                              *UEnum::GetValueAsString(CollisionDesc.CollisionType));

            DrawDebugString(World, CollisionDesc.Location + FVector(0, 0, 50), DebugText,
                           nullptr, DebugColor, -1.0f, true);
        }
    }
}

void UAuracronWorldPartitionCollisionManager::UpdateStatistics()
{
    FScopeLock Lock(&StatisticsLock);

    Statistics.TotalCollisionObjects = CollisionDescriptors.Num();
    Statistics.LoadedCollisionObjects = 0;
    Statistics.StreamingCollisionObjects = 0;
    Statistics.TotalMemoryUsageMB = 0.0f;

    for (const auto& CollisionPair : CollisionDescriptors)
    {
        const FAuracronCollisionDescriptor& CollisionDesc = CollisionPair.Value;

        switch (CollisionDesc.StreamingState)
        {
            case EAuracronCollisionStreamingState::Loaded:
                Statistics.LoadedCollisionObjects++;
                Statistics.TotalMemoryUsageMB += CollisionDesc.MemoryUsageMB;
                break;
            case EAuracronCollisionStreamingState::Loading:
            case EAuracronCollisionStreamingState::Unloading:
                Statistics.StreamingCollisionObjects++;
                break;
        }
    }

    Statistics.UpdateCalculatedFields();
}

FString UAuracronWorldPartitionCollisionManager::GenerateCollisionId(const FString& SourceActorId) const
{
    return FString::Printf(TEXT("Collision_%s_%lld"), *SourceActorId, FDateTime::Now().GetTicks());
}

bool UAuracronWorldPartitionCollisionManager::ValidateCollisionId(const FString& CollisionId) const
{
    return !CollisionId.IsEmpty() && CollisionId.StartsWith(TEXT("Collision_"));
}

void UAuracronWorldPartitionCollisionManager::OnCollisionLoadedInternal(const FString& CollisionId, bool bSuccess)
{
    OnCollisionLoaded.Broadcast(CollisionId, bSuccess);

    AURACRON_WP_LOG_VERBOSE(TEXT("Collision loaded event: %s (success: %s)"), *CollisionId, bSuccess ? TEXT("true") : TEXT("false"));
}

void UAuracronWorldPartitionCollisionManager::OnCollisionUnloadedInternal(const FString& CollisionId)
{
    OnCollisionUnloaded.Broadcast(CollisionId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Collision unloaded event: %s"), *CollisionId);
}

void UAuracronWorldPartitionCollisionManager::ValidateConfiguration()
{
    // Validate streaming distances
    Configuration.CollisionStreamingDistance = FMath::Max(0.0f, Configuration.CollisionStreamingDistance);
    Configuration.CollisionUnloadingDistance = FMath::Max(Configuration.CollisionStreamingDistance, Configuration.CollisionUnloadingDistance);

    // Validate LOD settings
    Configuration.BaseLODDistance = FMath::Max(0.0f, Configuration.BaseLODDistance);
    Configuration.LODDistanceMultiplier = FMath::Max(1.0f, Configuration.LODDistanceMultiplier);
    Configuration.MaxLODLevel = FMath::Clamp(Configuration.MaxLODLevel, 0, 4);

    // Validate performance settings
    Configuration.MaxConcurrentCollisionOperations = FMath::Max(1, Configuration.MaxConcurrentCollisionOperations);

    // Validate memory settings
    Configuration.MaxCollisionMemoryUsageMB = FMath::Max(100.0f, Configuration.MaxCollisionMemoryUsageMB);
}

void UAuracronWorldPartitionCollisionManager::UpdateCollisionCellMapping(const FString& CollisionId)
{
    const FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (!CollisionDesc)
    {
        return;
    }

    // Get grid manager to determine cell
    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager || !GridManager->IsInitialized())
    {
        return;
    }

    // Get cell at collision location
    FAuracronGridCell Cell = GridManager->GetCellAtLocation(CollisionDesc->Location);
    if (!Cell.CellId.IsEmpty())
    {
        MoveCollisionObjectToCell(CollisionId, Cell.CellId);
    }
}

bool UAuracronWorldPartitionCollisionManager::CreateCollisionMesh(const FString& CollisionId, const FString& SourceActorId)
{
    // In a real implementation, this would create actual collision mesh
    // For now, we'll just simulate the creation

    FScopeLock Lock(&CollisionLock);

    FAuracronCollisionDescriptor* CollisionDesc = CollisionDescriptors.Find(CollisionId);
    if (!CollisionDesc)
    {
        return false;
    }

    // Simulate collision mesh properties
    CollisionDesc->TriangleCount = FMath::RandRange(100, 5000);
    CollisionDesc->ConvexCount = FMath::RandRange(1, 10);

    // Calculate bounds based on source actor (simplified)
    FVector BoundsExtent = FVector(100.0f, 100.0f, 100.0f);
    CollisionDesc->Bounds = FBox(CollisionDesc->Location - BoundsExtent, CollisionDesc->Location + BoundsExtent);

    AURACRON_WP_LOG_VERBOSE(TEXT("Collision mesh created: %s (%d triangles, %d convex)"),
                           *CollisionId, CollisionDesc->TriangleCount, CollisionDesc->ConvexCount);

    return true;
}

bool UAuracronWorldPartitionCollisionManager::IsCollisionInQueryRange(const FAuracronCollisionDescriptor& Collision, const FAuracronCollisionQueryParameters& QueryParams) const
{
    // Check if collision bounds intersect with query bounds
    FBox QueryBox(QueryParams.QueryLocation - QueryParams.QueryExtent, QueryParams.QueryLocation + QueryParams.QueryExtent);

    return QueryBox.Intersect(Collision.Bounds);
}
