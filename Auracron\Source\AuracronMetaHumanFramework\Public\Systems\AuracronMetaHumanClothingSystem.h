// AURACRON MetaHuman Clothing System - Advanced Clothing Simulation for UE 5.6
// Handles MetaHuman clothing physics, materials, and real-time simulation
// Author: Augment Agent
// Date: 2025-08-05
// Version: 2.0.0

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/Engine.h"
#include "Components/SkeletalMeshComponent.h"
#include "ClothingSystemRuntimeInterface.h"
#include "ClothingSystemRuntimeCommon.h"
#include "PhysicsEngine/PhysicsAsset.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Engine/SkeletalMesh.h"
#include "Animation/AnimInstance.h"
#include "PhysicsCore.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Async/AsyncWork.h"

#include "AuracronMetaHumanClothingSystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMetaHumanClothing, Log, All);

// Forward declarations
class UAuracronMetaHumanFramework;
class UClothingAssetBase;
class UClothConfigBase;
class UClothSharedConfigBase;

/**
 * Clothing Types
 */
UENUM(BlueprintType)
enum class EAuracronClothingType : uint8
{
    Shirt               UMETA(DisplayName = "Shirt"),
    Pants               UMETA(DisplayName = "Pants"),
    Dress               UMETA(DisplayName = "Dress"),
    Jacket              UMETA(DisplayName = "Jacket"),
    Coat                UMETA(DisplayName = "Coat"),
    Skirt               UMETA(DisplayName = "Skirt"),
    Shoes               UMETA(DisplayName = "Shoes"),
    Hat                 UMETA(DisplayName = "Hat"),
    Gloves              UMETA(DisplayName = "Gloves"),
    Scarf               UMETA(DisplayName = "Scarf"),
    Cape                UMETA(DisplayName = "Cape"),
    Accessory           UMETA(DisplayName = "Accessory"),
    Custom              UMETA(DisplayName = "Custom")
};

/**
 * Clothing Material Types
 */
UENUM(BlueprintType)
enum class EAuracronClothingMaterial : uint8
{
    Cotton              UMETA(DisplayName = "Cotton"),
    Silk                UMETA(DisplayName = "Silk"),
    Wool                UMETA(DisplayName = "Wool"),
    Leather             UMETA(DisplayName = "Leather"),
    Denim               UMETA(DisplayName = "Denim"),
    Polyester           UMETA(DisplayName = "Polyester"),
    Linen               UMETA(DisplayName = "Linen"),
    Velvet              UMETA(DisplayName = "Velvet"),
    Satin               UMETA(DisplayName = "Satin"),
    Canvas              UMETA(DisplayName = "Canvas"),
    Rubber              UMETA(DisplayName = "Rubber"),
    Metal               UMETA(DisplayName = "Metal"),
    Custom              UMETA(DisplayName = "Custom")
};

/**
 * Clothing Physics Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronClothingPhysicsConfig
{
    GENERATED_BODY()

    /** Enable cloth simulation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bEnableSimulation = true;

    /** Cloth stiffness (0.0 = very soft, 1.0 = very stiff) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Stiffness = 0.5f;

    /** Cloth damping */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Damping = 0.1f;

    /** Wind responsiveness */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float WindResponsiveness = 1.0f;

    /** Gravity scale */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float GravityScale = 1.0f;

    /** Collision thickness */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float CollisionThickness = 1.0f;

    /** Self collision */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bEnableSelfCollision = false;

    /** Continuous collision detection */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bEnableCCD = true;

    FAuracronClothingPhysicsConfig()
    {
        // Default constructor
    }
};

/**
 * Clothing Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronClothingConfig
{
    GENERATED_BODY()

    /** Clothing type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clothing")
    EAuracronClothingType ClothingType = EAuracronClothingType::Shirt;

    /** Material type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clothing")
    EAuracronClothingMaterial MaterialType = EAuracronClothingMaterial::Cotton;

    /** Clothing name */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clothing")
    FString ClothingName;

    /** Physics configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clothing")
    FAuracronClothingPhysicsConfig PhysicsConfig;

    /** LOD distances */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clothing")
    TArray<float> LODDistances;

    /** Enable wind interaction */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clothing")
    bool bEnableWindInteraction = true;

    /** Enable character interaction */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clothing")
    bool bEnableCharacterInteraction = true;

    FAuracronClothingConfig()
    {
        // Default values
        LODDistances = {100.0f, 500.0f, 1000.0f};
    }
};

/**
 * Clothing Processing Result
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronClothingResult
{
    GENERATED_BODY()

    /** Whether the operation was successful */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    /** Result message */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString Message;

    /** Generated clothing asset */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TObjectPtr<UClothingAssetBase> GeneratedClothingAsset;

    /** Processing time in milliseconds */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ProcessingTimeMS = 0.0f;

    /** Output file path */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString OutputPath;

    FAuracronClothingResult()
    {
        // Default constructor
    }

    FAuracronClothingResult(bool bInSuccess, const FString& InMessage)
        : bSuccess(bInSuccess)
        , Message(InMessage)
    {
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronClothingComplete, const FAuracronClothingResult&, Result, const FString&, OperationID);

/**
 * AURACRON MetaHuman Clothing System
 * Advanced clothing simulation and management system for UE 5.6
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|MetaHuman|Clothing")
class AURACRONMETAHUMANFRAMEWORK_API UAuracronMetaHumanClothingSystem : public UObject
{
    GENERATED_BODY()

public:
    UAuracronMetaHumanClothingSystem();
    virtual ~UAuracronMetaHumanClothingSystem();

    // === Core Clothing Operations ===

    /**
     * Create clothing asset
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Core", CallInEditor = true)
    FAuracronClothingResult CreateClothingAsset(const FAuracronClothingConfig& Config, USkeletalMesh* TargetMesh);

    /**
     * Apply clothing to MetaHuman
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Core", CallInEditor = true)
    FAuracronClothingResult ApplyClothingToMetaHuman(UClothingAssetBase* ClothingAsset, USkeletalMeshComponent* MetaHumanComponent);

    /**
     * Remove clothing from MetaHuman
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Core", CallInEditor = true)
    bool RemoveClothingFromMetaHuman(USkeletalMeshComponent* MetaHumanComponent, const FString& ClothingName);

    /**
     * Update clothing physics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Core", CallInEditor = true)
    bool UpdateClothingPhysics(USkeletalMeshComponent* MeshComponent, const FString& ClothingName, const FAuracronClothingPhysicsConfig& PhysicsConfig);

    // === Async Clothing Operations ===

    /**
     * Create clothing asset asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Async", CallInEditor = true)
    FString CreateClothingAssetAsync(const FAuracronClothingConfig& Config, USkeletalMesh* TargetMesh);

    /**
     * Check if async operation is complete
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Async", CallInEditor = true)
    bool IsAsyncOperationComplete(const FString& OperationID) const;

    /**
     * Get async operation result
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Async", CallInEditor = true)
    FAuracronClothingResult GetAsyncOperationResult(const FString& OperationID);

    // === Clothing Simulation ===

    /**
     * Start clothing simulation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Simulation", CallInEditor = true)
    bool StartClothingSimulation(USkeletalMeshComponent* MeshComponent);

    /**
     * Stop clothing simulation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Simulation", CallInEditor = true)
    bool StopClothingSimulation(USkeletalMeshComponent* MeshComponent);

    /**
     * Reset clothing simulation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Simulation", CallInEditor = true)
    bool ResetClothingSimulation(USkeletalMeshComponent* MeshComponent);

    /**
     * Set wind parameters
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Simulation", CallInEditor = true)
    bool SetWindParameters(const FVector& WindDirection, float WindStrength);

    // === Clothing Materials ===

    /**
     * Create clothing material
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Materials", CallInEditor = true)
    UMaterialInstance* CreateClothingMaterial(EAuracronClothingMaterial MaterialType, const TMap<FString, float>& Parameters);

    /**
     * Apply material to clothing
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Materials", CallInEditor = true)
    bool ApplyMaterialToClothing(USkeletalMeshComponent* MeshComponent, const FString& ClothingName, UMaterialInterface* Material);

    /**
     * Update material parameters
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Materials", CallInEditor = true)
    bool UpdateMaterialParameters(UMaterialInstanceDynamic* MaterialInstance, const TMap<FString, float>& Parameters);

    // === Clothing Analysis ===

    /**
     * Analyze clothing performance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Analysis", CallInEditor = true)
    FAuracronClothingResult AnalyzeClothingPerformance(USkeletalMeshComponent* MeshComponent, const FString& ClothingName);

    /**
     * Get clothing statistics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Analysis", CallInEditor = true)
    FString GetClothingStatistics(UClothingAssetBase* ClothingAsset);

    /**
     * Validate clothing configuration
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Analysis", CallInEditor = true)
    FAuracronClothingResult ValidateClothingConfiguration(const FAuracronClothingConfig& Config);

    // === Clothing Optimization ===

    /**
     * Optimize clothing performance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Optimization", CallInEditor = true)
    FAuracronClothingResult OptimizeClothingPerformance(UClothingAssetBase* ClothingAsset);

    /**
     * Generate clothing LODs
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Optimization", CallInEditor = true)
    FAuracronClothingResult GenerateClothingLODs(UClothingAssetBase* ClothingAsset, const TArray<float>& LODDistances);

    /**
     * Simplify clothing mesh
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Optimization", CallInEditor = true)
    FAuracronClothingResult SimplifyClothingMesh(UClothingAssetBase* ClothingAsset, float ReductionPercentage);

    // === Utility Functions ===

    /**
     * Initialize clothing system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Utility", CallInEditor = true)
    bool Initialize(UAuracronMetaHumanFramework* InFramework);

    /**
     * Shutdown clothing system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Utility", CallInEditor = true)
    void Shutdown();

    /**
     * Check if clothing system is initialized
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Utility", CallInEditor = true)
    bool IsInitialized() const;

    /**
     * Get supported clothing types
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Utility", CallInEditor = true)
    TArray<FString> GetSupportedClothingTypes() const;

    /**
     * Get supported material types
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Clothing|Utility", CallInEditor = true)
    TArray<FString> GetSupportedMaterialTypes() const;

public:
    // === Events ===

    /** Called when an async clothing operation completes */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON MetaHuman|Clothing|Events")
    FAuracronClothingComplete OnClothingComplete;

protected:
    // === Internal State ===

    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanFramework> OwnerFramework;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FString LastErrorMessage;

    // Async operation tracking
    TMap<FString, TSharedPtr<class FAuracronClothingAsyncTask>> ActiveAsyncOperations;
    FCriticalSection AsyncOperationsMutex;

    // Clothing cache
    TMap<FString, TWeakObjectPtr<UClothingAssetBase>> ClothingCache;
    FCriticalSection ClothingCacheMutex;

    // Wind parameters
    FVector CurrentWindDirection = FVector::ZeroVector;
    float CurrentWindStrength = 0.0f;

private:
    // === Internal Methods ===

    FString GenerateOperationID() const;
    void CleanupCompletedAsyncOperations();
    bool ValidateClothingConfig(const FAuracronClothingConfig& Config, FString& OutError) const;
    UClothingAssetBase* CreateClothingAssetInternal(const FAuracronClothingConfig& Config, USkeletalMesh* TargetMesh);
    bool SetupClothingPhysics(UClothingAssetBase* ClothingAsset, const FAuracronClothingPhysicsConfig& PhysicsConfig);
    void CacheClothingAsset(const FString& Key, UClothingAssetBase* ClothingAsset);
    UClothingAssetBase* GetCachedClothingAsset(const FString& Key) const;
    bool InitializeClothingSystem();
    void ShutdownClothingSystem();
};
